.template-index .grid--uniform .medium-up--one-half:nth-of-type(odd),
.template-index.grid--uniform .medium-up--one-third:nth-of-type(3n + 1) {
  clear: unset !important;
}
.custom-section_ontdek_meer .grid--flush-bottom {
  overflow: unset;
}
.custom-section_ontdek_meer .slick-prev:before,
.custom-section_ontdek_meer .slick-next:before {
  color: #fff;
  font-size: 35px;
  opacity: 1;
}
.custom-section_ontdek_meer .slick-arrow {
  height: 35px;
  width: 35px;
  z-index: 9;
}
.custom-section_ontdek_meer .slick-arrow.slick-next {
  right: 0.2%;
}
.custom-section_ontdek_meer .slick-arrow.slick-prev {
  left: 1.3%;
}
.template-index .text_column_content_outer {
  position: relative;
}
.template-index .text_column_content h3 {
  color: #fff;
  font-weight: bold;
  font-size: 40px;
}

.template-index .text_column_content {
  position: absolute;
  top: 40%;
  width: 90%;
  margin: 0 auto;
  margin-left: 20px;
}

.template-index .text_column_content .rte-setting.text-spacing {
  color: #fff;
  font-weight: bold;
}
.text_column_btn {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  width: 100%;
}
.template-index .image-wrap.text-spacing {
  border-radius: 20px;
}
.template-index .text_column_btn a.btn.btn--secondary.btn--small {
  color: #000;
  background-color: #fff;
  border-radius: 25px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
  height: 45px;
  font-size: 14px;
}

.template-index .text_column_content_outer:nth-child(2) .text_column_content h3,
.text_column_content_outer:nth-child(2) .text_column_content p {
  text-shadow: 0px 0px 4px #000;
}
.template-product .text_column_content_outer:nth-child(2) .text_column_content p {
  text-shadow: 0px 0px 0px #000;
}
.template-index .footer__item-padding .footer__title {
  font-weight: bold;
}
.grid__item .footer__title {
  font-weight: bold;
}
strong {
  font-weight: bold;
}
.animation-cropper .animation-contents .btn {
  color: #fff !important;
}
.slideshow .hero__link .btn {
  color: #fff;
}

@meida only screen and (min-widht:768px) {
  .template-index .footer__item-padding .footer__title,
  .grid__item .footer__title,
  strong {
    font-weight: 700;
  }
}
@media only screen and (max-width: 425px) {
  .footer__item-padding .footer__title {
    text-align: center;
    font-weight: 100 !important;
  }
  .grid__item .footer__title {
    text-align: center;
    font-weight: 100 !important;
  }
  .custom-section_ontdek_meer .slick-arrow.slick-prev {
    left: 3.3%;
  }
}

.template-index .text_column_btn a.btn.btn--secondary.btn--small {
  width: 60%;
}
.template-index .text_column_btn a.btn.btn--secondary.btn--small {
  background: #0071e4 !important;
  color: #fff;
  border: none;
}
.black_friday_label {
  left: 0;
  right: auto;
  background-color: #b81724 !important;
  text-transform: uppercase;
}
body.template-oddit .desktop-images-list .grid-product__tag{ background-color: #EF4444 !important;}
body.template-oddit.template-product .oddit-pdp {
  display: flex !important;
}
body.template-oddit.template-product .oddit-pdp-hide {
  display: none !important;
}
body.template-oddit .specifiction-table {
  background: transparent;
}
body.template-oddit .specifiction-table td {
  width: 50%;
  float: right;
  border-bottom: 1px solid #E6E3D9;
  padding: 10px !important;
}
body.template-oddit .specifiction-table td:nth-child(odd) { 
  border-right: 1px solid #E6E3D9;
  float: left;
  clear: left;
}
body.template-oddit .specifiction-table td:last-child,
body.template-oddit .specifiction-table td:nth-last-child(2) {
  border-bottom: 0;
}
body.template-oddit .specifiction-table td .title {
  font-family: DM Sans,sans-serif;
  font-weight: 700;
  font-size: 14px;
  line-height: normal;
  margin: 0;
}
body.template-oddit .specifiction-table td .content {
  margin-top: 20px;
}
body.template-oddit .specifiction-table td .content p {
  margin: 0;
  font-family: DM Sans,sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: normal;
}
body.template-oddit .specifiction-table td .content p b {
  font-weight: 700;
}
body.template-oddit #bundle-sticky_add_to_cart{ display: none !important;}
body.template-oddit .grid-product__meta .loox-rating .loox-rating-content .loox-rating-label{ text-decoration: none; font-size: 15px; margin-left: 0;}
body.template-oddit .grid-product__meta .loox-rating .loox-rating-content .loox-icon{ color: #FFCA40; width: 14px; height: 14px;}
body.template-oddit .grid-product__meta .loox-rating .loox-rating-content{ gap: 2px;}
body.template-oddit #bundle-sticky_submit{ font-size: 10px !important;}
@media only screen and (max-width: 991px) {
  body.template-oddit .grid-overflow-wrapper{ overflow-x: auto;}
  body.template-oddit .grid-overflow-wrapper::-webkit-scrollbar {display: none;}
  body.template-oddit .grid-overflow-wrapper .product-recommendations .grid{ white-space: nowrap;display: flex;}
  body.template-oddit .grid-overflow-wrapper .product-recommendations .grid .grid__item{ width: 70%; flex: none;}
}
@media only screen and (max-width: 767px) {
  body.template-oddit .grid-product__meta .loox-rating .loox-rating-content .loox-rating-label{ font-size: 13px;}  
  body.template-oddit .product-single__meta .product__price.on-sale{ font-size: 27px !important;}
  body.template-oddit .product__photos .product__main-photos [data-product-photos] img { padding: 20px !important;}
  body.template-oddit .announcement-link { display: flex; justify-content: center; gap: 5px; }
}
