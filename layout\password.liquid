<!doctype html>
<html class="no-js" lang="{{ request.locale.iso_code }}" dir="{{ settings.text_direction }}">
<head>
<link rel='preconnect dns-prefetch' href='https://api.config-security.com/' crossorigin />
<link rel='preconnect dns-prefetch' href='https://conf.config-security.com/' crossorigin />
<link rel='preconnect dns-prefetch' href='https://whale.camera/' crossorigin />
<script>
/* >> TriplePixel :: start*/
window.TriplePixelData={TripleName:"gardpro-us.myshopify.com",ver:"2.11",plat:"SHOPIFY",isHeadless:false},function(W,H,A,L,E,_,B,N){function O(U,T,P,H,R){void 0===R&&(R=!1),H=new XMLHttpRequest,P?(H.open("POST",U,!0),H.setRequestHeader("Content-Type","application/json")):H.open("GET",U,!0),H.send(JSON.stringify(P||{})),H.onreadystatechange=function(){4===H.readyState&&200===H.status?(R=H.responseText,U.includes(".txt")?eval(R):P||(N[B]=R)):(299<H.status||H.status<200)&&T&&!R&&(R=!0,O(U,T-1,P))}}if(N=window,!N[H+"sn"]){N[H+"sn"]=1,L=function(){return Date.now().toString(36)+"_"+Math.random().toString(36)};try{A.setItem(H,1+(0|A.getItem(H)||0)),(E=JSON.parse(A.getItem(H+"U")||"[]")).push({u:location.href,r:document.referrer,t:Date.now(),id:L()}),A.setItem(H+"U",JSON.stringify(E))}catch(e){}var i,m,p;A.getItem('"!nC`')||(_=A,A=N,A[H]||(E=A[H]=function(t,e,a){return void 0===a&&(a=[]),"State"==t?E.s:(W=L(),(E._q=E._q||[]).push([W,t,e].concat(a)),W)},E.s="Installed",E._q=[],E.ch=W,B="configSecurityConfModel",N[B]=1,O("https://conf.config-security.com/model",5),i=L(),m=A[atob("c2NyZWVu")],_.setItem("di_pmt_wt",i),p={id:i,action:"profile",avatar:_.getItem("auth-security_rand_salt_"),time:m[atob("d2lkdGg=")]+":"+m[atob("aGVpZ2h0")],host:A.TriplePixelData.TripleName,plat:A.TriplePixelData.plat,url:window.location.href,ref:document.referrer,ver:A.TriplePixelData.ver},O("https://api.config-security.com/event",5,p),O("https://whale.camera/live/dot.txt",5)))}}("","TriplePixel",localStorage);
/* << TriplePixel :: end*/
</script>

  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
  <meta name="viewport" content="width=device-width,initial-scale=1">
  <meta name="theme-color" content="{{ settings.color_button }}">
  <link rel="canonical" href="{{ canonical_url }}">
  <link rel="preconnect" href="https://cdn.shopify.com" crossorigin>
  <link rel="preconnect" href="https://fonts.shopifycdn.com" crossorigin>

  {%- if settings.favicon != blank -%}
    <link rel="shortcut icon" href="{{ settings.favicon | img_url: '32x32' }}" type="image/png" />
  {%- endif -%}

  {%- render 'seo-title' -%}

  {%- if page_description -%}
  <meta name="description" content="{{ page_description | escape }}">
  {%- endif -%}

  {%- render 'social-meta-tags' -%}

  {%- render 'font-face' -%}
  {{ 'theme.css' | asset_url | stylesheet_tag: preload: true }}
  {%- render 'css-variables' -%}

  <script>
    document.documentElement.className = document.documentElement.className.replace('no-js', 'js');

    window.theme = window.theme || {};
    theme.settings = {
      moneyFormat: {{ shop.money_format | json }},
      saveType: {{ settings.product_save_type | json }},
      productImageSize: {{ settings.product_grid_image_size | json }},
      productImageCover: {{ settings.product_grid_image_fill }},
      themeName: 'Impulse',
      themeVersion: "7.1.0"
    };
  </script>

  {{ content_for_header }}

  <script src="{{ 'vendor-scripts-v11.js' | asset_url | split: '?' | first }}" defer="defer"></script>
  <script src="{{ 'theme.js' | asset_url }}" defer="defer"></script>
</head>

<body data-center-text="{{ settings.type_body_align_text }}" data-button_style="{{ settings.button_style }}" data-type_header_capitalize="{{ settings.type_header_capitalize }}" data-type_headers_align_text="{{ settings.type_headers_align_text }}" data-type_product_capitalize="{{ settings.type_product_capitalize }}" data-swatch_style="{{ settings.swatch_style }}">
  <a class="in-page-link visually-hidden skip-link" href="#MainContent">{{ 'general.accessibility.skip_to_content' | t }}</a>

  <div id="PageContainer" class="page-container">
    <div class="transition-body">
      <main id="MainContent">
        {{ content_for_layout }}
      </main>

      <footer class="site-footer text-center">
        <div class="footer__section">
          <div class="page-width">
            {%- capture shopify -%}
              <a href="//www.shopify.com" rel="nofollow" target="_blank" title="Create your own online store with Shopify">
                <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon--wide icon-shopify-logo" viewBox="0 0 150 43"><path fill="#999" d="M33.3 8.9s0-.2-.1-.3c-.1-.1-.2-.1-.2-.1l-3.4-.2-2.1-2.1c-.1-.1-.2-.1-.3-.1l-1.8 36.1L38 39.5 33.3 8.9zm-7.5-3l-.9.3c-.6-1.6-1.3-2.8-2.3-3.5-.7-.5-1.5-.7-2.3-.6l-.6-.6c-.9-.7-2.1-.9-3.6-.3C11.8 2.7 10 8.3 9.3 11l-3.8 1.1s-.9.2-1.1.5c-.2.3-.3 1-.3 1L.9 37.9l23.6 4.4L26.3 6c-.2-.2-.4-.1-.5-.1zm-5.7 1.7L16 8.9c.5-2.1 1.6-4.3 3.6-5.1.4 1 .5 2.5.5 3.8zm-3.5-5.2c.9-.3 1.6-.3 2.1 0-2.7 1.2-3.9 4.3-4.4 6.9l-3.3 1c.7-2.5 2.3-6.7 5.6-7.9zm2.3 17.9c-.2-.1-.4-.2-.7-.3-.3-.1-.5-.2-.8-.3-.3-.1-.6-.1-1-.2h-1.1c-.3 0-.6.1-.9.2-.3.1-.5.2-.7.4-.2.2-.3.4-.4.6-.1.2-.2.5-.2.7 0 .2 0 .4.1.6l.3.6.6.6c.2.2.5.4.8.6.5.3.9.6 1.4 1 .5.4.9.8 1.2 1.3.4.5.7 1 .9 1.7.2.6.3 1.3.3 2.1-.1 1.2-.3 2.3-.8 3.2-.4.9-1.1 1.6-1.8 2.1s-1.6.8-2.5.9c-.9.1-1.9.1-2.8-.2-.5-.1-.9-.3-1.3-.4l-1.2-.6c-.3-.2-.7-.4-.9-.6-.3-.2-.5-.4-.7-.7L7.8 30c.2.2.4.3.7.5.3.2.6.4.9.5.3.2.7.3 1 .5.4.1.7.2 1.1.3h.8c.2-.1.5-.2.6-.3.2-.1.3-.3.4-.5.1-.2.1-.4.2-.7 0-.2 0-.5-.1-.7-.1-.2-.2-.4-.3-.7-.1-.2-.3-.4-.6-.7-.2-.2-.5-.5-.9-.7-.4-.3-.8-.6-1.2-1-.3-.4-.7-.7-.9-1.2-.2-.4-.4-.9-.6-1.4-.1-.5-.2-1-.2-1.6 0-1 .2-1.8.6-2.6.3-.8.8-1.5 1.4-2.2.6-.6 1.3-1.2 2.2-1.6.9-.4 1.8-.7 2.9-.9.5-.1 1-.1 1.4-.1.5 0 .9 0 1.3.1s.8.1 1.1.2l.9.3-1.6 4.8zm2.6-13.1v-.5c0-1.3-.2-2.4-.5-3.2.3 0 .6.1.9.3.8.5 1.3 1.6 1.7 2.8l-2.1.6zM45.3 29.6c.9.5 2.5 1.1 4.1 1.1 1.4 0 2.2-.8 2.2-1.7 0-.9-.5-1.5-2.1-2.4-1.9-1.1-3.3-2.6-3.3-4.6 0-3.5 3-6 7.4-6 1.9 0 3.4.4 4.2.8l-1.2 3.5c-.7-.3-1.8-.7-3.1-.7-1.4 0-2.3.6-2.3 1.7 0 .8.7 1.4 1.9 2 2 1.1 3.6 2.6 3.6 4.7 0 4-3.2 6.2-7.7 6.1-2.1 0-4-.6-4.9-1.2l1.2-3.3zm12.4 4.5l4.9-25.2h5l-1.9 9.8h.1c1.3-1.6 3.1-2.7 5.3-2.7 2.6 0 4.1 1.7 4.1 4.5 0 .9-.1 2.2-.4 3.3l-2 10.3h-5l1.9-9.9c.1-.7.2-1.5.2-2.2 0-1.1-.4-1.8-1.6-1.8-1.6 0-3.3 2-4 5.3l-1.7 8.7h-4.9v-.1zM93.3 23c0 6.1-4 11.4-9.9 11.4-4.5 0-6.9-3.1-6.9-6.9 0-6 4-11.4 10-11.4 4.7 0 6.8 3.3 6.8 6.9zm-11.7 4.3c0 1.8.7 3.2 2.4 3.2 2.7 0 4.1-4.7 4.1-7.7 0-1.5-.6-3-2.4-3-2.6.1-4.1 4.7-4.1 7.5zm10.5 13.8L95.6 23c.4-2 .8-4.7 1-6.6h4.4l-.3 2.8h.1c1.3-1.9 3.3-3 5.3-3 3.7 0 5.2 2.9 5.2 6.3 0 6-3.9 12.1-9.7 12.1-1.2 0-2.4-.5-2.9-.5h-.1l-1.4 7h-5.1zm7.2-11.2c.5.4 1.2.7 2.1.7 2.8 0 4.7-4.6 4.7-7.8 0-1.3-.5-2.7-2-2.7-1.7 0-3.4 2-4 5.1l-.8 4.7zm12.2 4.2l3.4-17.7h5.1l-3.4 17.7h-5.1zm6.5-19.6c-1.4 0-2.4-1.1-2.4-2.6 0-1.6 1.3-2.9 2.9-2.9 1.5 0 2.5 1.1 2.5 2.6 0 1.8-1.4 2.9-3 2.9zm2.9 19.6l2.7-14h-2.3l.7-3.7h2.3l.1-.8c.4-2.1 1.2-4.2 2.9-5.6 1.3-1.1 3.1-1.6 4.9-1.6 1.2 0 2.1.2 2.7.4l-1 3.9c-.4-.1-.9-.3-1.6-.3-1.7 0-2.7 1.5-3 3.2l-.2.8h3.5l-.7 3.7h-3.5l-2.7 14h-4.8zm18-17.7l.8 7.9c.2 1.8.4 3.3.4 4.2h.1c.4-.9.8-2.3 1.5-4.2l3.1-7.9h5.2l-6.1 13.1c-2.2 4.5-4.3 7.7-6.6 9.9-1.8 1.7-3.9 2.5-4.9 2.7l-1.4-4.2c.8-.3 1.9-.7 2.8-1.4 1.2-.8 2.1-1.9 2.7-3 .1-.3.2-.5.1-1.9l-3-15.2h5.3z"/></svg>
                <span class="icon__fallback-text">Shopify</span>
              </a>
            {%- endcapture -%}
            {{ 'general.password_page.powered_by_shopify_html' | t: shopify: shopify }}
          </div>
        </div>
      </footer>
    </div>
  </div>

  <div id="LoginModal" class="modal modal--solid">
    <div class="modal__inner">
      <div class="modal__centered text-center">
        <div class="password-form">
          <p class="h4">{{ 'general.password_page.login_form_heading' | t }}</p>
          {%- form 'storefront_password' -%}
            {{ form.errors | default_errors }}
            <label for="password" class="hidden-label">{{ 'general.password_page.login_form_password_label' | t }}</label>
            <div class="input-group">
              <input type="password" name="password" id="password" class="input-group-field" placeholder="{{ 'general.password_page.login_form_password_placeholder' | t }}" autofocus>
              <div class="input-group-btn">
                <button type="submit" class="btn">
                  {{ 'general.password_page.login_form_submit' | t }}
                </button>
              </div>
            </div>
          {%- endform -%}
        </div>
        <p class="password-admin-link">
          <small>
            {{ 'general.password_page.admin_link_html' | t }}
          </small>
        </p>
      </div>
    </div>

    <button type="button" class="modal__close js-modal-close text-link">
      <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-close" viewBox="0 0 64 64"><path d="M19 17.61l27.12 27.13m0-27.12L19 44.74"/></svg>
      <span class="icon__fallback-text">{{ 'general.accessibility.close_modal' | t | json }}</span>
    </button>
  </div>

  {%- liquid
    section 'newsletter-popup'
    render 'video-modal'
    render 'photoswipe-template'
  -%}
</body>
</html>
