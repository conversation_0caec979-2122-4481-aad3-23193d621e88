/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin language editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "general": {
    "404": {
      "title": "404 Seite nicht gefunden",
      "subtext_html": "Die von Ihnen angeforderte Seite existiert nicht. Klicken Sie <a href='{{ url }}'>hier</a>, um den Einkauf fortzusetzen."
    },
    "accessibility": {
      "skip_to_content": "Direkt zum Inhalt",
      "close_modal": "Schließen (Esc)",
      "close": "<PERSON><PERSON>ießen",
      "learn_more": "<PERSON>rf<PERSON><PERSON> Si<PERSON> mehr"
    },
    "meta": {
      "tags": "Getaggt \"{{ tags }}\"",
      "page": "Seite {{ page }}"
    },
    "pagination": {
      "previous": "Zurück",
      "next": "Vorwärts"
    },
    "password_page": {
      "login_form_heading": "Shop mit Passwort betreten:",
      "login_form_password_label": "Passwort",
      "login_form_password_placeholder": "Ihr Passwort",
      "login_form_submit": "Betreten",
      "signup_form_email_label": "E-Mail",
      "signup_form_success": "Wir senden Ihnen eine E-Mail, kurz bevor wir eröffnen!",
      "admin_link_html": "Sind Sie der Geschäftsinhaber? <a href=\"/admin\" class=\"text-link\">Melden Sie sich hier an</a>",
      "password_link": "Passwort",
      "powered_by_shopify_html": "Dieser Shop wird mit Hilfe von {{ shopify }} betrieben werden"
    },
    "breadcrumbs": {
      "home": "Startseite",
      "home_link_title": "Zurück zur Startseite"
    },
    "social": {
      "share_on_facebook": "Teilen",
      "share_on_twitter": "Twittern",
      "share_on_pinterest": "Pinnen",
      "alt_text": {
        "share_on_facebook": "Auf Facebook teilen",
        "share_on_twitter": "Auf Twitter twittern",
        "share_on_pinterest": "Auf Pinterest pinnen"
      }
    },
    "newsletter_form": {
      "newsletter_email": "Melden Sie sich für unsere Mailingliste an",
      "newsletter_confirmation": "Danke fürs Anmelden",
      "submit": "Abonnieren"
    },
    "search": {
      "view_more": "Mehr sehen",
      "collections": "Kollektionen:",
      "pages": "Seiten:",
      "articles": "Artikel:",
      "no_results_html": "Ihre Suche nach \"{{ terms }}\" hat keine Ergebnisse hervorgebracht.",
      "results_for_html": "Ihre Suche nach \"{{ terms }}\" hat folgende Ergebnisse hervorgebracht:",
      "title": "Suche",
      "placeholder": "Durchsuchen Sie unseren Shop",
      "submit": "Suchen",
      "result_count": {
        "one": "{{ count }} Ergebnis",
        "other": "{{ count }} Ergebnisse"
      }
    },
    "drawers": {
      "navigation": "Seitennavigation",
      "close_menu": "Menü schließen",
      "expand_submenu": "Menü maximieren",
      "collapse_submenu": "Menü minimieren"
    },
    "currency": {
      "dropdown_label": "Währung"
    },
    "language": {
      "dropdown_label": "Sprache"
    }
  },
  "sections": {
    "map": {
      "get_directions": "Richtungen",
      "address_error": "Kann die Adresse nicht finden",
      "address_no_results": "Keine Ergebnisse für diese Adresse",
      "address_query_limit_html": "Sie haben die Google- API-Nutzungsgrenze überschritten. Betrachten wir zu einem <a href=\"https://developers.google.com/maps/premium/usage-limits\">Premium-Paket</a> zu aktualisieren.",
      "auth_error_html": "Es gab ein Problem bei Google Maps Konto zu authentifizieren. Erstellen und die <a href=\"https://developers.google.com/maps/documentation/javascript/get-api-key\">JavaScript- API</a> und <a href=\"https://developers.google.com/maps/documentation/geocoding/get-api-key\">Geocoding API</a> -Berechtigungen der App aktivieren."
    },
    "slideshow": {
      "play_slideshow": "Diashow abspielen",
      "pause_slideshow": "Pause Diashow"
    }
  },
  "blogs": {
    "article": {
      "view_all": "Alle anzeigen",
      "tags": "Tags",
      "read_more": "Weiterlesen",
      "back_to_blog": "Zurück zu {{ title }}"
    },
    "comments": {
      "title": "Hinterlassen Sie einen Kommentar",
      "name": "Name",
      "email": "E-Mail",
      "message": "Nachricht",
      "post": "Kommentar posten",
      "moderated": "Bitte beachten Sie, dass Kommentare vor der Veröffentlichung freigegeben werden müssen",
      "success_moderated": "Ihr Kommentar wurde erfolgreich gepostet. Da unser Blog moderiert wird, werden wir ihn erst kurze Zeit später veröffentlichen.",
      "success": "Ihr Kommentar wurde erfolgreich gepostet. Danke!",
      "with_count": {
        "one": "{{ count }} Kommentar",
        "other": "{{ count }} Kommentare"
      }
    }
  },
  "cart": {
    "general": {
      "title": "Einkaufswagen",
      "remove": "Entfernen",
      "note": "Besondere Hinweise an den Verkäufer",
      "subtotal": "Zwischensumme",
      "discounts": "Rabatte",
      "shipping_at_checkout": "Rabattcodes, Versandkosten und Steuern werden bei der Bezahlung berechnet.",
      "update": "Einkaufswagen aktualisieren",
      "checkout": "Zur Kasse",
      "empty": "Ihr Einkaufswagen ist im Moment leer.",
      "continue_browsing_html": "Mit der Suche <a href='{{ url }}'>hier</a> fortfahren.",
      "close_cart": "Einkaufswagen schließen",
      "reduce_quantity": "Artikelmenge um eins reduzieren",
      "increase_quantity": "Artikelmenge um eins erhöhen",
      "terms": "Ich stimme den Nutzungsbedingungen zu",
      "terms_html": "Ich stimme den <a href='{{ url }}' target='_blank'>Nutzungsbedingungen</a> zu",
      "terms_confirm": "Sie müssen den Verkaufsbedingungen zustimmen, um auszuchecken"
    },
    "label": {
      "price": "Preis",
      "quantity": "Menge",
      "total": "Gesamt"
    }
  },
  "collections": {
    "general": {
      "catalog_title": "Katalog",
      "all_of_collection": "Alle",
      "view_all_products_html": "zeige alle<br>{{ count }} Produkte an",
      "see_more": "Zeig mehr",
      "see_less": "Zeige weniger",
      "no_matches": "Es tut uns leid, aber Ihre Suche nach Produkten hat keine Treffer ergeben.",
      "items_with_count": {
        "one": "{{ count }} Produkt",
        "other": "{{ count }} Produkte"
      }
    },
    "sorting": {
      "title": "Sortieren"
    },
    "filters": {
      "title_tags": "Filtern",
      "all_tags": "Alle Produkte",
      "categories_title": "Kategorien"
    }
  },
  "contact": {
    "form": {
      "name": "Name",
      "email": "E-Mail",
      "phone": "Telefonnummer",
      "message": "Nachricht",
      "send": "Absenden",
      "post_success": "Danke, dass Sie uns kontaktiert haben. Wir werden uns so schnell wie möglich bei Ihnen melden."
    }
  },
  "customer": {
    "account": {
      "title": "Mein Account",
      "details": "Account-Details",
      "view_addresses": "Adressen ansehen",
      "return": "Zurück zu Account-Details"
    },
    "activate_account": {
      "title": "Account aktivieren",
      "subtext": "Erstellen Sie ein Passwort, um Ihren Account zu aktiveren.",
      "password": "Passwort",
      "password_confirm": "Passwort bestätigen",
      "submit": "Account aktivieren",
      "cancel": "Einladung ablehnen"
    },
    "addresses": {
      "title": "Ihre Adressen",
      "default": "Standard",
      "add_new": "Neue Adresse hinzufügen",
      "edit_address": "Adresse bearbeiten",
      "first_name": "Vorname",
      "last_name": "Nachname",
      "company": "Firma",
      "address1": "Adresse1",
      "address2": "Adresse2",
      "city": "Stadt",
      "country": "Land",
      "province": "Bundesland",
      "zip": "PLZ",
      "phone": "Telefon",
      "set_default": "Als Standard-Adresse festlegen",
      "add": "Adresse hinzufügen",
      "update": "Adresse aktualisieren",
      "cancel": "Abbrechen",
      "edit": "Bearbeiten",
      "delete": "Löschen",
      "delete_confirm": "Sind Sie sicher, dass Sie diese Adresse löschen möchten?"
    },
    "login": {
      "title": "Login",
      "email": "E-Mail",
      "password": "Passwort",
      "forgot_password": "Haben Sie Ihr Passwort vergessen?",
      "sign_in": "Anmelden",
      "cancel": "Zurück zum Shop",
      "guest_title": "Als Gast fortsetzen",
      "guest_continue": "Fortsetzen"
    },
    "orders": {
      "title": "Bestellungen",
      "order_number": "Bestellung",
      "date": "Datum",
      "payment_status": "Zahlungsstatus",
      "fulfillment_status": "Lieferstatus",
      "total": "Gesamt",
      "none": "Sie haben noch keine Bestellungen aufgegeben."
    },
    "order": {
      "title": "Bestellung {{ name }}",
      "date_html": "Aufgegeben am {{ date }}",
      "cancelled_html": "Bestellung storniert am {{ date }}",
      "cancelled_reason": "Grund: {{ reason }}",
      "billing_address": "Rechnungsadresse",
      "payment_status": "Zahlungsstatus",
      "shipping_address": "Lieferadresse",
      "fulfillment_status": "Lieferstatus",
      "discount": "Rabatt",
      "shipping": "Versand",
      "tax": "Steuern",
      "product": "Artikel",
      "sku": "SKU",
      "price": "Preis",
      "quantity": "Menge",
      "total": "Gesamt",
      "fulfilled_at_html": "Geliefert am {{ date }}",
      "subtotal": "Zwischensumme"
    },
    "recover_password": {
      "title": "Passwort zurücksetzen",
      "email": "E-Mail",
      "submit": "Absenden",
      "cancel": "Abbrechen",
      "subtext": "Wir werden Ihnen eine E-Mail zum Zurücksetzen des Passworts schicken.",
      "success": "Wir haben Ihnen eine E-Mail mit einem Link zum Aktualisieren des Passworts geschickt."
    },
    "reset_password": {
      "title": "Account-Passwort zurücksetzen",
      "subtext": "Geben Sie ein neues Passwort für {{ email }} ein",
      "password": "Passwort",
      "password_confirm": "Passwort bestätigen",
      "submit": "Passwort zurücksetzen"
    },
    "register": {
      "title": "Account erstellen",
      "first_name": "Vorname",
      "last_name": "Nachname",
      "email": "E-Mail",
      "password": "Passwort",
      "submit": "Erstellen",
      "cancel": "Zurück zum Shop"
    }
  },
  "home_page": {
    "onboarding": {
      "product_title": "Beispielhafter Produkttitel",
      "product_description": "Diesen Abschnitt können Sie nutzen, um die Details Ihres Produkts zu beschreiben. Erzählen Sie den Kunden, wie Ihr Produkt aussieht, sich anfühlt und designt ist. Fügen Sie Einzelheiten zu Farbe, verwendeten Materialien, Größe und Produktionsort hinzu.",
      "collection_title": "Beispielhafter Zusammenstellungstitel",
      "no_content": "Dieser Bereich hat zur Zeit keinen Inhalt. Füge diesem Bereich über die Seitenleiste Inhalte hinzu."
    }
  },
  "layout": {
    "cart": {
      "title": "Einkaufswagen"
    },
    "customer": {
      "account": "Account",
      "log_out": "Ausloggen",
      "log_in": "Einloggen",
      "create_account": "Account erstellen"
    },
    "footer": {
      "social_platform": "{{ name }} auf {{ platform }}"
    }
  },
  "products": {
    "general": {
      "color_swatch_trigger": "Farbe",
      "size_trigger": "Größe",
      "size_chart": "Größentabelle",
      "save_html": "Sparen {{ saved_amount }}",
      "collection_return": "Zurück zur {{ collection }}",
      "next_product": "Nächster: {{ title }}",
      "sale": "Reduziert",
      "sale_price": "Sonderpreis",
      "regular_price": "Normaler Preis",
      "from_text_html": "Von {{ price }}",
      "recent_products": "Zuletzt Angesehen",
      "reviews": "Rezensionen"
    },
    "product": {
      "description": "Beschreibung",
      "in_stock_label": "Auf Lager",
      "stock_label": {
        "one": "Geringer Lagerbestand - {{ count }} Artikel über",
        "other": "Geringer Lagerbestand - {{ counts }} Artikel über"
      },
      "sold_out": "Ausverkauft",
      "unavailable": "Nicht verfügbar",
      "quantity": "Menge",
      "add_to_cart": "In den Einkaufswagen legen",
      "preorder": "Vorbestellung",
      "include_taxes": "inkl. MwSt.",
      "shipping_policy_html": "zzgl. <a href='{{ link }}'>Versandkosten</a>",
      "will_not_ship_until": "Wird nach dem {{ date }} versendet",
      "will_be_in_stock_after": "Wird nach dem {{ date }} auf Lager sein",
      "waiting_for_stock": "Inventar auf dem Weg",
      "view_in_space": "In Ihrem Bereich ansehen",
      "view_in_space_label": "„Ansicht in Ihrem Raum“ lädt den Artikel im Augmented-Reality-Fenster"
    }
  },
  "store_availability": {
    "general": {
      "view_store_info": "Ansicht speichern Informationen",
      "check_other_stores": "Prüfen Sie die Verfügbarkeit von anderen Shops",
      "pick_up_available": "Pickup verfügbar",
      "pick_up_currently_unavailable": "Pickup derzeit nicht verfügbar",
      "pick_up_available_at_html": "Pickup verfügbar unter <strong> {{location_name}} </strong>",
      "pick_up_unavailable_at_html": "Pickup derzeit nicht verfügbar unter <strong> {{location_name}} </strong>"
    }
  },
  "gift_cards": {
    "issued": {
      "title_html": "Hier ist Ihre {{ value }} Geschenkkarte für {{ shop }}!",
      "subtext": "Hier ist Ihre Geschenkkarte!",
      "disabled": "Deaktiviert",
      "expired": "Abgelaufen am {{ expiry }}",
      "active": "Läuft ab am {{ expiry }}",
      "redeem": "Nutzen Sie an der Kasse diesen Code, um Ihre Geschenkkarte einzulösen",
      "shop_link": "Einkauf beginnen",
      "print": "Drucken",
      "add_to_apple_wallet": "Hinzufügen zu Apple Wallet"
    }
  },
  "date_formats": {
    "month_day_year": "%B %d, %Y"
  },
  "pagefly": {
    "products": {
      "product": {
        "regular_price": "Regular price",
        "sold_out": "Sold out",
        "unavailable": "Unavailable",
        "on_sale": "Sale",
        "quantity": "Quantity",
        "add_to_cart": "Add to cart",
        "back_to_collection": "Back to {{ title }}",
        "view_details": "View details"
      }
    },
    "article": {
      "tags": "Tags:",
      "all_topics": "All topics",
      "by_author": "by {{ author }}",
      "posted_in": "Posted in",
      "read_more": "Read more",
      "back_to_blog": "Back to {{ title }}"
    },
    "comments": {
      "title": "Leave a comment",
      "name": "Name",
      "email": "Email",
      "message": "Message",
      "post": "Post comment",
      "moderated": "Please note, comments must be approved before they are published",
      "success_moderated": "Your comment was posted successfully. We will publish it in a little while, as our blog is moderated.",
      "success": "Your comment was posted successfully! Thank you!",
      "comments_with_count": {
        "one": "{{ count }} comment",
        "other": "{{ count }} comments"
      }
    },
    "password_page": {
      "login_form_message": "Enter store using password:",
      "login_form_password_label": "Password",
      "login_form_password_placeholder": "Your password",
      "login_form_submit": "Enter",
      "signup_form_email_label": "Email",
      "signup_form_success": "We will send you an email right before we open!",
      "password_link": "Enter using password"
    }
  }
}
