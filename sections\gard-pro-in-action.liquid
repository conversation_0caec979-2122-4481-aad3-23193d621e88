{% if section.settings.sub_heading != blank or section.settings.heading != blank or section.blocks.size > 0 %}
  <div class="gard-pro-in-action tw-py-[60px] max-md:tw-py-[30px]">
    <div class="{% if section.settings.full_width %}full-width{% else %}page-width{% endif %}">
      {% if section.settings.sub_heading != blank or section.settings.heading != blank %}
        <div class="gard-pro-header tw-flex tw-flex-col tw-justify-center tw-text-center tw-mb-[30px]">
          {% if section.settings.sub_heading != blank %}
            <div class="sub-heading tw-mb-[30px] tw-text-[20px] max-md:tw-text-[15px] tw-text-[#A8A497] tw-font-medium tw-font-dm-sans tw-leading-[1] tw-tracking-normal tw-uppercase *:tw-m-0">
              {{ section.settings.sub_heading }}
            </div>
          {% endif %}
          {% if section.settings.heading != blank %}
            <h2 class="heading tw-m-0 tw-text-[40px] max-md:tw-text-[32px] max-md:tw-font-bold tw-text-darkblack tw-font-semibold tw-leading-[1] tw-tracking-normal tw-font-dm-sans !tw-capitalize">
              {{ section.settings.heading }}
            </h2>
          {% endif %}
        </div>
      {% endif %}
      {% if section.blocks.size > 0 %}
        <div class="swiper gard-pro-action">
          <div class="swiper-wrapper">
            {% for block in section.blocks %}
              {% if block.settings.block_image != blank %}
                <div class="gard-pro-action-img swiper-slide">
                  <img
                    src="{{ block.settings.block_image | image_url : width: 5760 }}"
                    class="right-img tw-w-full tw-object-cover tw-rounded-[30px]"
                    alt="{{ block.settings.block_image.alt }}"
                  >
                </div>
              {% endif %}
            {% endfor %}
          </div>
        </div>
      {% endif %}
    </div>
  </div>
{% endif %}
{% schema %}
{
  "name": "Oddit gard pro in action",
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "Section Full Width",
      "default": false
    },
    {
      "type": "text",
      "id": "heading",
      "label": "Heading"
    },
    {
      "type": "richtext",
      "id": "sub_heading",
      "label": "Sub Heading"
    }
  ],
  "blocks": [
    {
      "type": "image",
      "name": "Image",
      "settings": [
        {
          "type": "image_picker",
          "id": "block_image",
          "label": "Image"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Oddit gard pro in action"
    }
  ]
}
{% endschema %}
