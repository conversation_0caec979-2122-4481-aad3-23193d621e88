

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-555417746817614714.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-555417746817614714.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-555417746817614714.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-555417746817614714.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-555417746817614714.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-555417746817614714.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-555417746817614714.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-555417746817614714.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-555417746817614714.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-555417746817614714.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-555417746817614714.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-555417746817614714.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-555417746817614714.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-555417746817614714.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-555417746817614714.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-555417746817614714.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-555417746817614714.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-555417746817614714.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-555417746817614714.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-555417746817614714.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-555417746817614714.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-555417746817614714.gps.gpsil [style*="--bottom:"]{bottom:var(--bottom)}.gps-555417746817614714.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-555417746817614714.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-555417746817614714.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-555417746817614714.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-555417746817614714.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-555417746817614714.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-555417746817614714.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-555417746817614714.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-555417746817614714.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-555417746817614714.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-555417746817614714.gps.gpsil [style*="--left:"]{left:var(--left)}.gps-555417746817614714.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-555417746817614714.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-555417746817614714.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-555417746817614714.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-555417746817614714.gps.gpsil [style*="--ml:"]{margin-left:var(--ml)}.gps-555417746817614714.gps.gpsil [style*="--mt:"]{margin-top:var(--mt)}.gps-555417746817614714.gps.gpsil [style*="--maxw:"]{max-width:var(--maxw)}.gps-555417746817614714.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-555417746817614714.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-555417746817614714.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-555417746817614714.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-555417746817614714.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-555417746817614714.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-555417746817614714.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-555417746817614714.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-555417746817614714.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-555417746817614714.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-555417746817614714.gps.gpsil [style*="--right:"]{right:var(--right)}.gps-555417746817614714.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-555417746817614714.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-555417746817614714.gps.gpsil [style*="--top:"]{top:var(--top)}.gps-555417746817614714.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-555417746817614714.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-555417746817614714.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-555417746817614714.gps.gpsil [style*="--bottom-tablet:"]{bottom:var(--bottom-tablet)}.gps-555417746817614714.gps.gpsil [style*="--cg-tablet:"]{-moz-column-gap:var(--cg-tablet);column-gap:var(--cg-tablet)}.gps-555417746817614714.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-555417746817614714.gps.gpsil [style*="--gtc-tablet:"]{grid-template-columns:var(--gtc-tablet)}.gps-555417746817614714.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-555417746817614714.gps.gpsil [style*="--left-tablet:"]{left:var(--left-tablet)}.gps-555417746817614714.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-555417746817614714.gps.gpsil [style*="--ml-tablet:"]{margin-left:var(--ml-tablet)}.gps-555417746817614714.gps.gpsil [style*="--maxw-tablet:"]{max-width:var(--maxw-tablet)}.gps-555417746817614714.gps.gpsil [style*="--minw-tablet:"]{min-width:var(--minw-tablet)}.gps-555417746817614714.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-555417746817614714.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-555417746817614714.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-555417746817614714.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-555417746817614714.gps.gpsil [style*="--right-tablet:"]{right:var(--right-tablet)}.gps-555417746817614714.gps.gpsil [style*="--top-tablet:"]{top:var(--top-tablet)}.gps-555417746817614714.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-555417746817614714.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-555417746817614714.gps.gpsil [style*="--bottom-mobile:"]{bottom:var(--bottom-mobile)}.gps-555417746817614714.gps.gpsil [style*="--cg-mobile:"]{-moz-column-gap:var(--cg-mobile);column-gap:var(--cg-mobile)}.gps-555417746817614714.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-555417746817614714.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-555417746817614714.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-555417746817614714.gps.gpsil [style*="--left-mobile:"]{left:var(--left-mobile)}.gps-555417746817614714.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-555417746817614714.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-555417746817614714.gps.gpsil [style*="--ml-mobile:"]{margin-left:var(--ml-mobile)}.gps-555417746817614714.gps.gpsil [style*="--maxw-mobile:"]{max-width:var(--maxw-mobile)}.gps-555417746817614714.gps.gpsil [style*="--minw-mobile:"]{min-width:var(--minw-mobile)}.gps-555417746817614714.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-555417746817614714.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-555417746817614714.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-555417746817614714.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-555417746817614714.gps.gpsil [style*="--right-mobile:"]{right:var(--right-mobile)}.gps-555417746817614714.gps.gpsil [style*="--ta-mobile:"]{text-align:var(--ta-mobile)}.gps-555417746817614714.gps.gpsil [style*="--top-mobile:"]{top:var(--top-mobile)}.gps-555417746817614714.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-555417746817614714.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-555417746817614714 .gp-rotate-0,.gps-555417746817614714 .gp-rotate-180,.gps-555417746817614714 .mobile\:gp-rotate-0,.gps-555417746817614714 .mobile\:gp-rotate-180,.gps-555417746817614714 .tablet\:gp-rotate-0,.gps-555417746817614714 .tablet\:gp-rotate-180{--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1}.gps-555417746817614714 .gp-shadow-md{--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000}.gps-555417746817614714 .gp-g-paragraph-1{font-family:var(--g-p1-ff);font-size:var(--g-p1-size);font-style:var(--g-p1-fs);font-weight:var(--g-p1-weight);letter-spacing:var(--g-p1-ls);line-height:var(--g-p1-lh)}.gps-555417746817614714 .gp-static{position:static}.gps-555417746817614714 .\!gp-absolute{position:absolute!important}.gps-555417746817614714 .gp-relative{position:relative}.gps-555417746817614714 .gp-left-0{left:0}.gps-555417746817614714 .gp-right-0{right:0}.gps-555417746817614714 .gp-z-1{z-index:1}.gps-555417746817614714 .gp-z-2{z-index:2}.gps-555417746817614714 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-555417746817614714 .gp-my-0{margin-bottom:0;margin-top:0}.gps-555417746817614714 .gp-mb-0{margin-bottom:0}.gps-555417746817614714 .gp-block{display:block}.gps-555417746817614714 .\!gp-flex{display:flex!important}.gps-555417746817614714 .gp-flex{display:flex}.gps-555417746817614714 .gp-inline-flex{display:inline-flex}.gps-555417746817614714 .gp-grid{display:grid}.gps-555417746817614714 .gp-contents{display:contents}.gps-555417746817614714 .\!gp-hidden{display:none!important}.gps-555417746817614714 .gp-hidden{display:none}.gps-555417746817614714 .gp-aspect-square{aspect-ratio:1/1}.gps-555417746817614714 .gp-h-full{height:100%}.gps-555417746817614714 .\!gp-min-h-full{min-height:100%!important}.gps-555417746817614714 .gp-w-\[12px\]{width:12px}.gps-555417746817614714 .gp-w-full{width:100%}.gps-555417746817614714 .gp-max-w-full{max-width:100%}.gps-555417746817614714 .gp-shrink-0{flex-shrink:0}.gps-555417746817614714 .gp-rotate-0{--tw-rotate:0deg}.gps-555417746817614714 .gp-rotate-0,.gps-555417746817614714 .gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-555417746817614714 .gp-rotate-180{--tw-rotate:180deg}.gps-555417746817614714 .gp-cursor-pointer{cursor:pointer}.gps-555417746817614714 .gp-select-none{-webkit-user-select:none;-moz-user-select:none;user-select:none}.gps-555417746817614714 .\!gp-flex-row{flex-direction:row!important}.gps-555417746817614714 .gp-flex-row{flex-direction:row}.gps-555417746817614714 .gp-flex-col{flex-direction:column}.gps-555417746817614714 .gp-flex-wrap{flex-wrap:wrap}.gps-555417746817614714 .\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-555417746817614714 .gp-items-center{align-items:center}.gps-555417746817614714 .gp-justify-center{justify-content:center}.gps-555417746817614714 .gp-justify-between{justify-content:space-between}.gps-555417746817614714 .gp-gap-2{gap:8px}.gps-555417746817614714 .gp-gap-y-0{row-gap:0}.gps-555417746817614714 .gp-overflow-hidden{overflow:hidden}.gps-555417746817614714 .gp-break-words{overflow-wrap:break-word}.gps-555417746817614714 .gp-rounded-full{border-radius:9999px}.gps-555417746817614714 .gp-rounded-none{border-radius:0}.gps-555417746817614714 .gp-text-center{text-align:center}.gps-555417746817614714 .gp-leading-\[0\]{line-height:0}.gps-555417746817614714 .gp-text-g-text-3{color:var(--g-c-text-3)}.gps-555417746817614714 .gp-text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128/var(--tw-text-opacity))}.gps-555417746817614714 .gp-no-underline{text-decoration-line:none}.gps-555417746817614714 .gp-shadow-md{--tw-shadow:0 4px 6px -1px rgba(0,0,0,.1),0 2px 4px -2px rgba(0,0,0,.1);--tw-shadow-colored:0 4px 6px -1px var(--tw-shadow-color),0 2px 4px -2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.gps-555417746817614714 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555417746817614714 .gp-duration-200{transition-duration:.2s}.gps-555417746817614714 .gp-duration-300{transition-duration:.3s}.gps-555417746817614714 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555417746817614714 .disabled\:gp-btn-disabled:disabled{cursor:default}.gps-555417746817614714 .disabled\:gp-pointer-events-none:disabled{pointer-events:none}.gps-555417746817614714 .disabled\:gp-opacity-30:disabled{opacity:.3}@media (hover:hover) and (pointer:fine){.gps-555417746817614714 .gp-group\/button:hover .group-hover\/button\:\!gp-text-inherit{color:inherit!important}}.gps-555417746817614714 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-invisible{visibility:hidden}@media (max-width:1024px){.gps-555417746817614714 .tablet\:gp-static{position:static}.gps-555417746817614714 .tablet\:\!gp-absolute{position:absolute!important}.gps-555417746817614714 .tablet\:gp-left-0{left:0}.gps-555417746817614714 .tablet\:gp-right-0{right:0}.gps-555417746817614714 .tablet\:gp-z-2{z-index:2}.gps-555417746817614714 .tablet\:gp-block{display:block}.gps-555417746817614714 .tablet\:\!gp-flex{display:flex!important}.gps-555417746817614714 .tablet\:\!gp-hidden{display:none!important}.gps-555417746817614714 .tablet\:gp-hidden{display:none}.gps-555417746817614714 .tablet\:\!gp-min-h-full{min-height:100%!important}.gps-555417746817614714 .tablet\:gp-rotate-0{--tw-rotate:0deg}.gps-555417746817614714 .tablet\:gp-rotate-0,.gps-555417746817614714 .tablet\:gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-555417746817614714 .tablet\:gp-rotate-180{--tw-rotate:180deg}.gps-555417746817614714 .tablet\:\!gp-flex-row{flex-direction:row!important}.gps-555417746817614714 .tablet\:gp-flex-row{flex-direction:row}.gps-555417746817614714 .tablet\:\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-555417746817614714 .tablet\:gp-px-0{padding-left:0;padding-right:0}}@media (max-width:767px){.gps-555417746817614714 .mobile\:gp-static{position:static}.gps-555417746817614714 .mobile\:\!gp-absolute{position:absolute!important}.gps-555417746817614714 .mobile\:gp-left-0{left:0}.gps-555417746817614714 .mobile\:gp-right-0{right:0}.gps-555417746817614714 .mobile\:gp-z-2{z-index:2}.gps-555417746817614714 .mobile\:gp-block{display:block}.gps-555417746817614714 .mobile\:\!gp-flex{display:flex!important}.gps-555417746817614714 .mobile\:\!gp-hidden{display:none!important}.gps-555417746817614714 .mobile\:gp-hidden{display:none}.gps-555417746817614714 .mobile\:\!gp-min-h-full{min-height:100%!important}.gps-555417746817614714 .mobile\:gp-rotate-0{--tw-rotate:0deg}.gps-555417746817614714 .mobile\:gp-rotate-0,.gps-555417746817614714 .mobile\:gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-555417746817614714 .mobile\:gp-rotate-180{--tw-rotate:180deg}.gps-555417746817614714 .mobile\:\!gp-flex-row{flex-direction:row!important}.gps-555417746817614714 .mobile\:gp-flex-row{flex-direction:row}.gps-555417746817614714 .mobile\:\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-555417746817614714 .mobile\:gp-px-0{padding-left:0;padding-right:0}}.gps-555417746817614714 .\[\&\>svg\]\:\!gp-h-\[var\(--height-desktop\)\]>svg{height:var(--height-desktop)!important}.gps-555417746817614714 .\[\&\>svg\]\:gp-h-full>svg{height:100%}.gps-555417746817614714 .\[\&\>svg\]\:\!gp-w-auto>svg{width:auto!important}.gps-555417746817614714 .\[\&\>svg\]\:gp-w-full>svg{width:100%}@media (max-width:1024px){.gps-555417746817614714 .tablet\:\[\&\>svg\]\:\!gp-h-\[var\(--height-tablet\)\]>svg{height:var(--height-tablet)!important}}@media (max-width:767px){.gps-555417746817614714 .mobile\:\[\&\>svg\]\:\!gp-h-\[var\(--height-mobile\)\]>svg{height:var(--height-mobile)!important}}.gps-555417746817614714 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-555417746817614714 .\[\&_p\]\:gp-inline p{display:inline}.gps-555417746817614714 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}.gps-555417746817614714 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-555417746817614714 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      enableLazyloadImage="true" id="gAH6nvViti" data-id="gAH6nvViti"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-4xl);--pl:15px;--pb:var(--g-s-4xl);--pr:15px;--pt-mobile:4px;--pl-mobile:15px;--pb-mobile:var(--g-s-xxs);--pr-mobile:15px;--pt-tablet:var(--g-s-4xl);--pl-tablet:15px;--pb-tablet:var(--g-s-4xl);--pr-tablet:15px;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gAH6nvViti gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gjhQRofQa7 gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="gzqFmeLL0M" data-id="gzqFmeLL0M"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gzqFmeLL0M gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="glAV-pE6QG gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="gzxTP9k6Fe" data-id="gzxTP9k6Fe"
        style="--mb:var(--g-s-l);--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:8px;--pc:start;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gzxTP9k6Fe gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gsgixb2e-D gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="giaaIaswfn">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="giaaIaswfn "
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-3xl);--mb-mobile:18px"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:40px;--size-tablet:40px;--size-mobile:28px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;--tt:uppercase;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggiaaIaswfn_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div><div
      enableLazyloadImage="true" tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gIT8ur3gKD gp-relative gp-flex gp-flex-col"
    >
      
  <gp-button >
  <div
    style="--mb:38px;--mb-mobile:19px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--shadow:none;--op:100%;--ta:right;--ta-mobile:left"
    
  >
    <style>
    .g-7bIpRL-0.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      border-radius: var(--g-radius-small);
    }

    .g-7bIpRL-0:hover::before {
      
      
    }

    .g-7bIpRL-0:hover .gp-button-icon {
      color: undefined;
    }

     .g-7bIpRL-0 .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .g-7bIpRL-0:hover .gp-button-price {
      color: undefined;
    }

    .g-7bIpRL-0 .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .g-7bIpRL-0 .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .g-7bIpRL-0:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#gc4cTS57DT" target="_self" data-id="g-7bIpRL-0" aria-label="<p>SHOP THE RANGE</p>"
      
      data-state="idle"
      class="g-7bIpRL-0 gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center"
      style="--bg:#096de3;--hvr-bg:#1180FF;--radius:var(--g-radius-small);--shadow:none;--w:Auto;--w-tablet:Auto;--w-mobile:100%;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:24px;--pr:24px;--pt:8px;--pb:8px;--pl-mobile:24px;--pr-mobile:24px;--pt-mobile:8px;--pb-mobile:8px;--c:var(--g-c-text-3, text-3);--size:16px;--size-tablet:16px;--size-mobile:14px;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:600;--lh:180%;--lh-tablet:180%;--lh-mobile:180%;--tt:uppercase"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="group-hover/button:!gp-text-inherit gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:16px;--size-tablet:16px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.gg-7bIpRL-0_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

    </div>
    </div>
   
    
    <gp-carousel data-id="gBGB1ltsjm"  id="gp-root-carousel-gBGB1ltsjm-{{section.id}}" class="  gp-group/carousel gp-flex" gp-data='{"id":"gBGB1ltsjm-{{section.id}}","setting":{"animationMode":"ease-in","arrow":{"desktop":true,"mobile":true},"arrowBorder":{"desktop":{"border":"none","borderType":"none","borderWidth":"1px","isCustom":false,"width":"1px 1px 1px 1px"}},"arrowButtonSize":{"desktop":{"height":"32px","padding":{"linked":true},"shapeLinked":true,"shapeValue":"1/1","width":"32px"}},"arrowCustom":"<svg width=\"37\" height=\"37\" viewBox=\"0 0 37 37\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\"> <rect width=\"37\" height=\"37\" fill=\"white\"/> <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12 19.0006C12 18.868 12.0527 18.7408 12.1464 18.6471C12.2402 18.5533 12.3674 18.5006 12.5 18.5006H24.293L21.146 15.3546C21.0521 15.2607 20.9994 15.1334 20.9994 15.0006C20.9994 14.8679 21.0521 14.7405 21.146 14.6466C21.2399 14.5527 21.3672 14.5 21.5 14.5C21.6328 14.5 21.7601 14.5527 21.854 14.6466L25.854 18.6466C25.9006 18.6931 25.9375 18.7483 25.9627 18.809C25.9879 18.8697 26.0009 18.9349 26.0009 19.0006C26.0009 19.0664 25.9879 19.1315 25.9627 19.1923C25.9375 19.253 25.9006 19.3082 25.854 19.3546L21.854 23.3546C21.7601 23.4485 21.6328 23.5013 21.5 23.5013C21.3672 23.5013 21.2399 23.4485 21.146 23.3546C21.0521 23.2607 20.9994 23.1334 20.9994 23.0006C20.9994 22.8679 21.0521 22.7405 21.146 22.6466L24.293 19.5006H12.5C12.3674 19.5006 12.2402 19.448 12.1464 19.3542C12.0527 19.2604 12 19.1332 12 19.0006Z\" fill=\"#242424\"/> </svg>","arrowGapToEachSide":"16","arrowIconSize":{"desktop":24},"autoplay":false,"autoplayTimeout":2,"childItem":["Slide 1","Slide 2","Slide 3","Slide 4"],"controlOverContent":{"desktop":true,"mobile":true,"tablet":true},"dot":{"desktop":false,"mobile":false,"tablet":false},"dotActiveColor":{"desktop":"#242424"},"dotColor":{"desktop":"bg-1"},"dotGapToCarousel":{"desktop":"16","mobile":16,"tablet":16},"dotSize":{"desktop":12,"mobile":12,"tablet":12},"dotStyle":{"desktop":"outside","mobile":"none","tablet":"none"},"enableDrag":{"desktop":true},"itemNumber":{"desktop":2,"mobile":1,"tablet":1},"loop":{"desktop":false},"navigationStyle":{"desktop":"inside","mobile":"inside"},"pauseOnHover":true,"roundedArrow":{"desktop":{"radiusType":"small"}},"sneakPeak":{"desktop":false,"mobile":false,"tablet":false},"sneakPeakOffsetCenter":{"desktop":50,"mobile":50,"tablet":50},"sneakPeakOffsetForward":{"desktop":50,"mobile":50,"tablet":50},"sneakPeakType":{"desktop":"forward"},"vertical":{"desktop":false},"runPreview":false},"styles":{"align":{"desktop":"center"},"playSpeed":500,"sizeSetting":{"desktop":{"height":"auto","width":"100%"},"mobile":{"width":"100%"},"tablet":{"width":"100%"}},"spacing":{"desktop":16}},"isHiddenArrowWhenDisabled":true}' style="--jc:center">
      <div class="gp-hidden gp-aspect-square gp-w-[12px] gp-rounded-full gp-shadow-md"></div>
      <div
        
        class="gp-relative gp-my-0 tablet:gp-px-0 gp-max-w-full mobile:gp-px-0 gp-carousel-wrapper mobile:gp-block tablet:gp-block gp-block gBGB1ltsjm"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto"
      >
        <div
          class="gp-h-full gp-relative gp-mx-auto gp-flex gp-items-center gp-justify-between mobile:!gp-flex-row tablet:!gp-flex-row !gp-flex-row"
          style="--h:auto;--h-tablet:100%;--h-mobile:100%;gap:16px"
        >
          
    <button
      type="button"
      aria-label='Carousel Back Arrow'
      class="gp-z-1 gp-carousel-action-back gem-slider-previous gBGB1ltsjm-{{section.id}} gp-carousel-arrow-gBGB1ltsjm gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-flex !gp-absolute gp-z-2 tablet:!gp-flex tablet:!gp-absolute tablet:gp-z-2 mobile:!gp-flex mobile:!gp-absolute mobile:gp-z-2"
      style="--left:16px;--top:initial;--right:initial;--bottom:;--left-tablet:16px;--top-tablet:initial;--right-tablet:initial;--bottom-tablet:;--left-mobile:16px;--top-mobile:initial;--right-mobile:initial;--bottom-mobile:;--d:flex;--d-tablet:flex;--d-mobile:flex;background-color:;--w:32px;--h:32px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-180 tablet:gp-rotate-180 mobile:gp-rotate-180"
  style="--w:24px;--w-tablet:24px;--w-mobile:24px;--h:24px;--h-tablet:24px;--h-mobile:24px"
  >
    <svg width="37" height="37" viewBox="0 0 37 37" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect width="37" height="37" fill="white"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M12 19.0006C12 18.868 12.0527 18.7408 12.1464 18.6471C12.2402 18.5533 12.3674 18.5006 12.5 18.5006H24.293L21.146 15.3546C21.0521 15.2607 20.9994 15.1334 20.9994 15.0006C20.9994 14.8679 21.0521 14.7405 21.146 14.6466C21.2399 14.5527 21.3672 14.5 21.5 14.5C21.6328 14.5 21.7601 14.5527 21.854 14.6466L25.854 18.6466C25.9006 18.6931 25.9375 18.7483 25.9627 18.809C25.9879 18.8697 26.0009 18.9349 26.0009 19.0006C26.0009 19.0664 25.9879 19.1315 25.9627 19.1923C25.9375 19.253 25.9006 19.3082 25.854 19.3546L21.854 23.3546C21.7601 23.4485 21.6328 23.5013 21.5 23.5013C21.3672 23.5013 21.2399 23.4485 21.146 23.3546C21.0521 23.2607 20.9994 23.1334 20.9994 23.0006C20.9994 22.8679 21.0521 22.7405 21.146 22.6466L24.293 19.5006H12.5C12.3674 19.5006 12.2402 19.448 12.1464 19.3542C12.0527 19.2604 12 19.1332 12 19.0006Z" fill="#242424"/> </svg>
    </div>
      <style>
    .gp-carousel-arrow-gBGB1ltsjm {
      border-radius: var(--g-radius-small);
    }
    .gp-carousel-arrow-gBGB1ltsjm::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      border-radius: var(--g-radius-small);
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .gp-carousel-arrow-gBGB1ltsjm {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-gBGB1ltsjm::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
    @media only screen and (max-width: 768px) {
      .gp-carousel-arrow-gBGB1ltsjm {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-gBGB1ltsjm::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
  </style>
    </button>
  
          <div id="gp-carousel-gBGB1ltsjm-{{section.id}}" class="gem-slider gp-h-full gp-overflow-hidden gp-select-none mobile:!gp-flex-nowrap tablet:!gp-flex-nowrap !gp-flex-nowrap mobile:!gp-min-h-full tablet:!gp-min-h-full !gp-min-h-full"
          style="--cg-mobile:16px;--cg-tablet:16px;--cg:16px">
          
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      enableLazyloadImage="true" label="Carousel Item" tag="CarouselItem" type="component"
      style="--minw:calc(100% / 2 - 8px);--minw-tablet:calc(100% / 1 - 0px);--minw-mobile:calc(100% / 1 - 0px);--maxw:calc(100% / 2 - 8px);--maxw-tablet:calc(100% / 1 - 0px);--maxw-mobile:calc(100% / 1 - 0px);--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ml:0px;--ml-tablet:0px;--ml-mobile:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-gBGB1ltsjm g5DiqhJAsX"
      data-index="0"
    >
      <div
        class="gp-w-full",
        
      >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="CarouselItem" id="g-T6mdIy0n" data-id="g-T6mdIy0n"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--pt:20px;--pl:20px;--pb:20px;--pr:20px;--cg:8px;--cg-tablet:23px;--pc:start;--gtc:minmax(0, 5fr) minmax(0, 7fr);--gtc-tablet:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g-T6mdIy0n gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gPL7-Fh2zI gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="g1YfEJBKvG"
    role="presentation"
    class="gp-group/image g1YfEJBKvG"
    style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
    >
      <div
        class="pointer-events-auto h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/2K2ZUfZyF_7e55c866-148a-4aca-92a8-5462220a5a4f.jpg?v=1711838174" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTUwMCIgaGVpZ2h0PSIyMDAwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0xNTAwLTIwMDAiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjE1MDAiIGhlaWdodD0iMjAwMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTUwMCIgaGVpZ2h0PSIyMDAwIiBmaWxsPSJ1cmwoI2ctMTUwMC0yMDAwKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMTUwMCIgdG89IjE1MDAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <source media="(max-width: 1024px)" data-srcSet="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/2K2ZUfZyF_7e55c866-148a-4aca-92a8-5462220a5a4f.jpg?v=1711838174" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTUwMCIgaGVpZ2h0PSIyMDAwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0xNTAwLTIwMDAiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjE1MDAiIGhlaWdodD0iMjAwMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTUwMCIgaGVpZ2h0PSIyMDAwIiBmaWxsPSJ1cmwoI2ctMTUwMC0yMDAwKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMTUwMCIgdG89IjE1MDAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex gp_lazyload"
        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTUwMCIgaGVpZ2h0PSIyMDAwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0xNTAwLTIwMDAiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjE1MDAiIGhlaWdodD0iMjAwMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTUwMCIgaGVpZ2h0PSIyMDAwIiBmaWxsPSJ1cmwoI2ctMTUwMC0yMDAwKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMTUwMCIgdG89IjE1MDAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4="
        data-src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/2K2ZUfZyF_7e55c866-148a-4aca-92a8-5462220a5a4f.jpg?v=1711838174"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto;--btrr:8px;--bblr:8px;--bbrr:8px;--btlr:8px;--radiusType:custom"
      />
    
  </picture>
      </div>
  </div>
    </div><div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="guoYXgkcUG gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="g0Hp9UuQKK" data-id="g0Hp9UuQKK"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:0px;--pl:8px;--pr:8px;--pt-mobile:32px;--pl-mobile:6px;--pb-mobile:var(--g-s-2xl);--pr-mobile:6px;--pl-tablet:var(--g-s-3xl);--pr-tablet:var(--g-s-3xl);--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g0Hp9UuQKK gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gehPOA_dEd gp-relative gp-flex gp-flex-col"
    >
      
    
      
      
    <div
    data-id="gxTVrKWSg3"
      
      data-id="gxTVrKWSg3"
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:19px;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll">

    <div
            class="gp-flex gp-flex-wrap"
            style="--cg:4px;--jc:left">
            <div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gU20IhdRbE ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gKxk4V_ff5"
    >
      <div 
      data-id="gKxk4V_ff5"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:12px;--h:12px;--minw:12px;--height-desktop:12px;--height-tablet:12px;--height-mobile:12px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gyAVnqknSK ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gmDOGqoYdx"
    >
      <div 
      data-id="gmDOGqoYdx"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:12px;--h:12px;--minw:12px;--height-desktop:12px;--height-tablet:12px;--height-mobile:12px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gakz1R8HiA ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gPnraOTVGy"
    >
      <div 
      data-id="gPnraOTVGy"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:12px;--h:12px;--minw:12px;--height-desktop:12px;--height-tablet:12px;--height-mobile:12px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gJWGQ3opoI ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gTPnQnLjh4"
    >
      <div 
      data-id="gTPnQnLjh4"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:12px;--h:12px;--minw:12px;--height-desktop:12px;--height-tablet:12px;--height-mobile:12px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gW9W7ukw_f ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gfKxybl1Xw"
    >
      <div 
      data-id="gfKxybl1Xw"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:12px;--h:12px;--minw:12px;--height-desktop:12px;--height-tablet:12px;--height-mobile:12px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div>
         </div>
    </div>
  
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gBoab90QDm">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gBoab90QDm "
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-2xl)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:16px;--size-tablet:18px;--size-mobile:14px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggBoab90QDm_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gyG4tN6rhV">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gyG4tN6rhV "
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#242424;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggyG4tN6rhV_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
    </div>
    </div>
   
    
      </div>
    </div>
  
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      enableLazyloadImage="true" tag="CarouselItem" label="Carousel Item" type="component"
      style="--minw:calc(100% / 2 - 8px);--minw-tablet:calc(100% / 1 - 0px);--minw-mobile:calc(100% / 1 - 0px);--maxw:calc(100% / 2 - 8px);--maxw-tablet:calc(100% / 1 - 0px);--maxw-mobile:calc(100% / 1 - 0px);--ml:0px;--ml-tablet:0px;--ml-mobile:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-gBGB1ltsjm g5yXgByRai"
      data-index="1"
    >
      <div
        class="gp-w-full",
        
      >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="CarouselItem" id="geZL44F767" data-id="geZL44F767"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--pt:20px;--pl:20px;--pb:20px;--pr:20px;--cg:8px;--cg-tablet:23px;--pc:start;--gtc:minmax(0, 5fr) minmax(0, 7fr);--gtc-tablet:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="geZL44F767 gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gJ4Yv-fMXd gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gbT4vwnDwF"
    role="presentation"
    class="gp-group/image gbT4vwnDwF"
    style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
    >
      <div
        class="pointer-events-auto h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/6rOG-q4xM.jpg?v=1711837333" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTUwMCIgaGVpZ2h0PSIyMDAwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0xNTAwLTIwMDAiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjE1MDAiIGhlaWdodD0iMjAwMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTUwMCIgaGVpZ2h0PSIyMDAwIiBmaWxsPSJ1cmwoI2ctMTUwMC0yMDAwKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMTUwMCIgdG89IjE1MDAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <source media="(max-width: 1024px)" data-srcSet="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/6rOG-q4xM.jpg?v=1711837333" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTUwMCIgaGVpZ2h0PSIyMDAwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0xNTAwLTIwMDAiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjE1MDAiIGhlaWdodD0iMjAwMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTUwMCIgaGVpZ2h0PSIyMDAwIiBmaWxsPSJ1cmwoI2ctMTUwMC0yMDAwKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMTUwMCIgdG89IjE1MDAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex gp_lazyload"
        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTUwMCIgaGVpZ2h0PSIyMDAwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0xNTAwLTIwMDAiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjE1MDAiIGhlaWdodD0iMjAwMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTUwMCIgaGVpZ2h0PSIyMDAwIiBmaWxsPSJ1cmwoI2ctMTUwMC0yMDAwKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMTUwMCIgdG89IjE1MDAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4="
        data-src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/6rOG-q4xM.jpg?v=1711837333"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto;--btrr:8px;--bblr:8px;--bbrr:8px;--btlr:8px;--radiusType:custom"
      />
    
  </picture>
      </div>
  </div>
    </div><div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g9w4CUqABr gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="gmw9f0eL5O" data-id="gmw9f0eL5O"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pl:8px;--pr:8px;--pt-mobile:32px;--pl-mobile:6px;--pb-mobile:var(--g-s-2xl);--pr-mobile:6px;--pl-tablet:var(--g-s-3xl);--pr-tablet:var(--g-s-3xl);--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gmw9f0eL5O gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gtT-kfUK5r gp-relative gp-flex gp-flex-col"
    >
      
    
      
      
    <div
    data-id="gXkwrR_nIh"
      
      data-id="gXkwrR_nIh"
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:19px;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll">

    <div
            class="gp-flex gp-flex-wrap"
            style="--cg:4px;--jc:left">
            <div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gG-OrCeGHr ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gSVCsmUCw6"
    >
      <div 
      data-id="gSVCsmUCw6"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:12px;--h:12px;--minw:12px;--height-desktop:12px;--height-tablet:12px;--height-mobile:12px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gbZ-gLl75M ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gk1SlRj8OZ"
    >
      <div 
      data-id="gk1SlRj8OZ"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:12px;--h:12px;--minw:12px;--height-desktop:12px;--height-tablet:12px;--height-mobile:12px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="g6D9ZKyvS6 ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] g7_yliDU2V"
    >
      <div 
      data-id="g7_yliDU2V"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:12px;--h:12px;--minw:12px;--height-desktop:12px;--height-tablet:12px;--height-mobile:12px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gUBjCbrYcq ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gUxlxs5RSp"
    >
      <div 
      data-id="gUxlxs5RSp"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:12px;--h:12px;--minw:12px;--height-desktop:12px;--height-tablet:12px;--height-mobile:12px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gXoH_XJpCR ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gzw2zNUDZF"
    >
      <div 
      data-id="gzw2zNUDZF"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:12px;--h:12px;--minw:12px;--height-desktop:12px;--height-tablet:12px;--height-mobile:12px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div>
         </div>
    </div>
  
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gG--rirLhA">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gG--rirLhA "
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-2xl)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:16px;--size-tablet:18px;--size-mobile:14px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggG--rirLhA_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gOpChACAu5">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gOpChACAu5 "
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#242424;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggOpChACAu5_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
    </div>
    </div>
   
    
      </div>
    </div>
  
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      enableLazyloadImage="true" tag="CarouselItem" label="Carousel Item" type="component"
      style="--minw:calc(100% / 2 - 8px);--minw-tablet:calc(100% / 1 - 0px);--minw-mobile:calc(100% / 1 - 0px);--maxw:calc(100% / 2 - 8px);--maxw-tablet:calc(100% / 1 - 0px);--maxw-mobile:calc(100% / 1 - 0px);--ml:0px;--ml-tablet:0px;--ml-mobile:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-gBGB1ltsjm gpFBwGxZqA"
      data-index="2"
    >
      <div
        class="gp-w-full",
        
      >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="CarouselItem" id="gMpbZIicOD" data-id="gMpbZIicOD"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--pt:20px;--pl:20px;--pb:20px;--pr:20px;--cg:8px;--cg-tablet:23px;--pc:start;--gtc:minmax(0, 5fr) minmax(0, 7fr);--gtc-tablet:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gMpbZIicOD gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="ghOS85hiMJ gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gwcl3XPdja"
    role="presentation"
    class="gp-group/image gwcl3XPdja"
    style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
    >
      <div
        class="pointer-events-auto h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/TEGwGNcIv_mid.jpg?v=1711839062" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTA4MCIgaGVpZ2h0PSIxNDQwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0xMDgwLTE0NDAiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjEwODAiIGhlaWdodD0iMTQ0MCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTA4MCIgaGVpZ2h0PSIxNDQwIiBmaWxsPSJ1cmwoI2ctMTA4MC0xNDQwKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMTA4MCIgdG89IjEwODAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <source media="(max-width: 1024px)" data-srcSet="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/TEGwGNcIv_mid.jpg?v=1711839062" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTA4MCIgaGVpZ2h0PSIxNDQwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0xMDgwLTE0NDAiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjEwODAiIGhlaWdodD0iMTQ0MCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTA4MCIgaGVpZ2h0PSIxNDQwIiBmaWxsPSJ1cmwoI2ctMTA4MC0xNDQwKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMTA4MCIgdG89IjEwODAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex gp_lazyload"
        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTA4MCIgaGVpZ2h0PSIxNDQwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0xMDgwLTE0NDAiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjEwODAiIGhlaWdodD0iMTQ0MCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTA4MCIgaGVpZ2h0PSIxNDQwIiBmaWxsPSJ1cmwoI2ctMTA4MC0xNDQwKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMTA4MCIgdG89IjEwODAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4="
        data-src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/TEGwGNcIv_mid.jpg?v=1711839062"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto;--btrr:8px;--bblr:8px;--bbrr:8px;--btlr:8px;--radiusType:custom"
      />
    
  </picture>
      </div>
  </div>
    </div><div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g2GV3pEabP gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="gjLwHUQL5R" data-id="gjLwHUQL5R"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pl:8px;--pr:8px;--pt-mobile:32px;--pl-mobile:6px;--pb-mobile:var(--g-s-2xl);--pr-mobile:6px;--pl-tablet:var(--g-s-3xl);--pr-tablet:var(--g-s-3xl);--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gjLwHUQL5R gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gtvmYWVG1K gp-relative gp-flex gp-flex-col"
    >
      
    
      
      
    <div
    data-id="gvXy_zBU_Y"
      
      data-id="gvXy_zBU_Y"
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:18px;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll">

    <div
            class="gp-flex gp-flex-wrap"
            style="--cg:4px;--jc:left">
            <div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gJwI_iIe9g ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gLCiPa3ZMT"
    >
      <div 
      data-id="gLCiPa3ZMT"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:12px;--h:12px;--minw:12px;--height-desktop:12px;--height-tablet:12px;--height-mobile:12px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gVPyJKVswB ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gkJpEk5mGB"
    >
      <div 
      data-id="gkJpEk5mGB"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:12px;--h:12px;--minw:12px;--height-desktop:12px;--height-tablet:12px;--height-mobile:12px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gspYGVFprh ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gLiIadjl9w"
    >
      <div 
      data-id="gLiIadjl9w"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:12px;--h:12px;--minw:12px;--height-desktop:12px;--height-tablet:12px;--height-mobile:12px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="g67UQwI75v ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gepPT5RuXZ"
    >
      <div 
      data-id="gepPT5RuXZ"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:12px;--h:12px;--minw:12px;--height-desktop:12px;--height-tablet:12px;--height-mobile:12px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gEvU0bDceS ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] g7rgT5t7DV"
    >
      <div 
      data-id="g7rgT5t7DV"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:12px;--h:12px;--minw:12px;--height-desktop:12px;--height-tablet:12px;--height-mobile:12px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div>
         </div>
    </div>
  
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gvekNrK0m-">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gvekNrK0m- "
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-2xl)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:16px;--size-tablet:18px;--size-mobile:14px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggvekNrK0m-_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gIGJ1pRKPJ">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gIGJ1pRKPJ "
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#242424;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggIGJ1pRKPJ_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
    </div>
    </div>
   
    
      </div>
    </div>
  
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      enableLazyloadImage="true" tag="CarouselItem" label="Carousel Item" type="component"
      style="--minw:calc(100% / 2 - 8px);--minw-tablet:calc(100% / 1 - 0px);--minw-mobile:calc(100% / 1 - 0px);--maxw:calc(100% / 2 - 8px);--maxw-tablet:calc(100% / 1 - 0px);--maxw-mobile:calc(100% / 1 - 0px);--ml:0px;--ml-tablet:0px;--ml-mobile:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-gBGB1ltsjm gPpBMgwZ2L"
      data-index="3"
    >
      <div
        class="gp-w-full",
        
      >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="CarouselItem" id="gID5Nu0_sM" data-id="gID5Nu0_sM"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--pt:20px;--pl:20px;--pb:20px;--pr:20px;--cg:8px;--cg-tablet:23px;--pc:start;--gtc:minmax(0, 5fr) minmax(0, 7fr);--gtc-tablet:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#F6F6F6;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gID5Nu0_sM gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g2ravUee8q gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gjcIWSX8FL"
    role="presentation"
    class="gp-group/image gjcIWSX8FL"
    style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
    >
      <div
        class="pointer-events-auto h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/04nCuVIi_mid.jpg?v=1711838892" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTA4MCIgaGVpZ2h0PSIxNDQwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0xMDgwLTE0NDAiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjEwODAiIGhlaWdodD0iMTQ0MCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTA4MCIgaGVpZ2h0PSIxNDQwIiBmaWxsPSJ1cmwoI2ctMTA4MC0xNDQwKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMTA4MCIgdG89IjEwODAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <source media="(max-width: 1024px)" data-srcSet="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/04nCuVIi_mid.jpg?v=1711838892" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTA4MCIgaGVpZ2h0PSIxNDQwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0xMDgwLTE0NDAiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjEwODAiIGhlaWdodD0iMTQ0MCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTA4MCIgaGVpZ2h0PSIxNDQwIiBmaWxsPSJ1cmwoI2ctMTA4MC0xNDQwKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMTA4MCIgdG89IjEwODAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex gp_lazyload"
        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTA4MCIgaGVpZ2h0PSIxNDQwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0xMDgwLTE0NDAiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjEwODAiIGhlaWdodD0iMTQ0MCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTA4MCIgaGVpZ2h0PSIxNDQwIiBmaWxsPSJ1cmwoI2ctMTA4MC0xNDQwKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMTA4MCIgdG89IjEwODAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4="
        data-src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/04nCuVIi_mid.jpg?v=1711838892"
        width="100%"
        alt="Alt Image"
        style="--aspect:auto;--objf:cover;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto;--btrr:8px;--bblr:8px;--bbrr:8px;--btlr:8px;--radiusType:custom"
      />
    
  </picture>
      </div>
  </div>
    </div><div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gvAk1u17N_ gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="gLj4NghI1-" data-id="gLj4NghI1-"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pl:8px;--pr:8px;--pt-mobile:32px;--pl-mobile:6px;--pb-mobile:var(--g-s-2xl);--pr-mobile:6px;--pl-tablet:var(--g-s-3xl);--pr-tablet:var(--g-s-3xl);--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gLj4NghI1- gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g6yitLvln1 gp-relative gp-flex gp-flex-col"
    >
      
    
      
      
    <div
    data-id="gb5QPgy674"
      
      data-id="gb5QPgy674"
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:19px;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll">

    <div
            class="gp-flex gp-flex-wrap"
            style="--cg:4px;--jc:left">
            <div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gPyTYv2tUr ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gHoYomMn75"
    >
      <div 
      data-id="gHoYomMn75"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:12px;--h:12px;--minw:12px;--height-desktop:12px;--height-tablet:12px;--height-mobile:12px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gcKNhjeCOm ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] g7fklCjIJx"
    >
      <div 
      data-id="g7fklCjIJx"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:12px;--h:12px;--minw:12px;--height-desktop:12px;--height-tablet:12px;--height-mobile:12px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gm1YUwGFZh ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] georJmrouJ"
    >
      <div 
      data-id="georJmrouJ"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:12px;--h:12px;--minw:12px;--height-desktop:12px;--height-tablet:12px;--height-mobile:12px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gjaXXdfHdO ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gEkbRmhfPI"
    >
      <div 
      data-id="gEkbRmhfPI"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:12px;--h:12px;--minw:12px;--height-desktop:12px;--height-tablet:12px;--height-mobile:12px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gpUxFxkSFW ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gNVlaBw8io"
    >
      <div 
      data-id="gNVlaBw8io"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:12px;--h:12px;--minw:12px;--height-desktop:12px;--height-tablet:12px;--height-mobile:12px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div>
         </div>
    </div>
  
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gBw8uZnh44">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gBw8uZnh44 "
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-2xl)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:16px;--size-tablet:18px;--size-mobile:14px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--c:#575757;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggBw8uZnh44_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gRrR4FtJxn">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gRrR4FtJxn "
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#242424;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggRrR4FtJxn_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
    </div>
    </div>
   
    
      </div>
    </div>
  
          </div>
          
    <button
      type="button"
      aria-label='Carousel Next Arrow'
      class="gp-z-1 gp-carousel-action-next gem-slider-next gBGB1ltsjm-{{section.id}} gp-carousel-arrow-gBGB1ltsjm gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-flex !gp-absolute gp-z-2 tablet:!gp-flex tablet:!gp-absolute tablet:gp-z-2 mobile:!gp-flex mobile:!gp-absolute mobile:gp-z-2"
      style="--left:initial;--top:;--right:16px;--bottom:initial;--left-tablet:initial;--top-tablet:;--right-tablet:16px;--bottom-tablet:initial;--left-mobile:initial;--top-mobile:;--right-mobile:16px;--bottom-mobile:initial;--d:flex;--d-tablet:flex;--d-mobile:flex;background-color:;--w:32px;--h:32px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-0 tablet:gp-rotate-0 mobile:gp-rotate-0"
  style="--w:24px;--w-tablet:24px;--w-mobile:24px;--h:24px;--h-tablet:24px;--h-mobile:24px"
  >
    <svg width="37" height="37" viewBox="0 0 37 37" fill="none" xmlns="http://www.w3.org/2000/svg"> <rect width="37" height="37" fill="white"/> <path fill-rule="evenodd" clip-rule="evenodd" d="M12 19.0006C12 18.868 12.0527 18.7408 12.1464 18.6471C12.2402 18.5533 12.3674 18.5006 12.5 18.5006H24.293L21.146 15.3546C21.0521 15.2607 20.9994 15.1334 20.9994 15.0006C20.9994 14.8679 21.0521 14.7405 21.146 14.6466C21.2399 14.5527 21.3672 14.5 21.5 14.5C21.6328 14.5 21.7601 14.5527 21.854 14.6466L25.854 18.6466C25.9006 18.6931 25.9375 18.7483 25.9627 18.809C25.9879 18.8697 26.0009 18.9349 26.0009 19.0006C26.0009 19.0664 25.9879 19.1315 25.9627 19.1923C25.9375 19.253 25.9006 19.3082 25.854 19.3546L21.854 23.3546C21.7601 23.4485 21.6328 23.5013 21.5 23.5013C21.3672 23.5013 21.2399 23.4485 21.146 23.3546C21.0521 23.2607 20.9994 23.1334 20.9994 23.0006C20.9994 22.8679 21.0521 22.7405 21.146 22.6466L24.293 19.5006H12.5C12.3674 19.5006 12.2402 19.448 12.1464 19.3542C12.0527 19.2604 12 19.1332 12 19.0006Z" fill="#242424"/> </svg>
    </div>
      <style>
    .gp-carousel-arrow-gBGB1ltsjm {
      border-radius: var(--g-radius-small);
    }
    .gp-carousel-arrow-gBGB1ltsjm::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      border-radius: var(--g-radius-small);
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .gp-carousel-arrow-gBGB1ltsjm {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-gBGB1ltsjm::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
    @media only screen and (max-width: 768px) {
      .gp-carousel-arrow-gBGB1ltsjm {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-gBGB1ltsjm::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
  </style>
    </button>
  
        </div>
        <div
    class="gp-items-center gp-justify-center gp-gap-2 gp-text-center gp-carousel-dot-container gp-carousel-dot-container-gBGB1ltsjm-{{section.id}} gp-static gp-flex-row gp-left-0 gp-right-0 tablet:gp-static tablet:gp-flex-row tablet:gp-left-0 tablet:gp-right-0 mobile:gp-static mobile:gp-flex-row mobile:gp-left-0 mobile:gp-right-0"
    style="--mt:16px;--bottom-tablet:16px;--bottom-mobile:16px;--d:flex;--d-tablet:none;--d-mobile:none"
 ></div>
      </div>
    </gp-carousel>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-carousel-v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>

  
    </div>
    </div>
   
    
    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 4",
    "tag": "section",
    "class": "gps-555417746817614714 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/gardpro-us/apps/gempages-cro/app/shopify/edit?pageType=GP_STATIC&editorId=555417746733532026&sectionId=555417746817614714)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggiaaIaswfn_text","label":"ggiaaIaswfn_text","default":"What Our&nbsp;<br>Adventurers Say"},{"type":"html","id":"gg-7bIpRL-0_label","label":"gg-7bIpRL-0_label","default":"<p>SHOP THE RANGE</p>"},{"type":"html","id":"ggBoab90QDm_text","label":"ggBoab90QDm_text","default":"<p>\"Very nice watch with lots of features, I need it to monitor my blood pressure and it is very accurate. Love calling feature, now I talk through my watch, phone is no needed. I love to listen to various podcasts on YouTube, so now I can listen on my watch!\"</p>"},{"type":"html","id":"ggyG4tN6rhV_text","label":"ggyG4tN6rhV_text","default":"<p><span style=\"color:#242424;\"><strong>Edita K.</strong></span> <span style=\"color:#B4B4B4;font-size:13px;\">/ Happy Customer</span></p>"},{"type":"html","id":"ggG--rirLhA_text","label":"ggG--rirLhA_text","default":"<p>\"Great watch with fantastic battery life,looks a lot more expensive i also love all the health features as i have lots of health problems,dont hesitate to buy from this great company.\"</p>"},{"type":"html","id":"ggOpChACAu5_text","label":"ggOpChACAu5_text","default":"<p><span style=\"color:#242424;\"><strong>John</strong></span> <span style=\"color:#B4B4B4;font-size:13px;\">/ Happy Customer</span></p>"},{"type":"html","id":"ggvekNrK0m-_text","label":"ggvekNrK0m-_text","default":"<p>\"I'm so happy with my smart watch. It does everything I wanted it to and then some. It was easy to sync it with my phone as well. I'd highly recommend.\"</p>"},{"type":"html","id":"ggIGJ1pRKPJ_text","label":"ggIGJ1pRKPJ_text","default":"<p><span style=\"color:#242424;\"><strong>Darren P</strong></span> <span style=\"color:#B4B4B4;font-size:13px;\">/ Happy Customer</span></p>"},{"type":"html","id":"ggBw8uZnh44_text","label":"ggBw8uZnh44_text","default":"<p><span style=\"color:rgb(0,0,0);\">\"This watch was a gift for health reasons it has given the family peace of mind in watching blood pressure and heart rate it has many other benefit they are now a converted watch wearer. 10 🌟 stars\"</span></p>"},{"type":"html","id":"ggRrR4FtJxn_text","label":"ggRrR4FtJxn_text","default":"<p><span style=\"color:#242424;\"><strong>Heather P</strong></span> <span style=\"color:#B4B4B4;font-size:13px;\">/ Happy Customer</span></p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
