

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-555417746817942394.gps.gpsil [style*="--ai:"]{align-items:var(--ai)}.gps-555417746817942394.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-555417746817942394.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-555417746817942394.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-555417746817942394.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-555417746817942394.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-555417746817942394.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-555417746817942394.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-555417746817942394.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-555417746817942394.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-555417746817942394.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-555417746817942394.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-555417746817942394.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-555417746817942394.gps.gpsil [style*="--hvr-bb:"]:hover{border-bottom:var(--hvr-bb)}.gps-555417746817942394.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-555417746817942394.gps.gpsil [style*="--hvr-bc:"]:hover{border-color:var(--hvr-bc)}.gps-555417746817942394.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-555417746817942394.gps.gpsil [style*="--hvr-bblr:"]:hover{border-bottom-left-radius:var(--hvr-bblr)}.gps-555417746817942394.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-555417746817942394.gps.gpsil [style*="--hvr-bbrr:"]:hover{border-bottom-right-radius:var(--hvr-bbrr)}.gps-555417746817942394.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-555417746817942394.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-555417746817942394.gps.gpsil [style*="--hvr-bs:"]:hover{border-style:var(--hvr-bs)}.gps-555417746817942394.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-555417746817942394.gps.gpsil [style*="--hvr-bt:"]:hover{border-top:var(--hvr-bt)}.gps-555417746817942394.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-555417746817942394.gps.gpsil [style*="--hvr-btlr:"]:hover{border-top-left-radius:var(--hvr-btlr)}.gps-555417746817942394.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-555417746817942394.gps.gpsil [style*="--hvr-btrr:"]:hover{border-top-right-radius:var(--hvr-btrr)}.gps-555417746817942394.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-555417746817942394.gps.gpsil [style*="--hvr-bw:"]:hover{border-width:var(--hvr-bw)}.gps-555417746817942394.gps.gpsil [style*="--bottom:"]{bottom:var(--bottom)}.gps-555417746817942394.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-555417746817942394.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-555417746817942394.gps.gpsil [style*="--hvr-c:"]:hover{color:var(--hvr-c)}.gps-555417746817942394.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-555417746817942394.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-555417746817942394.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-555417746817942394.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-555417746817942394.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-555417746817942394.gps.gpsil [style*="--gg:"]{grid-gap:var(--gg)}.gps-555417746817942394.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-555417746817942394.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-555417746817942394.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-555417746817942394.gps.gpsil [style*="--left:"]{left:var(--left)}.gps-555417746817942394.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-555417746817942394.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-555417746817942394.gps.gpsil [style*="--tdt:"]{text-decoration-thickness:var(--tdt)}.gps-555417746817942394.gps.gpsil [style*="--tdc:"]{text-decoration-color:var(--tdc)}.gps-555417746817942394.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-555417746817942394.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-555417746817942394.gps.gpsil [style*="--mt:"]{margin-top:var(--mt)}.gps-555417746817942394.gps.gpsil [style*="--maxw:"]{max-width:var(--maxw)}.gps-555417746817942394.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-555417746817942394.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-555417746817942394.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-555417746817942394.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-555417746817942394.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-555417746817942394.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-555417746817942394.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-555417746817942394.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-555417746817942394.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-555417746817942394.gps.gpsil [style*="--pos:"]{position:var(--pos)}.gps-555417746817942394.gps.gpsil [style*="--rg:"]{row-gap:var(--rg)}.gps-555417746817942394.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-555417746817942394.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-555417746817942394.gps.gpsil [style*="--top:"]{top:var(--top)}.gps-555417746817942394.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-555417746817942394.gps.gpsil [style*="--w:"]{width:var(--w)}@media only screen and (max-width:1024px){.gps-555417746817942394.gps.gpsil [style*="--aspect-tablet:"]{aspect-ratio:var(--aspect-tablet)}.gps-555417746817942394.gps.gpsil [style*="--bottom-tablet:"]{bottom:var(--bottom-tablet)}.gps-555417746817942394.gps.gpsil [style*="--cg-tablet:"]{-moz-column-gap:var(--cg-tablet);column-gap:var(--cg-tablet)}.gps-555417746817942394.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-555417746817942394.gps.gpsil [style*="--gtc-tablet:"]{grid-template-columns:var(--gtc-tablet)}.gps-555417746817942394.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-555417746817942394.gps.gpsil [style*="--jc-tablet:"]{justify-content:var(--jc-tablet)}.gps-555417746817942394.gps.gpsil [style*="--left-tablet:"]{left:var(--left-tablet)}.gps-555417746817942394.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-555417746817942394.gps.gpsil [style*="--maxw-tablet:"]{max-width:var(--maxw-tablet)}.gps-555417746817942394.gps.gpsil [style*="--o-tablet:"]{order:var(--o-tablet)}.gps-555417746817942394.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-555417746817942394.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-555417746817942394.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-555417746817942394.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-555417746817942394.gps.gpsil [style*="--pos-tablet:"]{position:var(--pos-tablet)}.gps-555417746817942394.gps.gpsil [style*="--top-tablet:"]{top:var(--top-tablet)}.gps-555417746817942394.gps.gpsil [style*="--t-tablet:"]{transform:var(--t-tablet)}.gps-555417746817942394.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}}@media only screen and (max-width:767px){.gps-555417746817942394.gps.gpsil [style*="--aspect-mobile:"]{aspect-ratio:var(--aspect-mobile)}.gps-555417746817942394.gps.gpsil [style*="--bottom-mobile:"]{bottom:var(--bottom-mobile)}.gps-555417746817942394.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-555417746817942394.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-555417746817942394.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-555417746817942394.gps.gpsil [style*="--jc-mobile:"]{justify-content:var(--jc-mobile)}.gps-555417746817942394.gps.gpsil [style*="--left-mobile:"]{left:var(--left-mobile)}.gps-555417746817942394.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-555417746817942394.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-555417746817942394.gps.gpsil [style*="--mt-mobile:"]{margin-top:var(--mt-mobile)}.gps-555417746817942394.gps.gpsil [style*="--maxw-mobile:"]{max-width:var(--maxw-mobile)}.gps-555417746817942394.gps.gpsil [style*="--o-mobile:"]{order:var(--o-mobile)}.gps-555417746817942394.gps.gpsil [style*="--pc-mobile:"]{place-content:var(--pc-mobile)}.gps-555417746817942394.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-555417746817942394.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-555417746817942394.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-555417746817942394.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-555417746817942394.gps.gpsil [style*="--pos-mobile:"]{position:var(--pos-mobile)}.gps-555417746817942394.gps.gpsil [style*="--rg-mobile:"]{row-gap:var(--rg-mobile)}.gps-555417746817942394.gps.gpsil [style*="--top-mobile:"]{top:var(--top-mobile)}.gps-555417746817942394.gps.gpsil [style*="--t-mobile:"]{transform:var(--t-mobile)}.gps-555417746817942394.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}}.gps-555417746817942394 .-gp-translate-x-1\/2,.gps-555417746817942394 .-gp-translate-y-1\/2{--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1}.gps-555417746817942394 .gp-shadow-none{--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000}.gps-555417746817942394 .gp-invisible{visibility:hidden}.gps-555417746817942394 .gp-absolute{position:absolute}.gps-555417746817942394 .gp-relative{position:relative}.gps-555417746817942394 .gp-sticky{position:sticky}.gps-555417746817942394 .gp-left-0{left:0}.gps-555417746817942394 .gp-left-1\/2{left:50%}.gps-555417746817942394 .gp-top-0{top:0}.gps-555417746817942394 .gp-top-1\/2{top:50%}.gps-555417746817942394 .gp-z-0{z-index:0}.gps-555417746817942394 .gp-z-1{z-index:1}.gps-555417746817942394 .gp-z-\[90\]{z-index:90}.gps-555417746817942394 .\!gp-m-0{margin:0!important}.gps-555417746817942394 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-555417746817942394 .\!gp-ml-0{margin-left:0!important}.gps-555417746817942394 .gp-mb-0{margin-bottom:0}.gps-555417746817942394 .gp-block{display:block}.gps-555417746817942394 .gp-flex{display:flex}.gps-555417746817942394 .gp-inline-flex{display:inline-flex}.gps-555417746817942394 .gp-grid{display:grid}.gps-555417746817942394 .\!gp-hidden{display:none!important}.gps-555417746817942394 .gp-hidden{display:none}.gps-555417746817942394 .gp-aspect-\[56\/32\]{aspect-ratio:56/32}.gps-555417746817942394 .gp-aspect-square{aspect-ratio:1/1}.gps-555417746817942394 .gp-h-0{height:0}.gps-555417746817942394 .gp-h-5{height:20px}.gps-555417746817942394 .gp-h-auto{height:auto}.gps-555417746817942394 .gp-h-full{height:100%}.gps-555417746817942394 .\!gp-w-full{width:100%!important}.gps-555417746817942394 .gp-w-14{width:56px}.gps-555417746817942394 .gp-w-5{width:20px}.gps-555417746817942394 .gp-w-full{width:100%}.gps-555417746817942394 .gp-min-w-\[45px\]{min-width:45px}.gps-555417746817942394 .\!gp-max-w-full{max-width:100%!important}.gps-555417746817942394 .gp-max-w-full{max-width:100%}.gps-555417746817942394 .gp-shrink-\[99999\]{flex-shrink:99999}.gps-555417746817942394 .-gp-translate-x-1\/2{--tw-translate-x:-50%}.gps-555417746817942394 .-gp-translate-x-1\/2,.gps-555417746817942394 .-gp-translate-y-1\/2{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-555417746817942394 .-gp-translate-y-1\/2{--tw-translate-y:-50%}.gps-555417746817942394 .gp-cursor-default{cursor:default}.gps-555417746817942394 .gp-cursor-pointer{cursor:pointer}.gps-555417746817942394 .gp-appearance-none{-webkit-appearance:none;-moz-appearance:none;appearance:none}.gps-555417746817942394 .gp-flex-col{flex-direction:column}.gps-555417746817942394 .gp-flex-wrap{flex-wrap:wrap}.gps-555417746817942394 .gp-items-start{align-items:flex-start}.gps-555417746817942394 .gp-items-end{align-items:flex-end}.gps-555417746817942394 .gp-items-center{align-items:center}.gps-555417746817942394 .gp-justify-start{justify-content:flex-start}.gps-555417746817942394 .gp-justify-center{justify-content:center}.gps-555417746817942394 .gp-gap-3{gap:12px}.gps-555417746817942394 .gp-gap-y-0{row-gap:0}.gps-555417746817942394 .gp-overflow-hidden{overflow:hidden}.gps-555417746817942394 .gp-truncate{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.gps-555417746817942394 .gp-break-words{overflow-wrap:break-word}.gps-555417746817942394 .\!gp-rounded-none{border-radius:0!important}.gps-555417746817942394 .gp-rounded{border-radius:4px}.gps-555417746817942394 .gp-rounded-none{border-radius:0}.gps-555417746817942394 .\!gp-border-x-0{border-left-width:0!important;border-right-width:0!important}.gps-555417746817942394 .gp-border-y{border-bottom-width:1px;border-top-width:1px}.gps-555417746817942394 .gp-border-g-line-1{border-color:var(--g-c-line-1)}.gps-555417746817942394 .gp-border-g-line-2{border-color:var(--g-c-line-2)}.gps-555417746817942394 .gp-bg-black\/80{background-color:rgba(0,0,0,.8)}.gps-555417746817942394 .gp-bg-g-bg-2{background-color:var(--g-c-bg-2)}.gps-555417746817942394 .gp-bg-g-bg-3{background-color:var(--g-c-bg-3)}.gps-555417746817942394 .gp-bg-transparent{background-color:transparent}.gps-555417746817942394 .gp-bg-auto{background-size:auto}.gps-555417746817942394 .gp-object-cover{-o-object-fit:cover;object-fit:cover}.gps-555417746817942394 .gp-px-4{padding-left:16px;padding-right:16px}.gps-555417746817942394 .\!gp-pb-0{padding-bottom:0!important}.gps-555417746817942394 .gp-pl-4{padding-left:16px}.gps-555417746817942394 .gp-pr-6{padding-right:24px}.gps-555417746817942394 .gp-text-center{text-align:center}.gps-555417746817942394 .gp-text-g-text-2{color:var(--g-c-text-2)}.gps-555417746817942394 .gp-text-g-text-3{color:var(--g-c-text-3)}.gps-555417746817942394 .gp-text-white{--tw-text-opacity:1;color:rgb(255 255 255/var(--tw-text-opacity))}.gps-555417746817942394 .gp-line-through{text-decoration-line:line-through}.gps-555417746817942394 .gp-no-underline{text-decoration-line:none}.gps-555417746817942394 .gp-decoration-g-text-1{text-decoration-color:var(--g-c-text-1)}.gps-555417746817942394 .gp-opacity-25{opacity:.25}.gps-555417746817942394 .gp-opacity-30{opacity:.3}.gps-555417746817942394 .gp-opacity-75{opacity:.75}.gps-555417746817942394 .gp-shadow-none{--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.gps-555417746817942394 .gp-outline-none{outline:2px solid transparent;outline-offset:2px}.gps-555417746817942394 .gp-transition-all{transition-duration:.15s;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555417746817942394 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555417746817942394 .gp-transition-opacity{transition-duration:.15s;transition-property:opacity;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555417746817942394 .gp-duration-150{transition-duration:.15s}.gps-555417746817942394 .gp-duration-200{transition-duration:.2s}.gps-555417746817942394 .gp-duration-300{transition-duration:.3s}.gps-555417746817942394 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555417746817942394 .disabled\:gp-btn-disabled:disabled{cursor:default}@media (hover:hover) and (pointer:fine){.gps-555417746817942394 .hover\:gp-border-g-line-3:hover{border-color:var(--g-c-line-3)}.gps-555417746817942394 .hover\:gp-bg-\[\#ef0800\]:hover{--tw-bg-opacity:1;background-color:rgb(239 8 0/var(--tw-bg-opacity))}.gps-555417746817942394 .hover\:gp-bg-g-bg-3:hover{background-color:var(--g-c-bg-3)}.gps-555417746817942394 .hover\:gp-text-black:hover{--tw-text-opacity:1;color:rgb(0 0 0/var(--tw-text-opacity))}.gps-555417746817942394 .hover\:gp-text-g-text-2:hover{color:var(--g-c-text-2)}}.gps-555417746817942394 .active\:gp-bg-g-bg-3:active{background-color:var(--g-c-bg-3)}.gps-555417746817942394 .active\:gp-text-g-text-2:active{color:var(--g-c-text-2)}.gps-555417746817942394 .disabled\:gp-pointer-events-none:disabled{pointer-events:none}.gps-555417746817942394 .disabled\:gp-cursor-not-allowed:disabled{cursor:not-allowed}.gps-555417746817942394 .disabled\:gp-opacity-30:disabled{opacity:.3}@media (hover:hover) and (pointer:fine){.gps-555417746817942394 .gp-group\/button:hover .group-hover\/button\:\!gp-text-inherit{color:inherit!important}}.gps-555417746817942394 .data-\[disabled\=true\]\:gp-hidden[data-disabled=true],.gps-555417746817942394 .data-\[hidden\=true\]\:gp-hidden[data-hidden=true]{display:none}.gps-555417746817942394 .data-\[disabled\=true\]\:gp-opacity-60[data-disabled=true]{opacity:.6}.gps-555417746817942394 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-visible{visibility:visible}.gps-555417746817942394 .gp-group[data-state=loading] .group-data-\[state\=loading\]\:gp-invisible,.gps-555417746817942394 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-invisible{visibility:hidden}@keyframes gp-spin{to{transform:rotate(1turn)}}.gps-555417746817942394 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-animate-spin{animation:gp-spin 1s linear infinite}@media (max-width:1024px){.gps-555417746817942394 .tablet\:\!gp-hidden{display:none!important}.gps-555417746817942394 .tablet\:gp-hidden{display:none}}@media (max-width:767px){.gps-555417746817942394 .mobile\:\!gp-hidden{display:none!important}.gps-555417746817942394 .mobile\:gp-hidden{display:none}}.gps-555417746817942394 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-555417746817942394 .\[\&_p\]\:gp-inline p{display:inline}.gps-555417746817942394 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}.gps-555417746817942394 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-555417746817942394 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

    
    <gp-sticky
    data-id="g1nVKarjWG"
      gp-data='{"uid":"g1nVKarjWG","setting":{"display":{"desktop":"after-first-cart-button"}},"advanced":{"d":{"desktop":true,"mobile":true,"tablet":true}}}'
      id="g1nVKarjWG"
      data-id="g1nVKarjWG"
      class="g1nVKarjWG {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}  "
      style="margin:0 auto;--bs:solid;--bw:0px;--bc:#121212;--shadow:0px 0px 10px 0px #0000001a;--d:none;--d-mobile:none;--d-tablet:none;--op:100%;--pt:var(--g-s-m);--pl:15px;--pb:var(--g-s-m);--pr:15px;--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FBFBFB;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;--top:auto;--bottom:0;--pos:fixed;--top-tablet:auto;--bottom-tablet:0;--pos-tablet:fixed;--top-mobile:auto;--bottom-mobile:0;--pos-mobile:fixed;--left:50%;--t:translateX(-50%);--left-tablet:50%;--t-tablet:translateX(-50%);--left-mobile:50%;--t-mobile:translateX(-50%);z-index:100000"
    >
      <div >
        <div
      enableLazyloadImage="true"
      
      class="gJG4uF2noV gp-relative gp-flex gp-flex-col"
    >
      
    {%- assign gpBkProduct = product -%}
    
        {%- liquid
          if request.page_type == 'product'
            if 'static' == 'static'
              if '14924340658558' == 'latest'
                paginate collections.all.products by 100000
                  assign product = collections.all.products | sort: 'created_at' | reverse | first
                endpaginate
              else
                assign product = all_products['gard-pro-health-smartwatch-3-1']
                assign productId = '14924340658558' | times: 1
                if product == empty or product == null
                  paginate collections.all.products by 100000
                    for item in collections.all.products
                      if item.id == productId
                        assign product = item
                      endif
                    endfor
                  endpaginate
                endif
              endif
            endif
          else
            if '14924340658558' == 'latest'
              paginate collections.all.products by 100000
                assign product = collections.all.products | sort: 'created_at'| reverse | first
              endpaginate
            else
              assign product = all_products['gard-pro-health-smartwatch-3-1']
              assign productId = '14924340658558' | times: 1
              if product == empty or product == null
                paginate collections.all.products by 100000
                  for item in collections.all.products
                    if item.id == productId
                      assign product = item
                    endif
                  endfor
                endpaginate
              endif
            endif
          endif
        -%}
      

    {%-if product != empty and product != null -%}
      {%- assign initVariantId =  -%}
      {%- assign product_form_id = 'product-form-' | append: "g0ZkmwdlcQ" -%}
      {%- assign variant = product.selected_or_first_available_variant -%}
      {%- assign productSelectedVariant = product.selected_or_first_available_variant -%}
      {%-if productSelectedVariant == empty or productSelectedVariant == null -%}
        {%- assign productSelectedVariant = product.selected_or_first_available_variant -%}
      {%- endif -%}
      {%-if variant == empty or variant == null -%}
        {%- assign variant = product.selected_or_first_available_variant -%}
      {%- endif -%}
      <gp-product data-uid="g0ZkmwdlcQ" data-id="g0ZkmwdlcQ"   gp-context='{"productId": {{ product.id }}, "preSelectedOptionIds": [], "isSyncProduct": "true", "variantSelected": {{ variant | json | escape }}, "inventoryQuantity": {{ variant.inventory_quantity }}, "quantity": 1, "formId": "{{ product_form_id }}" }'
        gp-data='{"variantSelected": {{ variant | json | escape }}, "quantity": 1, "productUrl":{{ product.url | json | escape }}, "productHandle":{{ product.handle | json | escape }}, "collectionUrl": {{ collection.url | json | escape }}, "collectionHandle": {{ collection.handle | json | escape }}}'>
        <product-form class="product-form">
          {%- form 'product', product, id: product_form_id, class: 'form contents', data-type: 'add-to-cart-form', autocomplete: 'off'  -%}
            <input type="hidden" name="id" value="{{ variant.id }}" />
            <input type="hidden" name="quantity" value="{{ quantity }}" />
            <button type="submit" onclick="return false;" style="display:none;"></button>
            
       
      
    <div
      id="g0ZkmwdlcQ" data-id="g0ZkmwdlcQ-row"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:0px;--pl:0px;--pb:0px;--pr:0px;--pt-mobile:0px;--pl-mobile:0px;--pb-mobile:0px;--pr-mobile:0px;--cg:40px;--cg-tablet:12px;--pc:start;--gtc:minmax(0, 5fr) minmax(0, 7fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%"
        class="g0ZkmwdlcQ gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    {% render 'gp-section-555417746817942394-0', product: product, variant: variant, product_form_id: product_form_id, productSelectedVariant: productSelectedVariant, form: form %}<div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gmfwduqdFj gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="gl0wvcZHmd" data-id="gl0wvcZHmd"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--mt:0px;--cg:16px;--pc:end;--pc-mobile:center;--gtc:minmax(0, auto) minmax(0, auto);--gtc-tablet:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gl0wvcZHmd gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:center;--jc-mobile:center;--o:0"
      class="gmK4yCj0-3 gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="giXUaSsXPx" data-id="giXUaSsXPx"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:0px;--mb-mobile:var(--g-s-xl);--cg:16px;--pc:start;--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, 6fr) minmax(0, 6fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="giXUaSsXPx gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gcQAnoSLfb gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:0px" class="gtOuq-lCAz ">
      
  {%- assign total_combinations = 1 -%}
  {%- for option in product.options_with_values -%}
    {%- assign total_combinations = total_combinations | times: option.values.size -%}
  {%- endfor -%}
  <gp-product-variants
    data-id="gtOuq-lCAz"
    
    has-pre-selected="true"
    gp-data='{
      "setting":{"blankText":"Please select an option","column":{"desktop":1},"combineFullWidth":{"desktop":true},"combineHeight":"50px","hasPreSelected":true,"label":true,"layout":{"desktop":"vertical"},"optionAlign":{"desktop":"left"},"optionType":"groupOption","price":true,"showAsSwatches":true,"soldOutMark":true,"soldOutStyle":"line","variantPresets":[{"hide":false,"optionName":"base","optionType":"dropdown","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}}}},{"hide":false,"optionName":"SELECT YOUR COLOR","optionType":"color","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}}}},{"hide":false,"optionName":"Size","optionType":"dropdown","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}}}},{"hide":false,"optionName":"Color","optionType":"color","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}}}},{"hide":false,"optionName":"Size 2","optionType":"rectangle_list","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}}}},{"hide":false,"optionName":"Select Color","optionType":"image_shopify","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}}}},{"hide":false,"optionName":"SELECT YOUR SIZE","optionType":"dropdown","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}}}},{"hide":false,"optionName":"SELECT COLOR","optionType":"color","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}}}},{"hide":false,"optionName":"SELECT SIZE","optionType":"rectangle_list","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}}}},{"hide":false,"optionName":"Select Size","optionType":"rectangle_list","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}}}},{"hide":false,"optionName":"GemSleep Pack","optionType":"rectangle_list","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}}}},{"hide":false,"optionName":"Option","optionType":"rectangle_list","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}}}},{"hide":false,"optionName":"Camera Style","optionType":"rectangle_list","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}}}},{"hide":false,"optionName":"Style","optionType":"dropdown","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}}}},{"hide":false,"optionName":"Serving Size","optionType":"rectangle_list","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}}}},{"hide":false,"optionName":"Color Option","optionType":"dropdown","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}}}},{"hide":false,"optionName":"Colors","optionType":"color","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}}}},{"hide":false,"optionName":"COLOR","optionType":"rectangle_list","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}}}},{"hide":false,"optionName":"Size 3","optionType":"dropdown","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}}}},{"hide":false,"optionName":"Storage","optionType":"rectangle_list","presets":{"color":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"45px","tablet":"45px"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"fullWidth":{"desktop":true,"mobile":true,"tablet":true},"height":"50px","width":{"desktop":"100%","mobile":"100%","tablet":"100%"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}},"image":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":"","mobile":"64px","tablet":"64px"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"height":"50px","spacing":"var(--g-s-m)","width":{"desktop":""},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionRounded":{"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"}}}}]},
      "styles":{"align":{"desktop":"left"},"dropdownItemWidth":{"desktop":"fill","mobile":"fill","tablet":"fill"},"fixedDropdownWidth":{"desktop":"240px"},"fullWidth":{"desktop":true},"labelColor":"text-2","labelGap":"8px","labelTypo":{"attrs":{"color":"text-1"},"type":"paragraph-2"},"marginBottom":{"desktop":"24px","mobile":"var(--g-s-xl)"},"optionBgColor":{"active":"bg-3","hover":"bg-3","normal":"bg-3"},"optionBorder":{"active":{"border":"solid","borderType":"style-3","borderWidth":"2px","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px"},"hover":{"border":"solid","borderType":"style-3","borderWidth":"1px","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px"},"normal":{"border":"solid","borderType":"style-2","borderWidth":"1px","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px"}},"optionHasShadow":{"normal":false},"optionRounded":{"active":{"radiusType":"none"},"hover":{"radiusType":"none"},"normal":{"bblr":"6px","bbrr":"6px","btlr":"6px","btrr":"6px","radiusType":"custom"}},"optionShadow":{"normal":{"angle":90,"blur":"12px","color":"rgba(0, 0, 0, 0.20)","distance":"4px","spread":"0px","type":"shadow-1"}},"optionSpacing":"30px","optionTextColor":{"active":"text-2","hover":"text-2","normal":"text-2"},"optionTypo":{"custom":{"fontSize":{"desktop":"16px","mobile":"14px","tablet":"16px"},"fontStyle":"normal","fontWeight":"400","letterSpacing":"normal","lineHeight":{"desktop":"150%","mobile":"150%","tablet":"150%"}},"type":"paragraph-2"},"swatchAutoWidth":{"desktop":true},"swatchHeight":{"desktop":"50px"},"swatchItemWidth":{"desktop":"auto"},"swatchSpacing":"var(--g-s-m)","swatchWidth":{"desktop":"80px"},"width":{"desktop":"400px"}},
      "variants":{{product.variants | json | escape}},
      "optionsWithValues": {{product.options_with_values | json | escape}},
      "variantSelected": {{ variant | json | escape }},
      "variantInventoryQuantity": {{product.variants | map: 'inventory_quantity' | json | escape}},
      "variantInventoryPolicy": {{product.variants | map: 'inventory_policy' | json | escape}},
      "moneyFormat": {{shop.money_format | json | escape}},
      "productId": {{product.id | json | escape}},
      "productUrl": {{product.url | json | escape}},
      "productHandle": {{product.handle | json | escape}},
      "displayState": {"desktop":true,"mobile":true,"tablet":true},
      "totalVariantCombinations": {{total_combinations}},
      "firstAvailableVariant": {{product.selected_or_first_available_variant | json | escape}}
    }
  '>
    {%- assign options = product.options_with_values -%}
    {%- assign variants = product.variants -%}
    {%- if options.size == 0 or options.size == 1 and variants.size == 1 and variants[0].title == 'Default Title' and variants[0].option1 == 'Default Title' -%}
      <div></div>
    {% else %}
      <div
      class="gp-grid !gp-ml-0"
      style="--gtc:repeat(1, minmax(0, 1fr));--ta:left;--w:100%;--w-tablet:100%;--w-mobile:100%;--rg:24px;--rg-mobile:var(--g-s-xl)"
    >
      
    <div
        className="gp-flex gp-justify-start"
            >
            
    <select
    aria-label={{option.name | escape}}
    autocomplete="off"
    id="p-variant-group-dropdown"
    name="{%- if option -%}{{option.name | escape}}{% else %}Select Option{%- endif -%}"
    option-data="{{option.name}}"
    option-type="{{optionType}}"
    class="gp-truncate gp-bg-auto gp-pl-4 gp-pr-6 gp-outline-none dropdown-option-item gp-border-g-line-2 hover:gp-border-g-line-3 active:gp-text-g-text-2 hover:gp-text-g-text-2 gp-text-g-text-2 active:gp-bg-g-bg-3 hover:gp-bg-g-bg-3 gp-bg-g-bg-3 gp-outline-none gp-shadow-none"
    style="--shadow:none;--bg:var(--g-c-bg-3, bg-3);--bs:solid;--bw:1px 1px 1px 1px;--bc:var(--g-c-line-2, line-2);--h:50px;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--hvr-c:var(--g-c-text-2, text-2);--c:var(--g-c-text-2, text-2);--hvr-bg:var(--g-c-bg-3, bg-3);--w:auto;--w-tablet:auto;--w-mobile:auto;--hvr-bs:solid;--hvr-bw:1px 1px 1px 1px;--hvr-bc:var(--g-c-line-3, line-3);--bblr:6px;--bbrr:6px;--btlr:6px;--btrr:6px;appearance:none;background-image:url(https://cdn.shopify.com/s/files/1/1827/4239/t/1/assets/ico-select.svg?v=155563818344741998551488860031);background-repeat:no-repeat;background-position:right 16px center"
  >
  
  {%- for variantItem in variants -%}
    {%- if variantItem.id == variant.id -%}
              <option origin-price="{{variantItem.price}}" selected value="{{variantItem.id}}" key="{{variantItem.id}}">
              {{variantItem.title}} - {{variantItem.price | money}}
              </option>
            {% else %}
                <option origin-price="{{variantItem.price}}" value="{{variantItem.id}}" key="{{variantItem.id}}">
                {{variantItem.title}} - {{variantItem.price | money}}
                </option>
            {%- endif -%}
     
    {%- endfor -%}
  
  </select>
    
            </div>
    
    </div>
    {%- endif -%}

  </gp-product-variants>
  <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-variant-v3.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
   
      </div>
    </div><div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gl98ShU06J gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small)" class="gPQJgxhSvi ">
      
    {%- liquid
      assign current_variant = product.selected_or_first_available_variant
      assign available = current_variant.available | default: false
    -%}
      <gp-product-quantity
        data-id="gPQJgxhSvi"
        data-disabled="{%- if available -%} false {%- else -%} true {%- endif -%}"
        data-price=""
        class="quantityClass gp-relative gp-inline-flex gp-w-full gp-bg-transparent gp-transition-all gp-duration-150 data-[disabled=true]:gp-opacity-60 "
        style="--h:50px;--jc:center"
      >

      
          <button
            title="Decrement"
            aria-label="decrement"
            {% if available == false %} disabled {% endif %}
            class="gp-border-g-line-1 gp-bg-g-bg-3 gp-minus gp-flex gp-aspect-square gp-h-full gp-cursor-pointer gp-items-center gp-justify-center gp-outline-none disabled:gp-cursor-not-allowed"
            style="--w:50px;--bg:var(--g-c-bg-3, bg-3);--c:#242424;--bs:solid;--bw:1px 1px 1px 1px;--bc:var(--g-c-line-1, line-1);--bblr:6px;--bbrr:auto;--btlr:6px;--btrr:auto;--hvr-bblr:6px;--hvr-btlr:6px"
          >
            <span class="gp-inline-flex gp-items-center gp-justify-center" style="--w:21px;--w-tablet:21px;--w-mobile:21px">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="gp-h-full gp-w-full"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 12H6" />
              </svg>
            </span>
          </button>
      
        <input
          type="text"
          name="product-quantity"
          {% if available == false %} disabled {% endif %}
          class="gp-border-g-line-1 gp-bg-g-bg-3 !gp-border-x-0 gp-px-4 gp-flex gp-shadow-none gp-appearance-none gp-items-center gp-border-y gp-text-center gp-outline-none gp-transition-all gp-duration-150 hover:gp-text-black disabled:gp-pointer-events-none gp-h-auto gp-rounded-none gp-shrink-[99999] gp-w-full gp-min-w-[45px]"
          style="--maxw:90px;--maxw-tablet:50px;--maxw-mobile:100%;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--c:#242424;--bg:var(--g-c-bg-3, bg-3);--bs:solid;--bw:1px 1px 1px 1px;--bc:var(--g-c-line-1, line-1)"
          aria-label="Quantity"
          inputmode="numeric"
          min="{{ current_variant.quantity_rule.min }}"
          step="{{ current_variant.quantity_rule.increment }}"
          value="1"
          autocomplete='off'
        />

        
          <button
            {% if available == false %} disabled {% endif %}
            aria-label="increment"
            title="Increment"
            class="gp-border-g-line-1 gp-bg-g-bg-3 gp-plus gp-flex gp-aspect-square gp-h-full gp-cursor-pointer gp-items-center gp-justify-center gp-outline-none gp-transition-all gp-duration-150 disabled:gp-pointer-events-none"
            style="--w:50px;--bg:var(--g-c-bg-3, bg-3);--c:#242424;--bs:solid;--bw:1px 1px 1px 1px;--bc:var(--g-c-line-1, line-1);--bblr:auto;--bbrr:6px;--btlr:auto;--btrr:6px;--hvr-bbrr:6px;--hvr-btrr:6px"
          >
            <span class="gp-inline-flex gp-items-center gp-justify-center" style="--w:21px;--w-tablet:21px;--w-mobile:21px">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                class="gp-h-full gp-w-full"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                />
              </svg>
            </span>
          </button>
        
      </gp-product-quantity>
      <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-quantity.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
      </div>
    </div>
    </div>
   
    
    </div><div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:center;--jc-mobile:center"
      class="gXMN01lmGk gp-relative gp-flex gp-flex-col"
    >
      
   {%- liquid
      assign inventory_quantity = variant.inventory_quantity | default: 0
    -%}
    <gp-product-button
      class="gp-product-button"
      gp-data-wrapper="true"
      gp-data='{"setting":{"actionEffect":"open-cart-drawer","errorMessage":"Cannot add product to cart","successMessage":"Add product to cart successfully","enableSuccessMessage":true,"enableErrorMessage":true,"label":"Add to Cart","outOfStockLabel":"Out of stock","customURL":{}},"styles":{"errorTypo":{"attrs":{"color":"error"},"type":"paragraph-1"},"successTypo":{"attrs":{"color":"success"},"type":"paragraph-1"}},"disabled":"{{variant.available}}","variantID":"{{variant.id}}","totalVariant":"{{product.variants.size}}"}' 
       gp-href="{{ request.origin }}{{ routes.root_url | split: '/' | join: '/' }}/cart"
       data-variant-selection-required-message="{{}}"
    >
        
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:5px;--bbrr:5px;--btlr:5px;--btrr:5px;--ta:left"
    class="{% unless variant.available %} !gp-hidden {% endunless %}"
  >
    <style>
    .g4jUaJii-4.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-medium);
    }

    .g4jUaJii-4:hover::before {
      
      
    }

    .g4jUaJii-4:hover .gp-button-icon {
      color: undefined;
    }

     .g4jUaJii-4 .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .g4jUaJii-4:hover .gp-button-price {
      color: undefined;
    }

    .g4jUaJii-4 .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .g4jUaJii-4 .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .g4jUaJii-4:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <button
      type="submit" data-id="g4jUaJii-4" aria-label="Add to Cart"
      name="add" gp-data-hidden="{% unless variant.available %}true{% endunless %}"
      data-state="idle"
      class="g4jUaJii-4 gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-button-atc tcustomizer-submit-button"
      style="--hvr-bg:#424242;--bg:#096de3;--radius:var(--g-radius-medium);--pl:24px;--pr:24px;--pt:12px;--pb:12px;--pl-tablet:24px;--pr-tablet:24px;--pt-tablet:12px;--pb-tablet:12px;--pl-mobile:24px;--pr-mobile:24px;--pt-mobile:12px;--pb-mobile:12px;--w:Auto;--w-tablet:Auto;--w-mobile:100%;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3);--size:16px;--size-tablet:16px;--size-mobile:14px;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--tt:uppercase"
    >
        <svg
          class="gp-invisible gp-absolute gp-h-5 gp-w-5 group-data-[state=loading]/button:gp-animate-spin group-data-[state=loading]/button:gp-visible"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="gp-opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          ></circle>
          <path
            class="gp-opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="group-hover/button:!gp-text-inherit gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:16px;--size-tablet:16px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.gg4jUaJii-4_label }}
      </span>
      </div>
    
    </button>
  </div>
  </gp-button>

        
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--bblr:5px;--bbrr:5px;--btlr:5px;--btrr:5px;--ta:left"
    class="{% if variant.available %} !gp-hidden {% endif %}"
  >
    <style>
    .g4jUaJii-4-sold-out.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      border-radius: var(--g-radius-medium);
    }

    .g4jUaJii-4-sold-out:hover::before {
      
      
    }

    .g4jUaJii-4-sold-out:hover .gp-button-icon {
      color: undefined;
    }

     .g4jUaJii-4-sold-out .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .g4jUaJii-4-sold-out:hover .gp-button-price {
      color: undefined;
    }

    .g4jUaJii-4-sold-out .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .g4jUaJii-4-sold-out .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .g4jUaJii-4-sold-out:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <button
      type="button" data-id="g4jUaJii-4" aria-label="Out of stock"
      gp-data-hidden="{% if variant.available %}true{% endif %}"
      data-state="idle"
      class="g4jUaJii-4-sold-out gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-button-sold-out btn-disabled gp-opacity-30 gp-cursor-default"
      style="--hvr-bg:#424242;--bg:#096de3;--radius:var(--g-radius-medium);--pl:24px;--pr:24px;--pt:12px;--pb:12px;--pl-tablet:24px;--pr-tablet:24px;--pt-tablet:12px;--pb-tablet:12px;--pl-mobile:24px;--pr-mobile:24px;--pt-mobile:12px;--pb-mobile:12px;--w:Auto;--w-tablet:Auto;--w-mobile:100%;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--c:var(--g-c-text-3, text-3);--size:16px;--size-tablet:16px;--size-mobile:14px;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--tt:uppercase"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="group-hover/button:!gp-text-inherit gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:16px;--size-tablet:16px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.gg4jUaJii-4_outOfStockLabel }}
      </span>
      </div>
    
    </button>
  </div>
  </gp-button>

    </gp-product-button>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-button.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
    </div>
   
    
    </div>
    </div>
   
    
          {%- endform -%}
        </product-form>
      </gp-product>
      {%- assign product = gpBkProduct -%}
    {% else %}
      <div class="gp-text-center">{{ "gempages.Product.product_not_found" | t }}</div>
    {%- endif -%}
     <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product.v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
      </div>
    </gp-sticky>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-sticky.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  


{% schema %}
  {
    
    "name": "Section 10",
    "tag": "section",
    "class": "gps-555417746817942394 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/gardpro-us/apps/gempages-cro/app/shopify/edit?pageType=GP_STATIC&editorId=555417746733532026&sectionId=555417746817942394)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"gg-Pire-v9O_customContent_prefix","label":"gg-Pire-v9O_customContent_prefix","default":"-"},{"type":"html","id":"gg-Pire-v9O_customContent_suffix","label":"gg-Pire-v9O_customContent_suffix","default":"off"},{"type":"html","id":"gg4jUaJii-4_label","label":"gg4jUaJii-4_label","default":"Add to Cart"},{"type":"html","id":"gg4jUaJii-4_outOfStockLabel","label":"gg4jUaJii-4_outOfStockLabel","default":"Out of stock"},{"type":"html","id":"gg4jUaJii-4_unavailableLabel","label":"gg4jUaJii-4_unavailableLabel","default":"Unavailable"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
