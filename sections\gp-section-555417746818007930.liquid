

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-555417746818007930.gps.gpsil [style*="--ai:"]{align-items:var(--ai)}.gps-555417746818007930.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-555417746818007930.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-555417746818007930.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-555417746818007930.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-555417746818007930.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-555417746818007930.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-555417746818007930.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-555417746818007930.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-555417746818007930.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-555417746818007930.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-555417746818007930.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-555417746818007930.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-555417746818007930.gps.gpsil [style*="--hvr-bb:"]:hover{border-bottom:var(--hvr-bb)}.gps-555417746818007930.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-555417746818007930.gps.gpsil [style*="--hvr-bc:"]:hover{border-color:var(--hvr-bc)}.gps-555417746818007930.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-555417746818007930.gps.gpsil [style*="--hvr-bblr:"]:hover{border-bottom-left-radius:var(--hvr-bblr)}.gps-555417746818007930.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-555417746818007930.gps.gpsil [style*="--hvr-bbrr:"]:hover{border-bottom-right-radius:var(--hvr-bbrr)}.gps-555417746818007930.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-555417746818007930.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-555417746818007930.gps.gpsil [style*="--hvr-bs:"]:hover{border-style:var(--hvr-bs)}.gps-555417746818007930.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-555417746818007930.gps.gpsil [style*="--hvr-bt:"]:hover{border-top:var(--hvr-bt)}.gps-555417746818007930.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-555417746818007930.gps.gpsil [style*="--hvr-btlr:"]:hover{border-top-left-radius:var(--hvr-btlr)}.gps-555417746818007930.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-555417746818007930.gps.gpsil [style*="--hvr-btrr:"]:hover{border-top-right-radius:var(--hvr-btrr)}.gps-555417746818007930.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-555417746818007930.gps.gpsil [style*="--hvr-bw:"]:hover{border-width:var(--hvr-bw)}.gps-555417746818007930.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-555417746818007930.gps.gpsil [style*="--hvr-shadow:"]:hover{box-shadow:var(--hvr-shadow)}.gps-555417746818007930.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-555417746818007930.gps.gpsil [style*="--hvr-c:"]:hover{color:var(--hvr-c)}.gps-555417746818007930.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-555417746818007930.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-555417746818007930.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-555417746818007930.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-555417746818007930.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-555417746818007930.gps.gpsil [style*="--gg:"]{grid-gap:var(--gg)}.gps-555417746818007930.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-555417746818007930.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-555417746818007930.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-555417746818007930.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-555417746818007930.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-555417746818007930.gps.gpsil [style*="--tdt:"]{text-decoration-thickness:var(--tdt)}.gps-555417746818007930.gps.gpsil [style*="--tdc:"]{text-decoration-color:var(--tdc)}.gps-555417746818007930.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-555417746818007930.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-555417746818007930.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-555417746818007930.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-555417746818007930.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-555417746818007930.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-555417746818007930.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-555417746818007930.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-555417746818007930.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-555417746818007930.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-555417746818007930.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-555417746818007930.gps.gpsil [style*="--pos:"]{position:var(--pos)}.gps-555417746818007930.gps.gpsil [style*="--right:"]{right:var(--right)}.gps-555417746818007930.gps.gpsil [style*="--rg:"]{row-gap:var(--rg)}.gps-555417746818007930.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-555417746818007930.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-555417746818007930.gps.gpsil [style*="--top:"]{top:var(--top)}.gps-555417746818007930.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-555417746818007930.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-555417746818007930.gps.gpsil [style*="--z:"]{z-index:var(--z)}.gps-555417746818007930.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-555417746818007930.gps.gpsil [style*="--aspect-tablet:"]{aspect-ratio:var(--aspect-tablet)}.gps-555417746818007930.gps.gpsil [style*="--cg-tablet:"]{-moz-column-gap:var(--cg-tablet);column-gap:var(--cg-tablet)}.gps-555417746818007930.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-555417746818007930.gps.gpsil [style*="--gtc-tablet:"]{grid-template-columns:var(--gtc-tablet)}.gps-555417746818007930.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-555417746818007930.gps.gpsil [style*="--jc-tablet:"]{justify-content:var(--jc-tablet)}.gps-555417746818007930.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-555417746818007930.gps.gpsil [style*="--objf-tablet:"]{-o-object-fit:var(--objf-tablet);object-fit:var(--objf-tablet)}.gps-555417746818007930.gps.gpsil [style*="--o-tablet:"]{order:var(--o-tablet)}.gps-555417746818007930.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-555417746818007930.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-555417746818007930.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-555417746818007930.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-555417746818007930.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-555417746818007930.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-555417746818007930.gps.gpsil [style*="--aspect-mobile:"]{aspect-ratio:var(--aspect-mobile)}.gps-555417746818007930.gps.gpsil [style*="--cg-mobile:"]{-moz-column-gap:var(--cg-mobile);column-gap:var(--cg-mobile)}.gps-555417746818007930.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-555417746818007930.gps.gpsil [style*="--gg-mobile:"]{grid-gap:var(--gg-mobile)}.gps-555417746818007930.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-555417746818007930.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-555417746818007930.gps.gpsil [style*="--jc-mobile:"]{justify-content:var(--jc-mobile)}.gps-555417746818007930.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-555417746818007930.gps.gpsil [style*="--objf-mobile:"]{-o-object-fit:var(--objf-mobile);object-fit:var(--objf-mobile)}.gps-555417746818007930.gps.gpsil [style*="--o-mobile:"]{order:var(--o-mobile)}.gps-555417746818007930.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-555417746818007930.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-555417746818007930.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-555417746818007930.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-555417746818007930.gps.gpsil [style*="--rg-mobile:"]{row-gap:var(--rg-mobile)}.gps-555417746818007930.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-555417746818007930.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-555417746818007930 .-gp-translate-x-1\/2,.gps-555417746818007930 .-gp-translate-y-1\/2,.gps-555417746818007930 .before\:-gp-rotate-45:before,.gps-555417746818007930 .gp-translate-x-\[-50\%\],.gps-555417746818007930 .gp-translate-y-0{--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1}.gps-555417746818007930 .gp-shadow-none{--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000}.gps-555417746818007930 .gp-g-paragraph-2{font-family:var(--g-p2-ff);font-size:var(--g-p2-size);font-style:var(--g-p2-fs);font-weight:var(--g-p2-weight);letter-spacing:var(--g-p2-ls);line-height:var(--g-p2-lh)}.gps-555417746818007930 .gp-sr-only{clip:rect(0,0,0,0);border-width:0;height:1px;margin:-1px;overflow:hidden;padding:0;position:absolute;white-space:nowrap;width:1px}.gps-555417746818007930 .gp-pointer-events-none{pointer-events:none}.gps-555417746818007930 .gp-invisible{visibility:hidden}.gps-555417746818007930 .gp-absolute{position:absolute}.gps-555417746818007930 .gp-relative{position:relative}.gps-555417746818007930 .gp-bottom-0{bottom:0}.gps-555417746818007930 .gp-bottom-\[-4px\]{bottom:-4px}.gps-555417746818007930 .gp-bottom-\[calc\(100\%\+20px\)\]{bottom:calc(100% + 20px)}.gps-555417746818007930 .gp-left-0{left:0}.gps-555417746818007930 .gp-left-1\/2,.gps-555417746818007930 .gp-left-\[50\%\]{left:50%}.gps-555417746818007930 .gp-right-0{right:0}.gps-555417746818007930 .gp-top-0{top:0}.gps-555417746818007930 .gp-top-1\/2{top:50%}.gps-555417746818007930 .gp-z-0{z-index:0}.gps-555417746818007930 .gp-z-1{z-index:1}.gps-555417746818007930 .gp-z-10{z-index:10}.gps-555417746818007930 .gp-z-\[90\]{z-index:90}.gps-555417746818007930 .\!gp-m-0{margin:0!important}.gps-555417746818007930 .\!gp-m-auto{margin:auto!important}.gps-555417746818007930 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-555417746818007930 .\!gp-ml-0{margin-left:0!important}.gps-555417746818007930 .gp-mb-0{margin-bottom:0}.gps-555417746818007930 .gp-mb-\[-10px\]{margin-bottom:-10px}.gps-555417746818007930 .gp-block{display:block}.gps-555417746818007930 .gp-flex{display:flex}.gps-555417746818007930 .gp-inline-flex{display:inline-flex}.gps-555417746818007930 .gp-grid{display:grid}.gps-555417746818007930 .gp-contents{display:contents}.gps-555417746818007930 .\!gp-hidden{display:none!important}.gps-555417746818007930 .gp-hidden{display:none}.gps-555417746818007930 .gp-aspect-\[56\/32\]{aspect-ratio:56/32}.gps-555417746818007930 .gp-h-0{height:0}.gps-555417746818007930 .gp-h-5{height:20px}.gps-555417746818007930 .gp-h-auto{height:auto}.gps-555417746818007930 .gp-h-full{height:100%}.gps-555417746818007930 .\!gp-w-full{width:100%!important}.gps-555417746818007930 .gp-w-14{width:56px}.gps-555417746818007930 .gp-w-5{width:20px}.gps-555417746818007930 .gp-w-auto{width:auto}.gps-555417746818007930 .gp-w-full{width:100%}.gps-555417746818007930 .gp-w-max{width:-moz-max-content;width:max-content}.gps-555417746818007930 .gp-min-w-fit{min-width:-moz-fit-content;min-width:fit-content}.gps-555417746818007930 .\!gp-max-w-\[150px\]{max-width:150px!important}.gps-555417746818007930 .\!gp-max-w-full{max-width:100%!important}.gps-555417746818007930 .\!gp-max-w-none{max-width:none!important}.gps-555417746818007930 .gp-max-w-full{max-width:100%}.gps-555417746818007930 .-gp-translate-x-1\/2{--tw-translate-x:-50%}.gps-555417746818007930 .-gp-translate-x-1\/2,.gps-555417746818007930 .-gp-translate-y-1\/2{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-555417746818007930 .-gp-translate-y-1\/2{--tw-translate-y:-50%}.gps-555417746818007930 .gp-translate-x-\[-50\%\]{--tw-translate-x:-50%}.gps-555417746818007930 .gp-translate-x-\[-50\%\],.gps-555417746818007930 .gp-translate-y-0{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-555417746818007930 .gp-translate-y-0{--tw-translate-y:0px}.gps-555417746818007930 .\!gp-cursor-not-allowed{cursor:not-allowed!important}.gps-555417746818007930 .gp-cursor-default{cursor:default}.gps-555417746818007930 .gp-cursor-pointer{cursor:pointer}.gps-555417746818007930 .gp-grid-flow-row{grid-auto-flow:row}.gps-555417746818007930 .gp-flex-col{flex-direction:column}.gps-555417746818007930 .gp-flex-wrap{flex-wrap:wrap}.gps-555417746818007930 .gp-items-start{align-items:flex-start}.gps-555417746818007930 .gp-items-end{align-items:flex-end}.gps-555417746818007930 .gp-items-center{align-items:center}.gps-555417746818007930 .gp-justify-start{justify-content:flex-start}.gps-555417746818007930 .gp-justify-center{justify-content:center}.gps-555417746818007930 .gp-gap-3{gap:12px}.gps-555417746818007930 .gp-gap-\[6px\]{gap:6px}.gps-555417746818007930 .gp-gap-y-0{row-gap:0}.gps-555417746818007930 .gp-overflow-hidden{overflow:hidden}.gps-555417746818007930 .gp-truncate{overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.gps-555417746818007930 .gp-text-ellipsis{text-overflow:ellipsis}.gps-555417746818007930 .gp-break-words{overflow-wrap:break-word}.gps-555417746818007930 .\!gp-rounded-none{border-radius:0!important}.gps-555417746818007930 .gp-rounded{border-radius:4px}.gps-555417746818007930 .gp-rounded-\[8px\]{border-radius:8px}.gps-555417746818007930 .gp-rounded-none{border-radius:0}.gps-555417746818007930 .gp-border-g-line-2{border-color:var(--g-c-line-2)}.gps-555417746818007930 .gp-bg-\[\#333333\]{--tw-bg-opacity:1;background-color:rgb(51 51 51/var(--tw-bg-opacity))}.gps-555417746818007930 .gp-bg-black\/80{background-color:rgba(0,0,0,.8)}.gps-555417746818007930 .gp-bg-g-bg-3{background-color:var(--g-c-bg-3)}.gps-555417746818007930 .gp-bg-white{--tw-bg-opacity:1;background-color:rgb(255 255 255/var(--tw-bg-opacity))}.gps-555417746818007930 .gp-bg-auto{background-size:auto}.gps-555417746818007930 .gp-object-cover{-o-object-fit:cover;object-fit:cover}.gps-555417746818007930 .gp-p-\[4px\]{padding:4px}.gps-555417746818007930 .gp-px-1{padding-left:4px;padding-right:4px}.gps-555417746818007930 .gp-px-\[8px\]{padding-left:8px;padding-right:8px}.gps-555417746818007930 .gp-py-\[4px\]{padding-bottom:4px;padding-top:4px}.gps-555417746818007930 .\!gp-pb-0{padding-bottom:0!important}.gps-555417746818007930 .gp-pl-4{padding-left:16px}.gps-555417746818007930 .gp-pr-6{padding-right:24px}.gps-555417746818007930 .gp-text-center{text-align:center}.gps-555417746818007930 .gp-text-\[12px\]{font-size:12px}.gps-555417746818007930 .gp-text-\[\#F9F9F9\]{--tw-text-opacity:1;color:rgb(249 249 249/var(--tw-text-opacity))}.gps-555417746818007930 .gp-text-g-text-2{color:var(--g-c-text-2)}.gps-555417746818007930 .gp-text-g-text-3{color:var(--g-c-text-3)}.gps-555417746818007930 .gp-text-white{--tw-text-opacity:1;color:rgb(255 255 255/var(--tw-text-opacity))}.gps-555417746818007930 .gp-line-through{text-decoration-line:line-through}.gps-555417746818007930 .gp-no-underline{text-decoration-line:none}.gps-555417746818007930 .gp-decoration-g-highlight{text-decoration-color:var(--g-c-highlight)}.gps-555417746818007930 .gp-opacity-0{opacity:0}.gps-555417746818007930 .gp-opacity-25{opacity:.25}.gps-555417746818007930 .gp-opacity-30{opacity:.3}.gps-555417746818007930 .gp-opacity-75{opacity:.75}.gps-555417746818007930 .gp-shadow-none{--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000;box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.gps-555417746818007930 .gp-outline-none{outline:2px solid transparent;outline-offset:2px}.gps-555417746818007930 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555417746818007930 .gp-transition-opacity{transition-duration:.15s;transition-property:opacity;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555417746818007930 .gp-duration-200{transition-duration:.2s}.gps-555417746818007930 .gp-duration-300{transition-duration:.3s}.gps-555417746818007930 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555417746818007930 .disabled\:gp-btn-disabled:disabled{cursor:default}.gps-555417746818007930 .before\:gp-absolute:before{content:var(--tw-content);position:absolute}.gps-555417746818007930 .before\:gp-top-\[50\%\]:before{content:var(--tw-content);top:50%}.gps-555417746818007930 .before\:gp-z-1:before{content:var(--tw-content);z-index:1}.gps-555417746818007930 .before\:gp-hidden:before{content:var(--tw-content);display:none}.gps-555417746818007930 .before\:gp-w-full:before{content:var(--tw-content);width:100%}.gps-555417746818007930 .before\:-gp-rotate-45:before{--tw-rotate:-45deg;content:var(--tw-content);transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-555417746818007930 .before\:gp-border-t:before{border-top-width:1px;content:var(--tw-content)}.gps-555417746818007930 .before\:gp-content-\[\'\'\]:before{--tw-content:"";content:var(--tw-content)}.gps-555417746818007930 .after\:gp-absolute:after{content:var(--tw-content);position:absolute}.gps-555417746818007930 .after\:gp-bottom-\[-10px\]:after{bottom:-10px;content:var(--tw-content)}.gps-555417746818007930 .after\:gp-left-0:after{content:var(--tw-content);left:0}.gps-555417746818007930 .after\:gp-w-full:after{content:var(--tw-content);width:100%}.gps-555417746818007930 .after\:gp-p-\[7px\]:after{content:var(--tw-content);padding:7px}.gps-555417746818007930 .after\:gp-content-\[\'\'\]:after{--tw-content:"";content:var(--tw-content)}@media (hover:hover) and (pointer:fine){.gps-555417746818007930 .hover\:gp-border-g-line-3:hover{border-color:var(--g-c-line-3)}.gps-555417746818007930 .hover\:gp-bg-\[\#ef0800\]:hover{--tw-bg-opacity:1;background-color:rgb(239 8 0/var(--tw-bg-opacity))}.gps-555417746818007930 .hover\:gp-bg-g-bg-3:hover{background-color:var(--g-c-bg-3)}.gps-555417746818007930 .hover\:gp-text-g-text-2:hover{color:var(--g-c-text-2)}}.gps-555417746818007930 .active\:gp-bg-g-bg-3:active{background-color:var(--g-c-bg-3)}.gps-555417746818007930 .active\:gp-text-g-text-2:active{color:var(--g-c-text-2)}.gps-555417746818007930 .disabled\:gp-pointer-events-none:disabled{pointer-events:none}.gps-555417746818007930 .disabled\:gp-opacity-30:disabled{opacity:.3}@media (hover:hover) and (pointer:fine){.gps-555417746818007930 .gp-group:hover .group-hover\:gp-visible{visibility:visible}.gps-555417746818007930 .gp-group\/button:hover .group-hover\/button\:\!gp-text-inherit{color:inherit!important}.gps-555417746818007930 .gp-group:hover .group-hover\:gp-opacity-0{opacity:0}}.gps-555417746818007930 .data-\[disabled\=\'disabled\'\]\:gp-pointer-events-none[data-disabled=disabled]{pointer-events:none}.gps-555417746818007930 .data-\[hidden\=\'false\'\]\:gp-flex[data-hidden=false]{display:flex}.gps-555417746818007930 .data-\[disabled\=true\]\:gp-hidden[data-disabled=true],.gps-555417746818007930 .data-\[hidden\=true\]\:gp-hidden[data-hidden=true]{display:none}.gps-555417746818007930 .data-\[disabled\=\'disabled\'\]\:\!gp-cursor-not-allowed[data-disabled=disabled]{cursor:not-allowed!important}.gps-555417746818007930 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-visible{visibility:visible}.gps-555417746818007930 .gp-group[data-state=loading] .group-data-\[state\=loading\]\:gp-invisible,.gps-555417746818007930 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-invisible{visibility:hidden}@keyframes gp-spin{to{transform:rotate(1turn)}}.gps-555417746818007930 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-animate-spin{animation:gp-spin 1s linear infinite}@media (max-width:1024px){.gps-555417746818007930 .tablet\:\!gp-hidden{display:none!important}.gps-555417746818007930 .tablet\:gp-hidden{display:none}}@media (max-width:767px){.gps-555417746818007930 .mobile\:\!gp-hidden{display:none!important}.gps-555417746818007930 .mobile\:gp-hidden{display:none}.gps-555417746818007930 .mobile\:\!gp-max-w-\[0px\]{max-width:0!important}.gps-555417746818007930 .mobile\:gp-overflow-hidden{overflow:hidden}.gps-555417746818007930 .mobile\:gp-px-\[0px\]{padding-left:0;padding-right:0}.gps-555417746818007930 .mobile\:after\:gp-p-\[0px\]:after{content:var(--tw-content);padding:0}}.gps-555417746818007930 .\[\&\>\*\>div\:nth-child\(1\)\]\:gp-contents>*>div:first-child{display:contents}.gps-555417746818007930 .\[\&\>div\]\:gp-grid>div{display:grid}.gps-555417746818007930 .\[\&\>div\]\:gp-grid-rows-\[subgrid\]>div{grid-template-rows:subgrid}.gps-555417746818007930 .\[\&\>div\]\:gp-gap-y-0>div{row-gap:0}.gps-555417746818007930 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-555417746818007930 .\[\&_p\]\:gp-inline p{display:inline}.gps-555417746818007930 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}.gps-555417746818007930 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-555417746818007930 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      enableLazyloadImage="true" id="gc4cTS57DT" data-id="gc4cTS57DT"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--pt:var(--g-s-4xl);--pl:15px;--pb:var(--g-s-4xl);--pr:15px;--pt-tablet:70px;--pl-tablet:15px;--pb-tablet:70px;--pr-tablet:15px;--cg:30px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gc4cTS57DT gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gmttO9AyML gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="g6VEn-N2Ie" data-id="g6VEn-N2Ie"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--cg:var(--g-s-2xl);--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g6VEn-N2Ie gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g3qYGoncHd gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small)" class="gY_J60V8b4 ">
      

    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product.v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-same-height.v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    
{%- assign gpBkProduct = product -%}
{%- assign gpBkProducts = products -%}
{%- liquid
      assign productHandles = 'gard-pro-health-smartwatch-3-1,gard-pro-ultra-2-plus2,gard-pro-ultra,gard-pro-health-smartwatch-2-plus'| split: ','
      assign products = null
      assign target_collection = null
      assign productSrc = 'PickProduct'
      assign c = 0
      assign limit = 4
      if request.page_type == 'collection' or preview_page_type == 'collection'
        assign target_collection = collection
        if target_collection == empty or target_collection == null
          if 'latest' == 'latest'
            assign target_collection = collections | sort: 'updated_at'  | first
          else
            for collection in collections
              if collection.id == Latest
                assign target_collection = collection
                break
              endif
            endfor
          endif
        endif
        paginate target_collection.products by 4
          assign products = target_collection.products  
        endpaginate
      else
        if productSrc == 'RelatedProduct'
          
    
    
        elsif productSrc == 'PickProduct'
          unless false 
            assign products = null | sort
            for handle in productHandles
              assign productH = all_products[handle] | sort
              assign products = products | concat: productH
            endfor
            assign limit = products.length
          else
            if 4 == 0
              paginate collections.all.products by 100000
                assign products = collections.all.products  | sort : "created_at"  | reverse
              endpaginate
            endif
          endunless
        else
          if 'latest' == 'latest'
            assign target_collection = collections | sort: 'updated_at'  | first
          else
            for collection in collections
              if collection.id == Latest
                assign target_collection = collection
                break
              endif
            endfor
          endif
          paginate target_collection.products by 4
            assign products = target_collection.products  
          endpaginate
        endif
      endif
      -%}
        
    {%- if products == null -%}
    {%- if count == 0 and 4 == 0 -%}
      {% if 'PickProduct' == 'RelatedProduct' -%}
          <div>{{ "gempages.ProductList.no_related_products" | t }}</div>
        {% else %}
          <div>{{ "gempages.ProductList.no_products_found" | t }}</div>
      {%- endif -%}
    {%- endif -%}
    {%-else-%}
      
      
    <div items-repeat data-id="gY_J60V8b4" style="--w:100%;--w-tablet:100%;--w-mobile:100%;--cg:30px;--cg-tablet:50px;--cg-mobile:24px;--gtc:repeat(4, minmax(0, 1fr));--gtc-tablet:repeat(2, minmax(0, 1fr));--gtc-mobile:repeat(1, minmax(0, 1fr))"
          class="gp-grid !gp-m-auto gp-w-full  gp-grid-flow-row"
      >
        
      <gp-same-height-v2
        class="gp-contents"
        style="visibility: hidden"
        gp-data='{"targetUid":"gTu0xsQN1g","slidesToShow":{"desktop":4,"mobile":1,"tablet":2},"rowGap":{"desktop":"30px","mobile":"24px","tablet":"30px"}}'>
        
        {%- assign count = 0 -%}
        {%- for product in products limit: limit -%}
        {%- if product.id != empty -%}
        {%- assign count = count | plus: 1 -%}
        {%- assign variant = product.selected_or_first_available_variant -%}
        {%- assign product_form_id = 'product-form-' | append: "gY_J60V8b4" | append: product.id -%}
            <gp-product 
              class="gp-child-item-gY_J60V8b4  gp-contents" 
              gp-context='{"variantSelected": {{ variant | json | escape }}, "quantity": 1 ,  "formId": "{{ product_form_id }}"}' 
              gp-data='{"variantSelected": {{ variant | json | escape }}, "quantity": 1, "productUrl":{{ product.url | json | escape }}, "productHandle":{{ product.handle | json | escape }}, "collectionUrl": {{ collection.url | json | escape }}, "collectionHandle": {{ collection.handle | json | escape }},"pageContext" : {"numberOfProductOfProductList":0,"isPreviewing":false,"pageType":"GP_STATIC","hasCollectionHandle":true,"isTranslateWithLocale":false,"sectionName":"gp-section-555417746818007930"}}'
            >
              {%- form 'product', product, id: product_form_id, class: 'form contents  gp-contents [&>div]:gp-grid [&>div]:gp-grid-rows-[subgrid] [&>div]:gp-gap-y-0 [&>div]:gp-grid-col-[subgrid] [&>*>div:nth-child(1)]:gp-contents',  data-type: 'add-to-cart-form', autocomplete: 'off' -%}
              <input type="hidden" name="id" value="{{ variant.id }}" />
              <input type="hidden" min="1" name="quantity"  value="{{ quantity }}" />
              <button type="submit" onclick="return false;" style="display:none;"></button>
                      
       
      
    <div
      enableLazyloadImage="true" parentTag="ProductList" id="gTu0xsQN1g" data-id="gTu0xsQN1g"
        style="--bs:none;--bw:0px 0px 0px 0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:0px;--pl:0px;--pb:0px;--pr:0px;--cg:0px;--pc:start;--gtc:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gTu0xsQN1g gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:start;--o:0"
      class="gHYO9GxR1W gp-relative gp-flex gp-flex-col"
    >
      <div
      enableLazyloadImage="true"
      style="--ai:normal;--jc:normal;--o:0"
      class="g6xmokmCom gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="gs1EaYsHxt" data-id="gs1EaYsHxt"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pos:relative;--radius:var(--g-radius-small);--mb:var(--g-s-l);--pt:0px;--pb:0px;--cg:30px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gs1EaYsHxt gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gkT6Hs0ySO gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--pos:absolute;--right:8px;--radius:var(--g-radius-small);--top:8px;--z:99" class="gM4K72-wOE ">
      
  {% liquid
    assign price = variant.price | times: 1.0
    assign salePrice = variant.compare_at_price | times: 1.0
    assign priceSave = salePrice | minus: price
    assign prefixVal = 
    assign suffixVal = section.settings.ggM4K72-wOE_customContent_suffix
    if salePrice == null or price == null
      assign pricePercentage = prefixVal | append: ' 0% ' | append: suffixVal
    else
         assign salePercent = priceSave | divided_by: salePrice | times: 100  | round
                assign pricePercentage = prefixVal | append: ' ' | append: salePercent | append: '% ' | append: suffixVal
              
    endif
  %}

  <gp-product-tag
  data-id="gM4K72-wOE"
    data-disabled="{%- if priceSave > 0 -%} false {%- else -%} true {%- endif -%}"
    gp-data='{"setting":{"customContent":{"prefix":"","suffix":"off","unit":"percentage"},"translate":"customContent"}, "id": "gM4K72-wOE", "locale": "{{shop.locale}}", "currency": "{{shop.currency}}", "moneyFormat": "{{ shop.money_format | replace: '"', '\"' | escape }}"}'
    class="gp-block data-[disabled=true]:gp-hidden "
    style="--ta:left"
    price-save="{{priceSave}}"
    data-prefix="{{}}"
    data-suffix="{{section.settings.ggM4K72-wOE_customContent_suffix}}"
  >
     <div class="gp-inline-flex gp-flex-wrap gp-items-end gp-gap-3" >
       <div
         class="gp-flex gp-items-center gp-w-full gp-h-full gp-bg-g-bg-3"
         style="--pl:16px;--pr:16px;--pt:16px;--pb:16px;--pl-tablet:16px;--pr-tablet:16px;--pt-tablet:16px;--pb-tablet:16px;--pl-mobile:16px;--pr-mobile:16px;--pt-mobile:16px;--pb-mobile:16px;--bs:solid;--bw:1px 1px 1px 1px;--bc:#EEEEEE;--c:#FB4606;--radius:var(--g-radius-large)"
       >
         
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      id="p-tag-gM4K72-wOE"
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A']"
          style="--w:100%;--ta:left;--c:#FB4606;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:13px;--size-tablet:13px;--size-mobile:12px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;overflow:hidden"
        >{{pricePercentage}}</div>
      </div>
    </div>
    </gp-text>
    
       </div>
     </div>

 </gp-product-tag>
 <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-tag.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
   
      </div>
  {% assign featured_image = product.featured_image %}
  {%- if variant != null and variant.featured_image != null -%}
  {% assign featured_image = variant.featured_image %}
  {%- endif -%}
    <gp-product-images-v2
      gp-data='
    {
      "id":"gcBUliND2L",
      "pageContext": {"numberOfProductOfProductList":0,"isPreviewing":false,"pageType":"GP_STATIC","hasCollectionHandle":true,"isTranslateWithLocale":false,"sectionName":"gp-section-555417746818007930"},
      "setting":{"arrowIcon":"","borderActive":{"borderType":"none","borderWidth":"1px","color":"#000000","isCustom":"false","width":"1px 1px 1px 1px"},"clickOpenLightBox":{"desktop":false,"mobile":false,"tablet":false},"dragToScroll":true,"ftArrowIcon":"<svg width=\"100%\" height=\"100%\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M12 0C18.6274 0 24 5.37258 24 12C24 18.6274 18.6274 24 12 24C5.37258 24 0 18.6274 0 12C0 5.37258 5.37258 0 12 0ZM10.6828 8.13017C10.5266 7.95661 10.2734 7.95661 10.1172 8.13017C9.96095 8.30374 9.96095 8.58515 10.1172 8.75871L13.0343 12L10.1172 15.2413C9.96095 15.4149 9.96095 15.6963 10.1172 15.8698C10.2734 16.0434 10.5266 16.0434 10.6828 15.8698L13.8828 12.3143C14.0391 12.1407 14.0391 11.8593 13.8828 11.6857L10.6828 8.13017Z\" fill=\"currentColor\"/>\n    </svg>","ftArrowIconColor":"#000000","ftClickOpenLightBox":{"desktop":"product-link"},"ftDotActiveColor":{"desktop":"line-3"},"ftDotColor":{"desktop":"bg-1"},"ftDotGapToCarousel":{"desktop":16},"ftDotSize":{"desktop":12},"ftDotStyle":{"desktop":"none"},"ftNavigationPosition":{"desktop":"none","mobile":"none","tablet":"none"},"galleryClickEffect":"preview","hoverEffect":"other","loop":{"desktop":true,"mobile":true,"tablet":true},"navigationPosition":{"desktop":"inside","mobile":"inside","tablet":"inside"},"otherImage":1,"pauseOnHover":true,"speed":1,"type":{"desktop":"images","mobile":"images","tablet":"images"},"typeDisplay":"all-images","zoom":1.5,"zoomType":"default"},
      "styles":{"align":{"desktop":"center","mobile":"flex-start","tablet":"flex-start"},"aspectHeight":{"desktop":0,"mobile":0,"tablet":0},"aspectWidth":{"desktop":0,"mobile":0,"tablet":0},"dotActiveColor":{"desktop":"brand"},"dotColor":{"desktop":"bg-1"},"ftAspectHeight":{"desktop":0,"mobile":0,"tablet":0},"ftAspectWidth":{"desktop":0,"mobile":0,"tablet":0},"ftLayout":{"desktop":"cover","mobile":"cover","tablet":"cover"},"ftShape":{"desktop":{"shape":"original","shapeLinked":true,"width":"100%"},"mobile":{"shape":"original","shapeLinked":true,"width":"100%"},"tablet":{"shape":"original","shapeLinked":true,"width":"100%"}},"height":{"desktop":"100px","mobile":"auto","tablet":"auto"},"itemSpacing":{"desktop":"5px","mobile":"var(--g-s-s)"},"layout":{"desktop":"cover","mobile":"cover","tablet":"cover"},"position":{"desktop":"only-feature","mobile":"only-feature","tablet":"only-feature"},"ratioLayout":{"desktop":{},"mobile":{},"tablet":{}},"ratioLayoutRight":{"desktop":{},"mobile":{},"tablet":{}},"shape":{"desktop":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"},"mobile":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"tablet":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"}},"shapeFor1Col":{"desktop":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"100%"},"mobile":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"tablet":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"}},"shapeFor2Col":{"desktop":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"50%"},"mobile":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"tablet":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"}},"shapeForBottom":{"desktop":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1","width":"20%"},"mobile":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"tablet":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"}},"shapeForFtOnly":{"desktop":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"mobile":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"},"tablet":{"height":"","shape":"square","shapeLinked":true,"shapeValue":"1/1"}},"spacing":{"desktop":"5px","mobile":"var(--g-s-s)"},"verticalLayout":{"desktop":false}}, "productUrl":{{product.url | json | escape}}, "product":{{product | json | escape}}, "collectionUrl": {{ collection.url | json | escape }}, "collection": {{ collection | json | escape}}
    }
  '
      section-id="{{section.id}}"
      style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:0px"
      data-id="gcBUliND2L"
      class="gcBUliND2L gp-overflow-hidden"
    >
      <div
        class="{%- if product.media.size > 1 -%} gp-grid gp-w-full !gp-m-0 gp-relative {%- else -%} gp-w-full !gp-m-0 gp-relative {%- endif -%}"
        {%- if product.media.size > 1 -%}
        style="--gtc:minmax(0, 12fr);--gtc-tablet:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--gg:5px;--gg-mobile:var(--g-s-s)"
        {%- else -%}
        style="--gtc:minmax(0, 12fr);--gg:5px;--gg-mobile:var(--g-s-s)"
        {%- endif -%}
      >
        
    {% capture featureImageOnlyOne %}
      
        {% assign featureMedia = variant.featured_media %}
        {% unless featureMedia %}
        {% assign featureMedia = product.featured_media %}
        {% endunless %}
        {% if product.media.size > 1 %}
          
          {% assign featureMedia = variant.featured_media %}
          {% unless featureMedia %}
          {% assign featureMedia = product.featured_media %}
          {% endunless %}
        
        {% endif %}
        {% assign largestRatio = 0 %}
        {% assign height = featureMedia.height | times: 1.0 %}
        {% assign width = featureMedia.width | times: 1.0 %}
        {% assign ratio = height | divided_by: width %}
        {% if ratio > largestRatio %}
          {% assign largestRatio = ratio %}
        {% endif %}
        {% assign productImageWidth = 0 %}
        {% case featureMedia.media_type %}
          {% when 'image' %}
            {% assign productImageWidth = featureMedia.width %}
          {% else %}
            {% assign productImageWidth = featureMedia.preview_image.width %}
        {% endcase %}
        {% if featureMedia == null %}
        {% assign productImageWidth = 1600 %}
        {% endif %}
        <div 
          class='gp-feature-image-carousel gp-feature-image-only' 
          style="--o:0;--o-tablet:0;--o-mobile:0;--d:flex;--d-mobile:flex;--d-tablet:flex;--jc:center;--jc-tablet:flex-start;--jc-mobile:flex-start"
        >
          <div 
            class="gp-relative"
            style="--w:100%;--w-tablet:100%;--w-mobile:100%"
          >
            
      
    
            <div 
              type="gp-feature-image-only"
              product-id="{{product.id}}"
              product-media="{{product.media.size}}"
              class="gp-image-item gp-ft-image-item gp-group gp-z-0 gp-flex !gp-max-w-full !gp-w-full gp-relative gp-items-start gp-justify-center gp-overflow-hidden gp-featured-image-wrapper"
              style="--h:auto;--h-tablet:auto;--h-mobile:auto;width:{{productImageWidth}}px"
            >
              <div 
                class="gp-w-full  {% if featureMedia == null or featureMedia.media_type == 'image' %} {{ 'gp-h-0' }} {% else %} {{ 'gp-h-full !gp-pb-0' }} {% endif %} gp-relative" 
                style="--pb: calc(({%if largestRatio == 0%} 100 / 100 {%else%} {{largestRatio}} {%endif%})*100%);--pb-tablet: calc(({%if largestRatio == 0%} 100 / 100 {%else%} {{largestRatio}} {%endif%})*100%);--pb-mobile: calc(({%if largestRatio == 0%} 100 / 100 {%else%} {{largestRatio}} {%endif%})*100%); {% if featureMedia.media_type == 'video' or featureMedia.media_type == 'external_video' %} {{ 'display: flex; align-items: center; justify-content: center' }} {% endif %}"
              >
                
    {% case featureMedia.media_type %}
      {% when 'image' %}
        
      
    {% assign src = featureMedia.src %}
    
    
  <img
      id="{%- if featureMedia != null -%}
              {{featureMedia.id}}
            {%- endif -%}"
      
      draggable="false"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none gp-w-full gp-transition-opacity group-hover:gp-opacity-0 {{shouldHidden}} gp_lazyload"
      data-src="{{src | image_url}}" data-srcset="{%- if src != null -%}
              {{src | img_url: '480x480'}} 480w, {{src | img_url: '768x768'}} 768w,{{src | img_url: '1024x1024'}} 1024w,{{src | img_url: '1440x1440'}} 1440w
            {%- else -%}
              {{ 'https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif' }}
            {%- endif -%}" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDxkZWZzPgogICAgICA8bGluZWFyR3JhZGllbnQgaWQ9Imcte3tmZWF0dXJlTWVkaWEud2lkdGh9fS17e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSI+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI1MCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI3MCUiIC8+CiAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICA8L2RlZnM+CiAgICA8cmVjdCB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgZmlsbD0idXJsKCNnLXt7ZmVhdHVyZU1lZGlhLndpZHRofX0te3tmZWF0dXJlTWVkaWEuaGVpZ2h0fX0pIiAvPgogICAgPGFuaW1hdGUgeGxpbms6aHJlZj0iI3IiIGF0dHJpYnV0ZU5hbWU9IngiIGZyb209Ii17e2ZlYXR1cmVNZWRpYS53aWR0aH19IiB0bz0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPgogIDwvc3ZnPg==" width="{{featureMedia.width}}" height="{{featureMedia.height}}" alt="{{featureMedia.alt}}" base-src="{{src | image_url}}"
      quality-type={}
      quality-percent={}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:{{featureMedia.width}}/{{featureMedia.height}};--aspect-tablet:{{featureMedia.width}}/{{featureMedia.height}};--aspect-mobile:{{featureMedia.width}}/{{featureMedia.height}};--objf:cover;--objf-tablet:cover;--objf-mobile:cover"
    />
  
      <div class="zoom-element !gp-max-w-none gp-absolute gp-left-0 gp-top-0 gp-opacity-0 gp-transition-opacity gp-bg-white gp-pointer-events-none">
            
    {% assign src = featureMedia.src %}
    
      {% assign otherImage = product.media[1] %}
      {% assign src = otherImage.src %}
      {% if otherImage.media_type != 'image' %}
        {% assign src = otherImage.preview_image.src %}
      {%- endif -%}
    
    
  <img
      id="{%- if featureMedia != null -%}
              {{featureMedia.id}}
            {%- endif -%}"
      
      draggable="false"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none image-zoom gp_lazyload"
      data-src="{{src | image_url}}" data-srcset="{%- if src != null -%}
              {{src | img_url: '480x480'}} 480w, {{src | img_url: '768x768'}} 768w,{{src | img_url: '1024x1024'}} 1024w,{{src | img_url: '1440x1440'}} 1440w
            {%- else -%}
              {{ 'https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif' }}
            {%- endif -%}" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHhtbG5zOnhsaW5rPSJodHRwOi8vd3d3LnczLm9yZy8xOTk5L3hsaW5rIj4KICAgIDxkZWZzPgogICAgICA8bGluZWFyR3JhZGllbnQgaWQ9Imcte3tmZWF0dXJlTWVkaWEud2lkdGh9fS17e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSI+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSIyMCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI1MCUiIC8+CiAgICAgICAgPHN0b3Agc3RvcC1jb2xvcj0icmdiYSg1MSwgNTEsIDUxLCAwKSIgb2Zmc2V0PSI3MCUiIC8+CiAgICAgIDwvbGluZWFyR3JhZGllbnQ+CiAgICA8L2RlZnM+CiAgICA8cmVjdCB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgaGVpZ2h0PSJ7e2ZlYXR1cmVNZWRpYS5oZWlnaHR9fSIgZmlsbD0idXJsKCNnLXt7ZmVhdHVyZU1lZGlhLndpZHRofX0te3tmZWF0dXJlTWVkaWEuaGVpZ2h0fX0pIiAvPgogICAgPGFuaW1hdGUgeGxpbms6aHJlZj0iI3IiIGF0dHJpYnV0ZU5hbWU9IngiIGZyb209Ii17e2ZlYXR1cmVNZWRpYS53aWR0aH19IiB0bz0ie3tmZWF0dXJlTWVkaWEud2lkdGh9fSIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPgogIDwvc3ZnPg==" width="{{featureMedia.width}}" height="{{featureMedia.height}}" alt="{{featureMedia.alt}}" base-src="{{src | image_url}}"
      quality-type={}
      quality-percent={}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:{{featureMedia.width}}/{{featureMedia.height}};--aspect-tablet:{{featureMedia.width}}/{{featureMedia.height}};--aspect-mobile:{{featureMedia.width}}/{{featureMedia.height}};--objf:cover;--objf-tablet:cover;--objf-mobile:cover"
    />
  
          </div>
      
    
      {% when 'external_video' %}
        {% assign mediaSourceVideo = featureMedia | external_video_url %}
        
    <iframe
      width="100%" height="100%"
      class="feature-video gp-w-full gp-h-full gp-relative"
      src="{{mediaSourceVideo}}"
      controls="true"
      allowfullscreen="true"
      allow="accelerometer; autoplay; encrypted-media; gyroscope; picture-in-picture"
      frameborder="0"
      autoplay="false"
      alt="{{featureMedia.alt}}"
      style="--aspect:;--aspect-tablet:;--aspect-mobile:"
    >
    </iframe>
  
      {% when 'video' %}
        {% assign mediaSourceVideo = featureMedia.sources.last.url %}
        
  <gp-lite-html5-embed>
    
    <video
      id="gp-video-undefined"
      class=" gp-w-full lazy"
      style="width:100%;max-height:100%"
      controls
      
      
      
      title="{{featureMedia.alt}}"
      preload="none"
      data-src="{{mediaSourceVideo}}"
      src=""
      poster=""
      playsinline
    >
    
  </video>
    <div
          style="width:100%;max-height:100%"
           class="gp-absolute gp-top-0 gp-left-0  gp-w-full gp-thumbnail-video gp-hidden"
        >
          <img
            id="video-thumbnail"
            src=""
            class="gp-w-full gp-h-full gp-object-cover"
            alt="Video Thumbnail"
          ></img>
          <button
            type="button"
            class="gp-absolute gp-left-1/2 gp-top-1/2 gp-flex gp-aspect-[56/32] gp-w-14 -gp-translate-x-1/2 -gp-translate-y-1/2 gp-items-center gp-justify-center gp-rounded gp-bg-black/80 gp-transition-colors hover:gp-bg-[#ef0800]"
            aria-label="Play"
          >
            <svg class="gp-w-5 gp-text-white" viewBox="0 0 24 24">
              <path
                fill="currentColor"
                d="M5 5.274c0-1.707 1.826-2.792 3.325-1.977l12.362 6.726c1.566.853 1.566 3.101 0 3.953L8.325 20.702C6.826 21.518 5 20.432 5 18.726V5.274Z"
              />
            </svg>
          </button>
        </div>
        </gp-lite-html5-embed>
        <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-lite-html5-embed.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
    <script src="https://cdn.jsdelivr.net/npm/vanilla-lazyload@17.8.3/dist/lazyload.min.js"></script>
    <script>
        if(lazyLoadInstance){lazyLoadInstance.update()}else{var lazyLoadInstance = new LazyLoad()}
    </script>
  
      {% when 'model' %}
        
    <model-viewer
      class="gp-w-full gp-h-full model-viewer gp-z-[90]"
      width="100%"
      
      
      
      
      src="{% if featureMedia.sources.first.url contains '.glb' %}{{ featureMedia.sources.first.url }}{% else %}{{featureMedia.sources.last.url}}{% endif %}"
      alt="{{featureMedia.preview_image.alt}}"
      camera-controls="true"
      poster="{{featureMedia.preview_image.src | product_img_url: '1024x1024'}}"
      data-js-focus-visible=""
      data-shopify-feature="1.12"
      ar-status="not-presenting"
      style="--aspect:1/1;--aspect-tablet:1/1;--aspect-mobile:1/1">
    </model-viewer>
  
      {% else %}
        
    
  <img
      
      
      draggable="false"
      class="gp-w-full gp-h-full gp-absolute gp-top-0 gp-left-0 featured-image-only gp-cursor-pointer !gp-rounded-none gp_lazyload"
      data-src data-srcset="https://cdn.shopify.com/s/assets/no-image-2048-5e88c1b20e087fb7bbe9a3771824e743c244f437e4f8ba93bbf7b11b53f7824c_large.gif" src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0yMjM3LTE2NzgiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjIyMzciIGhlaWdodD0iMTY3OCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMjIzNyIgaGVpZ2h0PSIxNjc4IiBmaWxsPSJ1cmwoI2ctMjIzNy0xNjc4KSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMjIzNyIgdG89IjIyMzciIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" width="2237" height="1678" alt="No Image"
      quality-type={}
      quality-percent={}
      data-sizes="auto"
      sizes="100vw"
      style="--aspect:2237/1678;--aspect-tablet:2237/1678;--aspect-mobile:2237/1678;--objf:cover;--objf-tablet:cover;--objf-mobile:cover"
    />
  
    {% endcase %}
    
              </div>
            </div>
          </div>
        </div>
      
    {% endcapture %}
    {% if product.media.size > 1 %}
      
      {{ featureImageOnlyOne }}
    {% else %}
      {{ featureImageOnlyOne }}
    {% endif %}
  
         
      </div>
    </gp-product-images-v2>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-images-v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
    </div>
   
    <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:var(--g-s-xs);--pr:0px" class="gKkVzeaZJF ">
      
    {%- unless product -%}
      <p>{{ "gempages.ProductTitle.product_not_found" | t }}</p>
    {%- else -%}
      
        <a href="{{ product.url }}" title="{{ product.title }}" class="gp-product-title-link-wrapper">
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gKkVzeaZJF">
    <div
      
        class="gKkVzeaZJF "
        style="--tt:default"
      >
      <div  >
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A']"
          style="--w:100%;--ta:left;--line-clamp:2;--line-clamp-tablet:2;--line-clamp-mobile:0;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:16px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--tt:none;word-break:break-word;overflow:hidden"
        >{{ product.title }}</h2>
      </div>
    </div>
    </gp-text>
    
        </a>
      
    {%- endunless -%}
  
      </div>
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="g8sp7YaWFL" data-id="g8sp7YaWFL"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--mb:var(--g-s-l);--cg:8px;--pc:start;--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, auto) minmax(0, auto);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g8sp7YaWFL gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gx8oGfL9jc gp-relative gp-flex gp-flex-col"
    >
      
      <gp-product-price
        data-id="gr5OP6jZJD"
        class="gr5OP6jZJD gp-product-price group-data-[state=loading]:gp-invisible data-[hidden=true]:gp-hidden"
        gp-data='{"priceType":"regular","uid":"gr5OP6jZJD","locale":"{{shop.locale}}","currency":"{{shop.currency}}","moneyFormat":"{{ shop.money_format | replace: '"', '\\"' | escape }}"}'
        
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small)"
        data-hidden="false"
      >
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      id="p-price-gr5OP6jZJD"
        class=" "
        
      >
      <div  >
        <div
          type="regular"
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-price gp-money gp-decoration-g-highlight"
          style="--w:100%;--tdc:highlight;--tdt:1;--ta:left;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;overflow:hidden"
        >
     {% if variant.price %} 
       {{ variant.price | money}}
     {% endif %}
   </div>
      </div>
    </div>
    </gp-text>
    
      </gp-product-price>
      <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-price.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div><div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:center"
      class="guPkenUQMp gp-relative gp-flex gp-flex-col"
    >
      
      <gp-product-price
        data-id="gorngnNGPu"
        class="gorngnNGPu gp-product-price group-data-[state=loading]:gp-invisible data-[hidden=true]:gp-hidden"
        gp-data='{"priceType":"compare","uid":"gorngnNGPu","locale":"{{shop.locale}}","currency":"{{shop.currency}}","moneyFormat":"{{ shop.money_format | replace: '"', '\\"' | escape }}"}'
        
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small)"
        data-hidden="{% if variant.compare_at_price > variant.price and variant.compare_at_price >= 0 %}false{% else %}true{% endif %}"
      >
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      id="p-price-gorngnNGPu"
        class=" "
        
      >
      <div  >
        <div
          type="compare"
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-price gp-product-compare-price gp-line-through"
          style="--w:100%;--tdc:#B4B4B4;--tdt:1;--ta:left;--c:#B4B4B4;--fs:normal;--ff:var(--g-font-heading, heading);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;overflow:hidden"
        >
      {% if variant.compare_at_price  %} 
        {{ variant.compare_at_price | money}}
      {% else %}
        
      {% endif %}
    </div>
      </div>
    </div>
    </gp-text>
    
      </gp-product-price>
      <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-price.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
    </div>
   
    <div gp-el-wrapper style="--mb:var(--g-s-l);--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--shadow:none;--op:100%" class="go8FFDHbfM ">
      
  {%- assign total_combinations = 1 -%}
  {%- for option in product.options_with_values -%}
    {%- assign total_combinations = total_combinations | times: option.values.size -%}
  {%- endfor -%}
  <gp-product-variants
    data-id="go8FFDHbfM"
    
    has-pre-selected="true"
    gp-data='{
      "setting":{"showAsSwatches":true,"optionType":"singleOption","label":false,"optionAlign":{"desktop":"left"},"price":true,"combineFullWidth":{"desktop":true},"combineWidth":{"desktop":"100%"},"combineHeight":"45px","variantPresets":[{"optionName":"base","optionType":"rectangle_list","presets":{"color":{"width":{"desktop":"45px","tablet":"45px","mobile":"45px"},"height":"45px","spacing":"16px","optionBorder":{"normal":{"borderType":"style-2","border":"solid","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px","borderWidth":"1px"},"active":{"borderType":"style-3","border":"solid","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px","borderWidth":"2px"},"hover":{"borderType":"style-3","border":"solid","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px","borderWidth":"1px"}},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionShadow":{"normal":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"width":{"desktop":""},"height":"45px","spacing":"16px","optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"normal":{"borderType":"style-2","border":"solid","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px","borderWidth":"1px"},"active":{"borderType":"style-3","border":"solid","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px","borderWidth":"2px"},"hover":{"borderType":"style-3","border":"solid","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px","borderWidth":"1px"}},"optionRounded":{"normal":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionShadow":{"normal":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}}},"image":{"width":{"desktop":"64px","tablet":"64px","mobile":"64px"},"height":"64px","spacing":"16px","optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"normal":{"borderType":"style-2","border":"solid","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px","borderWidth":"1px"},"active":{"borderType":"style-3","border":"solid","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px","borderWidth":"2px"},"hover":{"borderType":"style-3","border":"solid","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px","borderWidth":"1px"}},"optionRounded":{"normal":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionShadow":{"normal":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"width":{"desktop":"64px","tablet":"64px","mobile":"64px"},"height":"64px","spacing":"16px","optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"normal":{"borderType":"style-2","border":"solid","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px","borderWidth":"1px"},"active":{"borderType":"style-3","border":"solid","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px","borderWidth":"2px"},"hover":{"borderType":"style-3","border":"solid","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px","borderWidth":"1px"}},"optionRounded":{"normal":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionShadow":{"normal":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"width":{"desktop":"100%","tablet":"100%","mobile":"100%"},"height":"45px","optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"normal":{"borderType":"style-2","border":"solid","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px","borderWidth":"1px"},"active":{"borderType":"style-3","border":"solid","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px","borderWidth":"2px"},"hover":{"borderType":"style-3","border":"solid","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px","borderWidth":"1px"}},"optionRounded":{"normal":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionShadow":{"normal":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}}}},"isEdited":true},{"optionName":"Color","optionType":"color","presets":{"color":{"width":{"desktop":"30px","tablet":"30px","mobile":"30px"},"height":"30px","spacing":"2px","optionBorder":{"normal":{"borderType":"style-2","border":"solid","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px","borderWidth":"1px"},"active":{"borderType":"style-3","border":"solid","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px","borderWidth":"2px"},"hover":{"borderType":"style-3","border":"solid","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px","borderWidth":"1px"}},"optionRounded":{"normal":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"hover":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"},"active":{"btrr":"999999px","bblr":"999999px","bbrr":"999999px","btlr":"999999px","radiusType":"custom"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionShadow":{"normal":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"rectangle_list":{"width":{"desktop":""},"height":"45px","spacing":"16px","optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"normal":{"borderType":"style-2","border":"solid","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px","borderWidth":"1px"},"active":{"borderType":"style-3","border":"solid","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px","borderWidth":"2px"},"hover":{"borderType":"style-3","border":"solid","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px","borderWidth":"1px"}},"optionRounded":{"normal":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionShadow":{"normal":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}}},"image":{"width":{"desktop":"64px","tablet":"64px","mobile":"64px"},"height":"64px","spacing":"16px","optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"normal":{"borderType":"style-2","border":"solid","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px","borderWidth":"1px"},"active":{"borderType":"style-3","border":"solid","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px","borderWidth":"2px"},"hover":{"borderType":"style-3","border":"solid","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px","borderWidth":"1px"}},"optionRounded":{"normal":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionShadow":{"normal":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"image_shopify":{"width":{"desktop":"64px","tablet":"64px","mobile":"64px"},"height":"64px","spacing":"16px","optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"normal":{"borderType":"style-2","border":"solid","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px","borderWidth":"1px"},"active":{"borderType":"style-3","border":"solid","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px","borderWidth":"2px"},"hover":{"borderType":"style-3","border":"solid","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px","borderWidth":"1px"}},"optionRounded":{"normal":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionShadow":{"normal":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"}},"dropdown":{"width":{"desktop":"100%","tablet":"100%","mobile":"100%"},"height":"45px","optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionBorder":{"normal":{"borderType":"style-2","border":"solid","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px","borderWidth":"1px"},"active":{"borderType":"style-3","border":"solid","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px","borderWidth":"2px"},"hover":{"borderType":"style-3","border":"solid","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px","borderWidth":"1px"}},"optionRounded":{"normal":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionShadow":{"normal":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}}}},"isEdited":true}],"soldOutStyle":"line","layout":{"desktop":"vertical"},"column":{"desktop":1},"hasPreSelected":true,"blankText":"Please select an option","hasOnlyDefaultVariant":false},
      "styles":{"fullWidth":{"desktop":true},"width":{"desktop":"100%"},"optionSpacing":"30px","labelGap":"8px","swatchAutoWidth":{"desktop":true},"swatchWidth":{"desktop":"80px"},"swatchHeight":{"desktop":"45px"},"swatchSpacing":"var(--g-s-m)","marginBottom":{"desktop":"8px","mobile":"var(--g-s-xl)"},"align":{"desktop":"left"},"labelTypo":{"type":"paragraph-2","attrs":{"color":"text-1","bold":true}},"labelColor":"text-2","optionTypo":{"type":"paragraph-2"},"optionBgColor":{"normal":"bg-3","hover":"bg-3","active":"bg-3"},"optionTextColor":{"normal":"text-2","hover":"text-2","active":"text-2"},"optionBorder":{"normal":{"borderType":"style-2","border":"solid","color":"line-2","isCustom":false,"width":"1px 1px 1px 1px","borderWidth":"1px"},"active":{"borderType":"style-3","border":"solid","color":"line-3","isCustom":false,"width":"2px 2px 2px 2px","borderWidth":"2px"},"hover":{"borderType":"style-3","border":"solid","color":"line-3","isCustom":false,"width":"1px 1px 1px 1px","borderWidth":"1px"}},"optionRounded":{"normal":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"hover":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"},"active":{"btrr":"0px","bblr":"0px","bbrr":"0px","btlr":"0px","radiusType":"custom"}},"optionHasShadow":{"normal":false,"hover":false,"active":false},"optionShadow":{"normal":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"hover":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90},"active":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"rgba(0, 0, 0, 0.20)","angle":90}},"dropdownItemWidth":{"desktop":"fill","mobile":"fill","tablet":"fill"},"fixedDropdownWidth":{"desktop":"240px"},"swatchItemWidth":{"desktop":"auto"}},
      "variants":{{product.variants | json | escape}},
      "optionsWithValues": {{product.options_with_values | json | escape}},
      "variantSelected": {{ variant | json | escape }},
      "variantInventoryQuantity": {{product.variants | map: 'inventory_quantity' | json | escape}},
      "variantInventoryPolicy": {{product.variants | map: 'inventory_policy' | json | escape}},
      "moneyFormat": {{shop.money_format | json | escape}},
      "productId": {{product.id | json | escape}},
      "productUrl": {{product.url | json | escape}},
      "productHandle": {{product.handle | json | escape}},
      "displayState": {"desktop":true,"tablet":true,"mobile":true},
      "totalVariantCombinations": {{total_combinations}},
      "firstAvailableVariant": {{product.selected_or_first_available_variant | json | escape}}
    }
  '>
    {%- assign options = product.options_with_values -%}
    {%- assign variants = product.variants -%}
    {%- if options.size == 0 or options.size == 1 and variants.size == 1 and variants[0].title == 'Default Title' and variants[0].option1 == 'Default Title' -%}
      <div></div>
    {% else %}
      <div
      class="gp-grid !gp-ml-0"
      style="--gtc:repeat(1, minmax(0, 1fr));--ta:left;--w:100%;--w-tablet:100%;--w-mobile:100%;--rg:8px;--rg-mobile:var(--g-s-xl)"
    >
      
      {% assign presets = "base($2)rectangle_list($1)Color($2)color" | split: '($1)' %}
      {% assign hiddenPresetOptions = "" | split: ',' %}
      {%- for option in options -%}
        <div
        option-name="{{option.name | escape}}"
        class="gp-flex variant-inside gp-flex-col gp-items-start"

        >
          {% assign showVariantClass = 'variant-display' %}
          {% assign optionName = option.name %}
          {% for preset in presets %}
            {% assign presetDetail = preset | split: '($2)' %}
            {% if presetDetail[1] == 'dropdown' and presetDetail[0] == optionName %}
              {% assign showVariantClass = '' %}
              {% break %}
            {% endif %}
          {% endfor %}

          

          <div
              variant-option-name="{{option.name | escape}}"
              class="gp-justify-start gp-flex gp-w-full gp-flex-wrap gp-items-center variant-option-group"
              style="--rg:var(--g-s-m);--cg:var(--g-s-m)"
            >
              {%- assign values = option.values -%}
              {%- assign rootForloop = forloop.index0 -%}
              {%- if option.position == 1 -%}
                {%- assign selectedValue = variant.option1 -%}
              {%- elsif option.position == 2 -%}
                {%- assign selectedValue = variant.option2 -%}
              {%- else -%}
                {%- assign selectedValue = variant.option3 -%}
              {%- endif -%}
              
              
    {% assign optionRendered = false %}
    {%  assign swatches = shop.metafields.GEMPAGES.swatches %}
    {%  assign swatchesItems = swatches | split: '($1)'  %}
    {% for swatchesItem in swatchesItems %}
      {% assign colorArraysString = "" %}
      {% assign labelsString = "" %}
      {% assign imageUrlsString = "" %}

      {%  assign attrItems = swatchesItem | split: '($3)'  %}
      {% for attrItem in attrItems %}
        {%  assign attrs = attrItem | split: '($2)'  %}


          {% assign optionKey = attrs[0] %}
          {% assign optionValue = attrs[1] %}
          {% if optionKey == 'optionTitle' %}
                {% assign optionTitle = optionValue %}
              {% elsif optionKey == 'optionType' %}
                {% assign optionType = optionValue %}
            {% endif %}


            {% if optionKey == 'optionValues' %}

              {% assign opValueItems = optionValue | split: '($4)'  %}
              {% for opValueItem in opValueItems %}
                {% assign opValueItemAttrs = opValueItem | split: '($6)'  %}
                {% for opValueItemAttr in opValueItemAttrs %}
                  {% assign attrs = opValueItemAttr | split: '($5)'  %}
                  {% assign opValueItemKey = attrs[0] %}
                  {% assign opValueItemValue = attrs[1] %}

                  {% if opValueItemKey == 'label' %}
                    {% assign labelsString = labelsString | append: opValueItemValue %}
                    {% assign labelsString = labelsString | append: "($8)" %}
                  {% endif %}

                  {% if opValueItemKey == 'colors' %}
                    {% assign colorArraysString = colorArraysString | append: opValueItemValue %}
                    {% assign colorArraysString = colorArraysString | append: "($8)" %}
                  {% endif %}

                  {% if opValueItemKey == 'imageUrl' %}
                    {% assign imageUrlsString = imageUrlsString | append: opValueItemValue %}
                    {% assign imageUrlsString = imageUrlsString | append: "($8)" %}

                  {% endif %}
                {% endfor %}
              {% endfor %}
            {% endif %}

      {% endfor %}
      {% assign labels = labelsString | split: '($8)' %}
      {% assign colorStrings = colorArraysString | split: '($8)' %}
      {% assign imageUrls = imageUrlsString | split: '($8)' %}

      {% if optionTitle == option.name %}
      {% assign variantPresetString = "base($1)rectangle_list($2)Color($1)color" %}
      {% assign optionName = option.name | replace: "'", "&apos;" | replace: '"', "&quot;" %}
        {% assign items = variantPresetString | split:'($2)' %}
        {% assign type = 'dropdown' %}
        {%- for item in items -%}
          {% assign itemPreset = item | split:'($1)' %}
          {% if itemPreset[0] == optionName %}
            {% assign type = itemPreset[1] %}
          {% endif %}
          {% if itemPreset[0] == "base" %}
            {% assign type = itemPreset[1] %}
          {% endif %}
        {%- endfor -%}
        {% assign optionRendered = true %}
        {%- for value in values -%}
          
    {%- liquid
      assign option_disabled = true
      for variantItem in product.variants
        case option.position
          when 1
            if variantItem.available and variantItem.option1 == value
              assign option_disabled = false
            endif
          when 2
            if variantItem.available and variantItem.option2 == value
              assign option_disabled = false
            endif
          when 3
            if variantItem.available and variantItem.option3 == value
              assign option_disabled = false
            endif
        endcase
      endfor
    -%}

  {% assign option_disabled = false %}
  <label
  id="{{option.name | escape}}-{{value | escape}}"
  for="{{product.id}}-go8FFDHbfM-{{option.name | escape}}-{{option.position}}-{{ forloop.index }}"
  class="gp-group gp-relative option-item gp-child-item-go8FFDHbfM" 
>
  <div class="gp-invisible mobile:gp-overflow-hidden mobile:!gp-max-w-[0px] !gp-max-w-[150px] gp-left-[50%] gp-translate-x-[-50%] gp-translate-y-0 gp-absolute gp-bottom-[calc(100%+20px)] gp-text-[12px] group-hover:gp-visible gp-w-max gp-bg-[#333333] gp-text-[#F9F9F9] mobile:gp-px-[0px] gp-px-[8px] gp-py-[4px] gp-rounded-[8px] gp-mb-[-10px] after:gp-content-[''] after:gp-absolute mobile:after:gp-p-[0px] after:gp-p-[7px] after:gp-left-0 after:gp-w-full after:gp-bottom-[-10px]">
    <p class="gp-text-[#F9F9F9]">{{value | escape}}</p>
    <svg class="gp-absolute gp-left-[50%] gp-translate-x-[-50%] gp-translate-y-0 gp-z-10 gp-bottom-[-4px]" width="8" height="4" viewBox="0 0 16 10" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M8 10L0 0L16 1.41326e-06L8 10Z" fill="#333333"/>
    </svg>
  </div>
  <input
    checked="{%- if option.selected_value == value -%}true{% else %}false{%- endif -%}"
    name="{{product.id}}-options-{{option.name | escape}}"
    id="{{product.id}}-go8FFDHbfM-{{option.name | escape}}-{{option.position}}-{{ forloop.index }}"
    value="{{value | escape}}"
    type="radio"
    class="gp-sr-only gp-absolute gp-bottom-0 gp-right-0"
  />
  <span class="gp-sr-only">{{value | escape}}</span>
  <div   
  option-name="{{option.name | escape}}"
  option-name-value="{{value | escape}}"
  data-gp-option-value-id="{{value.id}}"
  class="option-item-inner gp-w-auto gp-h-auto {%- if option_disabled == true -%}{%- endif -%}">
  
    {% case type %}
      {% when "rectangle_list" %}
      <div
    class="
    {%- if selectedValue == value -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex gp-g-paragraph-2
    {%- else -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex gp-g-paragraph-2
    {%- endif -%}
    "
    style="
      {%- if selectedValue == value -%}
        --bg: var(--g-c-bg-3, bg-3);--bs: solid;--bw: 2px 2px 2px 2px;--bc: var(--g-c-line-3, line-3);--c: var(--g-c-text-2, text-2);--shadow: none;--bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--h: 45px;
      {%- else -%}
        --bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--hvr-bblr: 0px;--hvr-bbrr: 0px;--hvr-btlr: 0px;--hvr-btrr: 0px;--shadow: none;--hvr-shadow: none;--bg: var(--g-c-bg-3, bg-3);--hvr-bg: var(--g-c-bg-3, bg-3);--bs: solid;--hvr-bs: solid;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--c: var(--g-c-text-2, text-2);--hvr-c: var(--g-c-text-2, text-2);--h: 45px;
      {%- endif -%}
      "
    id="value-wrapper-{{option.name | escape | strip}}-{{value | escape | strip}}"
    option-data="{{option.name | escape}}"
    option-value="{{value | escape}}"
    option-position="{{option.position}}"
    data-gp-option-available="{{value.available}}"
    option-type="rectangle_list"
    data-disabled="{%- if option_disabled == true -%} disabled  {%- endif -%}"
    data-hidden='true'
  >
    
      <div class="option-value gp-overflow-hidden gp-relative 
      "
      style="--pl: 16px;--pr: 16px;--pt: 8px;--pb: 8px;">
      
      <span class="gp-text-center gp-g-paragraph-2" >{{value}}</span>
      </div>
    
  </div>
      {% when "color" %}
      
    {% assign colorsString = null %}
    {% assign colors = null %}
    {% for label in labels %}
      {% if label == value %}
        {% assign colorsString = colorStrings[forloop.index0] %}
      {% endif %}
    {% endfor %}
    {% if colorsString != null %}
      {% assign colors = colorsString | split: '($7)' %}
    {% endif %}
    <div
    class="
    {%- if selectedValue == value -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex gp-g-paragraph-2 gp-p-[4px]
    {%- else -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex gp-g-paragraph-2 gp-p-[4px]
    {%- endif -%}
    "
    style="
      {%- if selectedValue == value -%}
        --bs: solid;--bw: 2px 2px 2px 2px;--bc: var(--g-c-line-3, line-3);--shadow: none;--bblr: 999999px;--bbrr: 999999px;--btlr: 999999px;--btrr: 999999px;--h: 45px;--w: 45px;
      {%- else -%}
        --bblr: 999999px;--bbrr: 999999px;--btlr: 999999px;--btrr: 999999px;--hvr-bblr: 999999px;--hvr-bbrr: 999999px;--hvr-btlr: 999999px;--hvr-btrr: 999999px;--shadow: none;--hvr-shadow: none;--h: 45px;--w: 45px;
      {%- endif -%}
      "
    id="value-wrapper-{{option.name | escape | strip}}-{{value | escape | strip}}"
    option-data="{{option.name | escape}}"
    option-value="{{value | escape}}"
    option-position="{{option.position}}"
    data-gp-option-available="{{value.available}}"
    option-type="color"
    data-disabled="{%- if option_disabled == true -%} disabled  {%- endif -%}"
    data-hidden='true'
  >
    {%- if colors != null and colors.size > 0 -%} 
      {%- for color in colors -%}
      {% assign backgroundType = "background-color" %}
        {% if color contains "linear-gradient" %}
          {% assign backgroundType = "background-image" %}
        {% endif %}
        <div
          class="gp-relative gp-h-full gp-w-full gp-min-w-fit gp-flex gp-color-circle before:gp-hidden before:gp-absolute before:gp-top-[50%] before:-gp-rotate-45 before:gp-border-t before:gp-z-1 before:gp-content-[''] before:gp-w-full"
          data-test="{{ backgroundType }}: {{ color }}"
          style="{{ backgroundType }}: {{ color }}; 
    {%- if selectedValue == value -%}
      --d: flex;--jc: center;	--ai: center;--w: 100%;--h: 100%;--of: hidden;--bs: none;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--bblr: 999999px;--bbrr: 999999px;--btlr: 999999px;--btrr: 999999px;--hvr-bblr: 999999px;--hvr-bbrr: 999999px;--hvr-btlr: 999999px;--hvr-btrr: 999999px;
    {%- else -%}
      --d: flex;--jc: center;	--ai: center;--w: 100%;--h: 100%;--of: hidden;--bs: solid;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--bblr: 999999px;--bbrr: 999999px;--btlr: 999999px;--btrr: 999999px;--hvr-bblr: 999999px;--hvr-bbrr: 999999px;--hvr-btlr: 999999px;--hvr-btrr: 999999px;
    {%- endif -%}
    "></div>
      {%- endfor -%}
       {% else %} 
      <div class="option-value gp-overflow-hidden gp-relative 
      "
      style="
    {%- if selectedValue == value -%}
      --d: flex;--jc: center;	--ai: center;--w: 100%;--h: 100%;--of: hidden;--bs: none;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--bblr: 999999px;--bbrr: 999999px;--btlr: 999999px;--btrr: 999999px;--hvr-bblr: 999999px;--hvr-bbrr: 999999px;--hvr-btlr: 999999px;--hvr-btrr: 999999px;
    {%- else -%}
      --d: flex;--jc: center;	--ai: center;--w: 100%;--h: 100%;--of: hidden;--bs: solid;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--bblr: 999999px;--bbrr: 999999px;--btlr: 999999px;--btrr: 999999px;--hvr-bblr: 999999px;--hvr-bbrr: 999999px;--hvr-btlr: 999999px;--hvr-btrr: 999999px;
    {%- endif -%}
    ">
      
      <span class="gp-text-center gp-overflow-hidden gp-text-ellipsis gp-px-1 gp-g-paragraph-2" >{{value}}</span>
      </div>
     {%- endif -%}
  </div>
  
      {% when "image_shopify" %}
      
      {% assign imageUrl = null %}
      {% for variant in variants %}
        {% assign valueIncludesSelectedOption = false %}
        {% for item in variant.options %}
          {% if item == value %}
          {% assign valueIncludesSelectedOption = true %}
          {% endif %}
        {% endfor %}
        {% if valueIncludesSelectedOption and variant.featured_image or variant.featured_media%}
          {% unless imageUrl %}
            {% if variant.featured_media %}
              {% assign imageUrl = variant.featured_media.preview_image.src | product_img_url: '200x'  %}
            {% else %}
              {% assign imageUrl = variant.featured_image.src | product_img_url: '200x'  %}
            {% endif %}
          {% endunless %}
        {% endif %}
      {% endfor %}
      <div
    class="
    {%- if selectedValue == value -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex gp-g-paragraph-2
    {%- else -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex gp-g-paragraph-2
    {%- endif -%}
    "
    style="
      {%- if selectedValue == value -%}
        --bg: var(--g-c-bg-3, bg-3);--bs: solid;--bw: 2px 2px 2px 2px;--bc: var(--g-c-line-3, line-3);--shadow: none;--bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--h: 64px;--w: 64px;
      {%- else -%}
        --bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--hvr-bblr: 0px;--hvr-bbrr: 0px;--hvr-btlr: 0px;--hvr-btrr: 0px;--shadow: none;--hvr-shadow: none;--bg: var(--g-c-bg-3, bg-3);--hvr-bg: var(--g-c-bg-3, bg-3);--bs: solid;--hvr-bs: solid;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--h: 64px;--w: 64px;
      {%- endif -%}
      "
    id="value-wrapper-{{option.name | escape | strip}}-{{value | escape | strip}}"
    option-data="{{option.name | escape}}"
    option-value="{{value | escape}}"
    option-position="{{option.position}}"
    data-gp-option-available="{{value.available}}"
    option-type="image_shopify"
    data-disabled="{%- if option_disabled == true -%} disabled  {%- endif -%}"
    data-hidden='true'
  >
    {%- if imageUrl != null and imageUrl != "" -%} 
      <img style="width: 64px; height: 64px" class="gp-object-cover gp-rounded-none" src="{{imageUrl}}" alt="" />
     {% else %} 
      <div class="option-value gp-overflow-hidden gp-relative gp-flex gp-flex-col gp-justify-center gp-items-center gp-gap-[6px]
      "
      >
      <svg width="15" height="12" viewBox="0 0 15 12" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="2" cy="2" r="2" fill="#D6D6D6"></circle><path d="M0.196854 10.6453L2.40968 8.05946C2.4846 7.97175 2.57719 7.9008 2.68141 7.85124C2.78562 7.80168 2.89913 7.77461 3.01452 7.77181C3.12991 7.76902 3.2446 7.79055 3.3511 7.835C3.45761 7.87945 3.55353 7.94583 3.63262 8.02981L4.66733 9.12714L8.71205 4.29464C8.79123 4.19995 8.89077 4.1243 9.00325 4.07333C9.11573 4.02236 9.23827 3.99737 9.36176 4.00022C9.48524 4.00307 9.6065 4.03369 9.71651 4.08979C9.82651 4.1459 9.92245 4.22605 9.99717 4.32429L14.8329 10.6772C14.9254 10.7989 14.982 10.9441 14.9964 11.0962C15.0107 11.2484 14.9823 11.4016 14.9144 11.5385C14.8464 11.6754 14.7415 11.7907 14.6115 11.8713C14.4815 11.952 14.3316 11.9948 14.1786 11.995L0.822048 12C0.664945 11.9999 0.511148 11.9549 0.378853 11.8703C0.246557 11.7857 0.141299 11.6649 0.0755311 11.5224C0.00976323 11.3799 -0.013762 11.2216 0.00773837 11.0661C0.0292388 10.9107 0.0948652 10.7646 0.196854 10.6453Z" fill="#D6D6D6"></path></svg>
      <span class="gp-text-center gp-overflow-hidden gp-text-ellipsis gp-px-1 gp-g-paragraph-2" >{{value}}</span>
      </div>
     {%- endif -%}
  </div>
  
      {% when "image" %}
      
    {% assign imageUrl = null %}
    {% for label in labels %}
    {% if label == value %}
      {% assign imageUrl = imageUrls[forloop.index0] %}
    {% endif %}
    {% endfor %}
    <div
    class="
    {%- if selectedValue == value -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex gp-g-paragraph-2
    {%- else -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex gp-g-paragraph-2
    {%- endif -%}
    "
    style="
      {%- if selectedValue == value -%}
        --bg: var(--g-c-bg-3, bg-3);--bs: solid;--bw: 2px 2px 2px 2px;--bc: var(--g-c-line-3, line-3);--shadow: none;--bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--h: 64px;--w: 64px;
      {%- else -%}
        --bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--hvr-bblr: 0px;--hvr-bbrr: 0px;--hvr-btlr: 0px;--hvr-btrr: 0px;--shadow: none;--hvr-shadow: none;--bg: var(--g-c-bg-3, bg-3);--hvr-bg: var(--g-c-bg-3, bg-3);--bs: solid;--hvr-bs: solid;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--h: 64px;--w: 64px;
      {%- endif -%}
      "
    id="value-wrapper-{{option.name | escape | strip}}-{{value | escape | strip}}"
    option-data="{{option.name | escape}}"
    option-value="{{value | escape}}"
    option-position="{{option.position}}"
    data-gp-option-available="{{value.available}}"
    option-type="image"
    data-disabled="{%- if option_disabled == true -%} disabled  {%- endif -%}"
    data-hidden='true'
  >
    {%- if imageUrl != null and imageUrl != "" -%} 
      <img style="width: 64px; height: 64px" class="gp-object-cover gp-rounded-none" src="{{imageUrl}}" alt="" />
     {% else %} 
      <div class="option-value gp-overflow-hidden gp-relative gp-flex gp-flex-col gp-justify-center gp-items-center gp-gap-[6px]
      "
      >
      <svg width="15" height="12" viewBox="0 0 15 12" fill="none" xmlns="http://www.w3.org/2000/svg"><circle cx="2" cy="2" r="2" fill="#D6D6D6"></circle><path d="M0.196854 10.6453L2.40968 8.05946C2.4846 7.97175 2.57719 7.9008 2.68141 7.85124C2.78562 7.80168 2.89913 7.77461 3.01452 7.77181C3.12991 7.76902 3.2446 7.79055 3.3511 7.835C3.45761 7.87945 3.55353 7.94583 3.63262 8.02981L4.66733 9.12714L8.71205 4.29464C8.79123 4.19995 8.89077 4.1243 9.00325 4.07333C9.11573 4.02236 9.23827 3.99737 9.36176 4.00022C9.48524 4.00307 9.6065 4.03369 9.71651 4.08979C9.82651 4.1459 9.92245 4.22605 9.99717 4.32429L14.8329 10.6772C14.9254 10.7989 14.982 10.9441 14.9964 11.0962C15.0107 11.2484 14.9823 11.4016 14.9144 11.5385C14.8464 11.6754 14.7415 11.7907 14.6115 11.8713C14.4815 11.952 14.3316 11.9948 14.1786 11.995L0.822048 12C0.664945 11.9999 0.511148 11.9549 0.378853 11.8703C0.246557 11.7857 0.141299 11.6649 0.0755311 11.5224C0.00976323 11.3799 -0.013762 11.2216 0.00773837 11.0661C0.0292388 10.9107 0.0948652 10.7646 0.196854 10.6453Z" fill="#D6D6D6"></path></svg>
      <span class="gp-text-center gp-overflow-hidden gp-text-ellipsis gp-px-1 gp-g-paragraph-2" >{{value}}</span>
      </div>
     {%- endif -%}
  </div>
  
      {% else %}
    {% endcase %}
      

     
    
    
  </div>
</label>
        {%- endfor -%}
        {% if type == 'dropdown' %}
        
    <select
    aria-label={{option.name | escape}}
    autocomplete="off"
    id="p-variant-dropdown-{{option.position}}"
    name="{%- if option -%}{{option.name | escape}}{% else %}Select Option{%- endif -%}"
    option-data="{{option.name}}"
    option-type="{{optionType}}"
    option-renderer="{{optionRendered}}"
    class="gp-truncate gp-bg-auto gp-pl-4 gp-pr-6 gp-outline-none dropdown-option-item gp-border-g-line-2 hover:gp-border-g-line-3 gp-g-paragraph-2 gp-text-g-text-2 hover:gp-text-g-text-2 active:gp-text-g-text-2 gp-bg-g-bg-3 hover:gp-bg-g-bg-3 active:gp-bg-g-bg-3 gp-outline-none gp-shadow-none"
 
    style="--shadow:none;--hvr-shadow:none;--bg:var(--g-c-bg-3, bg-3);--bs:solid;--bw:1px 1px 1px 1px;--bc:var(--g-c-line-2, line-2);--c:var(--g-c-text-2, text-2);--hvr-c:var(--g-c-text-2, text-2);--hvr-bg:var(--g-c-bg-3, bg-3);--h:45px;--w:100%;--w-tablet:100%;--w-mobile:100%;--hvr-bs:solid;--hvr-bw:1px 1px 1px 1px;--hvr-bc:var(--g-c-line-3, line-3);--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--hvr-bblr:0px;--hvr-bbrr:0px;--hvr-btlr:0px;--hvr-btrr:0px;appearance:none;background-image:url(https://cdn.shopify.com/s/files/1/1827/4239/t/1/assets/ico-select.svg?v=155563818344741998551488860031);background-repeat:no-repeat;background-position:right 16px center"
  >
  
  {%- for value in values -%}
          {%- liquid
            assign option_disabled = true
            for variantItem in product.variants
              case option.position
                when 1
                  if variantItem.available and variantItem.option1 == value
                    assign option_disabled = false
                  endif
                when 2
                  if variantItem.available and variantItem.option2 == value
                    assign option_disabled = false
                  endif
                when 3
                  if variantItem.available and variantItem.option3 == value
                    assign option_disabled = false
                  endif
              endcase
            endfor
          -%}
          {%- if value == selectedValue -%}
              <option selected 
                  
    option-data="{{option.name | escape}}"
    option-value="{{value | escape}}" 
    data-gp-option-value-id="{{value.id}}" 
    data-gp-option-available="{{value.available}}"
    value="{{value | escape}}" 
    class="option-value-wrapper" 
    key="{{value | escape}}"
  
                  option-position="{{option.position}}">
                  {{value}} 
              </option>
              {% else %}
              <option
                  
    option-data="{{option.name | escape}}"
    option-value="{{value | escape}}" 
    data-gp-option-value-id="{{value.id}}" 
    data-gp-option-available="{{value.available}}"
    value="{{value | escape}}" 
    class="option-value-wrapper" 
    key="{{value | escape}}"
  
                  option-position="{{option.position}}">
                  {{value}} 
              </option>
              {%- endif -%}
         
        {%- endfor -%}
  </select>
    
        {% endif %}
      {% endif %}
    {% endfor %}

    {% if optionRendered == false %}
      
    {%- for value in values -%}
      
    {%- liquid
      assign option_disabled = true
      for variantItem in product.variants
        case option.position
          when 1
            if variantItem.available and variantItem.option1 == value
              assign option_disabled = false
            endif
          when 2
            if variantItem.available and variantItem.option2 == value
              assign option_disabled = false
            endif
          when 3
            if variantItem.available and variantItem.option3 == value
              assign option_disabled = false
            endif
        endcase
      endfor
    -%}

  {% assign option_disabled = false %}
  <label
  id="{{option.name | escape}}-{{value | escape}}"
  for="{{product.id}}-go8FFDHbfM-{{option.name | escape}}-{{option.position}}-{{ forloop.index }}"
  class="gp-group gp-relative option-item gp-child-item-go8FFDHbfM" 
>
  <div class="gp-invisible mobile:gp-overflow-hidden mobile:!gp-max-w-[0px] !gp-max-w-[150px] gp-left-[50%] gp-translate-x-[-50%] gp-translate-y-0 gp-absolute gp-bottom-[calc(100%+20px)] gp-text-[12px] group-hover:gp-visible gp-w-max gp-bg-[#333333] gp-text-[#F9F9F9] mobile:gp-px-[0px] gp-px-[8px] gp-py-[4px] gp-rounded-[8px] gp-mb-[-10px] after:gp-content-[''] after:gp-absolute mobile:after:gp-p-[0px] after:gp-p-[7px] after:gp-left-0 after:gp-w-full after:gp-bottom-[-10px]">
    <p class="gp-text-[#F9F9F9]">{{value | escape}}</p>
    <svg class="gp-absolute gp-left-[50%] gp-translate-x-[-50%] gp-translate-y-0 gp-z-10 gp-bottom-[-4px]" width="8" height="4" viewBox="0 0 16 10" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M8 10L0 0L16 1.41326e-06L8 10Z" fill="#333333"/>
    </svg>
  </div>
  <input
    checked="{%- if option.selected_value == value -%}true{% else %}false{%- endif -%}"
    name="{{product.id}}-options-{{option.name | escape}}"
    id="{{product.id}}-go8FFDHbfM-{{option.name | escape}}-{{option.position}}-{{ forloop.index }}"
    value="{{value | escape}}"
    type="radio"
    class="gp-sr-only gp-absolute gp-bottom-0 gp-right-0"
  />
  <span class="gp-sr-only">{{value | escape}}</span>
  <div   
  option-name="{{option.name | escape}}"
  option-name-value="{{value | escape}}"
  data-gp-option-value-id="{{value.id}}"
  class="option-item-inner gp-w-auto gp-h-auto {%- if option_disabled == true -%}{%- endif -%}">
  
    <div
    class="
    {%- if selectedValue == value -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex gp-g-paragraph-2
    {%- else -%}
      gp-relative gp-flex gp-cursor-pointer gp-items-center gp-justify-center gp-text-center option-value-wrapper gp-overflow-hidden gp-w-full gp-h-full data-[disabled='disabled']:gp-pointer-events-none data-[disabled='disabled']:!gp-cursor-not-allowed 
         data-[hidden='false']:gp-flex gp-g-paragraph-2
    {%- endif -%}
    "
    style="
      {%- if selectedValue == value -%}
        --bg: var(--g-c-bg-3, bg-3);--bs: solid;--bw: 2px 2px 2px 2px;--bc: var(--g-c-line-3, line-3);--c: var(--g-c-text-2, text-2);--shadow: none;--bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--h: 45px;--w: 80px;
      {%- else -%}
        --bblr: 0px;--bbrr: 0px;--btlr: 0px;--btrr: 0px;--hvr-bblr: 0px;--hvr-bbrr: 0px;--hvr-btlr: 0px;--hvr-btrr: 0px;--shadow: none;--hvr-shadow: none;--bg: var(--g-c-bg-3, bg-3);--hvr-bg: var(--g-c-bg-3, bg-3);--bs: solid;--hvr-bs: solid;--bw: 1px 1px 1px 1px;--hvr-bw: 1px 1px 1px 1px;--bc: var(--g-c-line-2, line-2);--hvr-bc: var(--g-c-line-3, line-3);--c: var(--g-c-text-2, text-2);--hvr-c: var(--g-c-text-2, text-2);--h: 45px;--w: 80px;
      {%- endif -%}
      "
    id="value-wrapper-{{option.name | escape | strip}}-{{value | escape | strip}}"
    option-data="{{option.name | escape}}"
    option-value="{{value | escape}}"
    option-position="{{option.position}}"
    data-gp-option-available="{{value.available}}"
    option-type="rectangle_list"
    data-disabled="{%- if option_disabled == true -%} disabled  {%- endif -%}"
    data-hidden='true'
  >
    
      <div class="option-value gp-overflow-hidden gp-relative 
      "
      style="--pl: 16px;--pr: 16px;--pt: 8px;--pb: 8px;">
      
      <span class="gp-text-center gp-g-paragraph-2" >{{value}}</span>
      </div>
    
  </div>
    
  </div>
</label>
    {%- endfor -%}
    
    {% endif %}
    
          </div>
      </div>
      {%- endfor -%}
    
    </div>
    {%- endif -%}

  </gp-product-variants>
  <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-variant-v3.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
   
      </div>
   {%- liquid
      assign inventory_quantity = variant.inventory_quantity | default: 0
    -%}
    <gp-product-button
      class="gp-product-button"
      gp-data-wrapper="true"
      gp-data='{"setting":{"actionEffect":"open-cart-drawer","errorMessage":"Cannot add product to cart","successMessage":"Add product to cart successfully","enableSuccessMessage":true,"enableErrorMessage":true,"label":"Add to cart","outOfStockLabel":"Out Of Stock","customURL":{"link":"/cart","target":"_self"}},"styles":{"errorTypo":{"attrs":{"color":"error"},"type":"paragraph-2"},"successTypo":{"attrs":{"color":"success"},"type":"paragraph-2"}},"disabled":"{{variant.available}}","variantID":"{{variant.id}}","totalVariant":"{{product.variants.size}}"}' 
       gp-href="{{ request.origin }}{{ routes.root_url | split: '/' | join: '/' }}/cart"
       data-variant-selection-required-message="{{}}"
    >
        
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
    class="{% unless variant.available %} !gp-hidden {% endunless %}"
  >
    <style>
    .gua_Lzrt3y.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px 0px 0px 0px;
  border-color: transparent;
  
      
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
      
    }

    .gua_Lzrt3y:hover::before {
      border-style: none;
  border-width: 0px 0px 0px 0px;
  border-color: transparent;
  
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }

    .gua_Lzrt3y:hover .gp-button-icon {
      color: #f6f6f6;
    }

     .gua_Lzrt3y .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gua_Lzrt3y:hover .gp-button-price {
      color: #f6f6f6;
    }

    .gua_Lzrt3y .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gua_Lzrt3y .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gua_Lzrt3y:hover .gp-product-dot-price {
      color: #f6f6f6;
    }
  </style>
    <button
      type="submit" data-id="gua_Lzrt3y" aria-label="Add to cart"
      name="add" gp-data-hidden="{% unless variant.available %}true{% endunless %}"
      data-state="idle"
      class="gua_Lzrt3y gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-button-atc tcustomizer-submit-button"
      style="--hvr-bg:#0274F7;--bg:#096de3;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:32px;--pr:32px;--pt:12px;--pb:12px;--pl-tablet:32px;--pr-tablet:32px;--pt-tablet:12px;--pb-tablet:12px;--pl-mobile:32px;--pr-mobile:32px;--pt-mobile:12px;--pb-mobile:12px;--hvr-c:#f6f6f6;--c:var(--g-c-text-3, text-3);--size:14px;--size-tablet:13px;--size-mobile:12px;--fs:normal;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:600;--ls:normal;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--tt:uppercase"
    >
        <svg
          class="gp-invisible gp-absolute gp-h-5 gp-w-5 group-data-[state=loading]/button:gp-animate-spin group-data-[state=loading]/button:gp-visible"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            class="gp-opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            stroke-width="4"
          ></circle>
          <path
            class="gp-opacity-75"
            fill="currentColor"
            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          ></path>
        </svg>
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="group-hover/button:!gp-text-inherit gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:14px;--size-tablet:13px;--size-mobile:12px;--hvr-c:#f6f6f6;--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggua_Lzrt3y_label }}
      </span>
      </div>
    
    </button>
  </div>
  </gp-button>

        
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--ta:left"
    class="{% if variant.available %} !gp-hidden {% endif %}"
  >
    <style>
    .gua_Lzrt3y-sold-out.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px 0px 0px 0px;
  border-color: transparent;
  
      
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
      
    }

    .gua_Lzrt3y-sold-out:hover::before {
      border-style: none;
  border-width: 0px 0px 0px 0px;
  border-color: transparent;
  
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }

    .gua_Lzrt3y-sold-out:hover .gp-button-icon {
      color: #f6f6f6;
    }

     .gua_Lzrt3y-sold-out .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gua_Lzrt3y-sold-out:hover .gp-button-price {
      color: #f6f6f6;
    }

    .gua_Lzrt3y-sold-out .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gua_Lzrt3y-sold-out .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gua_Lzrt3y-sold-out:hover .gp-product-dot-price {
      color: #f6f6f6;
    }
  </style>
    <button
      type="button" data-id="gua_Lzrt3y" aria-label="Out Of Stock"
      gp-data-hidden="{% if variant.available %}true{% endif %}"
      data-state="idle"
      class="gua_Lzrt3y-sold-out gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-button-sold-out btn-disabled gp-opacity-30 gp-cursor-default"
      style="--hvr-bg:#0274F7;--bg:#096de3;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:32px;--pr:32px;--pt:12px;--pb:12px;--pl-tablet:32px;--pr-tablet:32px;--pt-tablet:12px;--pb-tablet:12px;--pl-mobile:32px;--pr-mobile:32px;--pt-mobile:12px;--pb-mobile:12px;--hvr-c:#f6f6f6;--c:var(--g-c-text-3, text-3);--size:14px;--size-tablet:13px;--size-mobile:12px;--fs:normal;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:600;--ls:normal;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--tt:uppercase"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="group-hover/button:!gp-text-inherit gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:14px;--size-tablet:13px;--size-mobile:12px;--hvr-c:#f6f6f6;--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggua_Lzrt3y_outOfStockLabel }}
      </span>
      </div>
    
    </button>
  </div>
  </gp-button>

    </gp-product-button>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-product-button.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
    </div>
    </div>
    </div>
   
    
              {%- endform -%}
            </gp-product>
        {%- endif -%}
        {% endfor %}
        {%- if count == 0 and 4 == 0 -%}
          {% if 'PickProduct' == 'RelatedProduct' -%}
              <div>{{ "gempages.ProductList.no_related_products" | t }}</div>
            {% else %}
              <div>{{ "gempages.ProductList.no_products_found" | t }}</div>
          {%- endif -%}
        {%- endif -%}
      
      </gp-same-height-v2>
    
        
      </div>

    
    
      
    {%- endif -%}
    
{%- assign product = gpBkProduct -%}
{%- assign products = gpBkProducts -%}

      </div>
      <style id="custom-css-gY_J60V8b4">
         .gY_J60V8b4 gp-product-variants [option-name*="Color"] .variant-option-group label.gp-hide{
  display: none !important;
}
      </style>
    
      <script id="custom-js-gY_J60V8b4">
        try {
          document.addEventListener('DOMContentLoaded', function(){
  
 const currentElement = document.querySelector('.gY_J60V8b4');
 const variantElements = currentElement.querySelectorAll('gp-product-variants [option-name*="Color"]');
  
 const colors = ['Black', 'Blue','Beige', 'Brown', 'Gray', 'Grey'];
 variantElements.forEach(elm => {
   const hiddenElements = [];
   elm.querySelectorAll('.variant-option-group label').forEach(variant => {
     const variantName = variant.querySelector('input').value;
     if(!colors.includes(variantName)){
       variant.classList.add('gp-hide');
       hiddenElements.push(variant);
     }
      
   });
//   if(hiddenElements.length > 0){
//       //const newElm = document.createElement("div");
//       //newElm.classList.add('hidden-elements');
//       //newElm.innerText = "+" + hiddenElements.length;
//       //elm.querySelector('.variant-option-group').appendChild(newElm);
      
//       setTimeout(function() {
//         hiddenElements.forEach(hiddenelm => {
//           if(hiddenelm.classList.contains('option-item-active')){
//             elm.querySelector('.variant-option-group label:not(.gp-hide) [option-value]').click();
//           }
//         })
        
//       }, 1000);
//   }
 })
});
        } catch(err){}
      </script>
    
    </div>
    </div>
   
    
    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 2",
    "tag": "section",
    "class": "gps-555417746818007930 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/gardpro-us/apps/gempages-cro/app/shopify/edit?pageType=GP_STATIC&editorId=555417746733532026&sectionId=555417746818007930)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggM4K72-wOE_customContent_suffix","label":"ggM4K72-wOE_customContent_suffix","default":"off"},{"type":"html","id":"ggua_Lzrt3y_label","label":"ggua_Lzrt3y_label","default":"Add to cart"},{"type":"html","id":"ggua_Lzrt3y_outOfStockLabel","label":"ggua_Lzrt3y_outOfStockLabel","default":"Out Of Stock"},{"type":"html","id":"ggua_Lzrt3y_unavailableLabel","label":"ggua_Lzrt3y_unavailableLabel","default":"Unavailable"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
