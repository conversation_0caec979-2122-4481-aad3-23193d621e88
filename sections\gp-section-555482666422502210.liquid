

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-555482666422502210.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-555482666422502210.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-555482666422502210.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-555482666422502210.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-555482666422502210.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-555482666422502210.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-555482666422502210.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-555482666422502210.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-555482666422502210.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-555482666422502210.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-555482666422502210.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-555482666422502210.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-555482666422502210.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-555482666422502210.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-555482666422502210.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-555482666422502210.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-555482666422502210.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-555482666422502210.gps.gpsil [style*="--bottom:"]{bottom:var(--bottom)}.gps-555482666422502210.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-555482666422502210.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-555482666422502210.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-555482666422502210.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-555482666422502210.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-555482666422502210.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-555482666422502210.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-555482666422502210.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-555482666422502210.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-555482666422502210.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-555482666422502210.gps.gpsil [style*="--left:"]{left:var(--left)}.gps-555482666422502210.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-555482666422502210.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-555482666422502210.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-555482666422502210.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-555482666422502210.gps.gpsil [style*="--ml:"]{margin-left:var(--ml)}.gps-555482666422502210.gps.gpsil [style*="--mt:"]{margin-top:var(--mt)}.gps-555482666422502210.gps.gpsil [style*="--maxw:"]{max-width:var(--maxw)}.gps-555482666422502210.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-555482666422502210.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-555482666422502210.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-555482666422502210.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-555482666422502210.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-555482666422502210.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-555482666422502210.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-555482666422502210.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-555482666422502210.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-555482666422502210.gps.gpsil [style*="--pos:"]{position:var(--pos)}.gps-555482666422502210.gps.gpsil [style*="--right:"]{right:var(--right)}.gps-555482666422502210.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-555482666422502210.gps.gpsil [style*="--ts:"]{text-shadow:var(--ts)}.gps-555482666422502210.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-555482666422502210.gps.gpsil [style*="--top:"]{top:var(--top)}.gps-555482666422502210.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-555482666422502210.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-555482666422502210.gps.gpsil [style*="--z:"]{z-index:var(--z)}.gps-555482666422502210.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-555482666422502210.gps.gpsil [style*="--bgi-tablet:"]{background-image:var(--bgi-tablet)}.gps-555482666422502210.gps.gpsil [style*="--bottom-tablet:"]{bottom:var(--bottom-tablet)}.gps-555482666422502210.gps.gpsil [style*="--cg-tablet:"]{-moz-column-gap:var(--cg-tablet);column-gap:var(--cg-tablet)}.gps-555482666422502210.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-555482666422502210.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-555482666422502210.gps.gpsil [style*="--left-tablet:"]{left:var(--left-tablet)}.gps-555482666422502210.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-555482666422502210.gps.gpsil [style*="--mb-tablet:"]{margin-bottom:var(--mb-tablet)}.gps-555482666422502210.gps.gpsil [style*="--ml-tablet:"]{margin-left:var(--ml-tablet)}.gps-555482666422502210.gps.gpsil [style*="--mt-tablet:"]{margin-top:var(--mt-tablet)}.gps-555482666422502210.gps.gpsil [style*="--maxw-tablet:"]{max-width:var(--maxw-tablet)}.gps-555482666422502210.gps.gpsil [style*="--minw-tablet:"]{min-width:var(--minw-tablet)}.gps-555482666422502210.gps.gpsil [style*="--pc-tablet:"]{place-content:var(--pc-tablet)}.gps-555482666422502210.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-555482666422502210.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-555482666422502210.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-555482666422502210.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-555482666422502210.gps.gpsil [style*="--pos-tablet:"]{position:var(--pos-tablet)}.gps-555482666422502210.gps.gpsil [style*="--right-tablet:"]{right:var(--right-tablet)}.gps-555482666422502210.gps.gpsil [style*="--top-tablet:"]{top:var(--top-tablet)}.gps-555482666422502210.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-555482666422502210.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-555482666422502210.gps.gpsil [style*="--bgi-mobile:"]{background-image:var(--bgi-mobile)}.gps-555482666422502210.gps.gpsil [style*="--bottom-mobile:"]{bottom:var(--bottom-mobile)}.gps-555482666422502210.gps.gpsil [style*="--cg-mobile:"]{-moz-column-gap:var(--cg-mobile);column-gap:var(--cg-mobile)}.gps-555482666422502210.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-555482666422502210.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-555482666422502210.gps.gpsil [style*="--left-mobile:"]{left:var(--left-mobile)}.gps-555482666422502210.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-555482666422502210.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-555482666422502210.gps.gpsil [style*="--ml-mobile:"]{margin-left:var(--ml-mobile)}.gps-555482666422502210.gps.gpsil [style*="--mt-mobile:"]{margin-top:var(--mt-mobile)}.gps-555482666422502210.gps.gpsil [style*="--maxw-mobile:"]{max-width:var(--maxw-mobile)}.gps-555482666422502210.gps.gpsil [style*="--minw-mobile:"]{min-width:var(--minw-mobile)}.gps-555482666422502210.gps.gpsil [style*="--pc-mobile:"]{place-content:var(--pc-mobile)}.gps-555482666422502210.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-555482666422502210.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-555482666422502210.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-555482666422502210.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-555482666422502210.gps.gpsil [style*="--pos-mobile:"]{position:var(--pos-mobile)}.gps-555482666422502210.gps.gpsil [style*="--right-mobile:"]{right:var(--right-mobile)}.gps-555482666422502210.gps.gpsil [style*="--top-mobile:"]{top:var(--top-mobile)}.gps-555482666422502210.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-555482666422502210.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-555482666422502210 .gp-rotate-0,.gps-555482666422502210 .gp-rotate-180,.gps-555482666422502210 .mobile\:gp-rotate-0,.gps-555482666422502210 .mobile\:gp-rotate-180,.gps-555482666422502210 .tablet\:gp-rotate-0,.gps-555482666422502210 .tablet\:gp-rotate-180{--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1}.gps-555482666422502210 .gp-shadow-md{--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000}.gps-555482666422502210 .gp-g-paragraph-1{font-family:var(--g-p1-ff);font-size:var(--g-p1-size);font-style:var(--g-p1-fs);font-weight:var(--g-p1-weight);letter-spacing:var(--g-p1-ls);line-height:var(--g-p1-lh)}.gps-555482666422502210 .gp-static{position:static}.gps-555482666422502210 .\!gp-absolute{position:absolute!important}.gps-555482666422502210 .gp-absolute{position:absolute}.gps-555482666422502210 .gp-relative{position:relative}.gps-555482666422502210 .gp-inset-0{inset:0}.gps-555482666422502210 .gp-left-0{left:0}.gps-555482666422502210 .gp-left-\[var\(--left\)\]{left:var(--left)}.gps-555482666422502210 .gp-right-0{right:0}.gps-555482666422502210 .gp-top-\[var\(--top\)\]{top:var(--top)}.gps-555482666422502210 .gp-z-1{z-index:1}.gps-555482666422502210 .gp-z-2{z-index:2}.gps-555482666422502210 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-555482666422502210 .gp-my-0{margin-bottom:0;margin-top:0}.gps-555482666422502210 .gp-mb-0{margin-bottom:0}.gps-555482666422502210 .gp-block{display:block}.gps-555482666422502210 .\!gp-flex{display:flex!important}.gps-555482666422502210 .gp-flex{display:flex}.gps-555482666422502210 .gp-inline-flex{display:inline-flex}.gps-555482666422502210 .gp-grid{display:grid}.gps-555482666422502210 .\!gp-hidden{display:none!important}.gps-555482666422502210 .gp-hidden{display:none}.gps-555482666422502210 .gp-aspect-square{aspect-ratio:1/1}.gps-555482666422502210 .gp-h-full{height:100%}.gps-555482666422502210 .\!gp-min-h-full{min-height:100%!important}.gps-555482666422502210 .gp-w-\[12px\]{width:12px}.gps-555482666422502210 .gp-w-full{width:100%}.gps-555482666422502210 .gp-max-w-full{max-width:100%}.gps-555482666422502210 .gp-shrink-0{flex-shrink:0}.gps-555482666422502210 .gp-rotate-0{--tw-rotate:0deg}.gps-555482666422502210 .gp-rotate-0,.gps-555482666422502210 .gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-555482666422502210 .gp-rotate-180{--tw-rotate:180deg}.gps-555482666422502210 .gp-cursor-pointer{cursor:pointer}.gps-555482666422502210 .gp-select-none{-webkit-user-select:none;-moz-user-select:none;user-select:none}.gps-555482666422502210 .\!gp-flex-row{flex-direction:row!important}.gps-555482666422502210 .gp-flex-row{flex-direction:row}.gps-555482666422502210 .gp-flex-col{flex-direction:column}.gps-555482666422502210 .gp-flex-wrap{flex-wrap:wrap}.gps-555482666422502210 .\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-555482666422502210 .gp-items-center{align-items:center}.gps-555482666422502210 .\!gp-justify-end{justify-content:flex-end!important}.gps-555482666422502210 .gp-justify-center{justify-content:center}.gps-555482666422502210 .gp-justify-between{justify-content:space-between}.gps-555482666422502210 .gp-gap-2{gap:8px}.gps-555482666422502210 .gp-gap-y-0{row-gap:0}.gps-555482666422502210 .gp-overflow-hidden{overflow:hidden}.gps-555482666422502210 .gp-rounded-full{border-radius:9999px}.gps-555482666422502210 .gp-text-center{text-align:center}.gps-555482666422502210 .gp-leading-\[0\]{line-height:0}.gps-555482666422502210 .gp-text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128/var(--tw-text-opacity))}.gps-555482666422502210 .gp-shadow-md{--tw-shadow:0 4px 6px -1px rgba(0,0,0,.1),0 2px 4px -2px rgba(0,0,0,.1);--tw-shadow-colored:0 4px 6px -1px var(--tw-shadow-color),0 2px 4px -2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.gps-555482666422502210 .gp-transition-all{transition-duration:.15s;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555482666422502210 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555482666422502210 .gp-duration-200{transition-duration:.2s}.gps-555482666422502210 .gp-duration-300{transition-duration:.3s}.gps-555482666422502210 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}@media (hover:hover) and (pointer:fine){.gps-555482666422502210 .gp-group\/hero:hover .group-hover\/hero\:gp-bg-\[color\:var\(--hvr-bgc\2c _var\(--bgc\)\)\]{background-color:var(--hvr-bgc,var(--bgc))}.gps-555482666422502210 .gp-group\/hero:hover .group-hover\/hero\:gp-opacity-\[var\(--hvr-op\2c _var\(--op\)\)\]{opacity:var(--hvr-op,var(--op))}}@media (max-width:1024px){.gps-555482666422502210 .tablet\:gp-static{position:static}.gps-555482666422502210 .tablet\:\!gp-absolute{position:absolute!important}.gps-555482666422502210 .tablet\:gp-relative{position:relative}.gps-555482666422502210 .tablet\:gp-left-0{left:0}.gps-555482666422502210 .tablet\:gp-right-0{right:0}.gps-555482666422502210 .tablet\:gp-z-2{z-index:2}.gps-555482666422502210 .tablet\:gp-block{display:block}.gps-555482666422502210 .tablet\:\!gp-flex{display:flex!important}.gps-555482666422502210 .tablet\:\!gp-hidden{display:none!important}.gps-555482666422502210 .tablet\:gp-hidden{display:none}.gps-555482666422502210 .tablet\:\!gp-min-h-full{min-height:100%!important}.gps-555482666422502210 .tablet\:gp-rotate-0{--tw-rotate:0deg}.gps-555482666422502210 .tablet\:gp-rotate-0,.gps-555482666422502210 .tablet\:gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-555482666422502210 .tablet\:gp-rotate-180{--tw-rotate:180deg}.gps-555482666422502210 .tablet\:\!gp-flex-row{flex-direction:row!important}.gps-555482666422502210 .tablet\:gp-flex-row{flex-direction:row}.gps-555482666422502210 .tablet\:\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-555482666422502210 .tablet\:\!gp-content-stretch{align-content:stretch!important}.gps-555482666422502210 .tablet\:\!gp-justify-end{justify-content:flex-end!important}.gps-555482666422502210 .tablet\:gp-px-0{padding-left:0;padding-right:0}}@media (max-width:767px){.gps-555482666422502210 .mobile\:gp-static{position:static}.gps-555482666422502210 .mobile\:\!gp-absolute{position:absolute!important}.gps-555482666422502210 .mobile\:gp-relative{position:relative}.gps-555482666422502210 .mobile\:gp-left-0{left:0}.gps-555482666422502210 .mobile\:gp-right-0{right:0}.gps-555482666422502210 .mobile\:gp-z-2{z-index:2}.gps-555482666422502210 .mobile\:gp-block{display:block}.gps-555482666422502210 .mobile\:\!gp-flex{display:flex!important}.gps-555482666422502210 .mobile\:\!gp-hidden{display:none!important}.gps-555482666422502210 .mobile\:gp-hidden{display:none}.gps-555482666422502210 .mobile\:\!gp-min-h-full{min-height:100%!important}.gps-555482666422502210 .mobile\:gp-rotate-0{--tw-rotate:0deg}.gps-555482666422502210 .mobile\:gp-rotate-0,.gps-555482666422502210 .mobile\:gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-555482666422502210 .mobile\:gp-rotate-180{--tw-rotate:180deg}.gps-555482666422502210 .mobile\:\!gp-flex-row{flex-direction:row!important}.gps-555482666422502210 .mobile\:gp-flex-row{flex-direction:row}.gps-555482666422502210 .mobile\:\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-555482666422502210 .mobile\:\!gp-content-stretch{align-content:stretch!important}.gps-555482666422502210 .mobile\:\!gp-justify-end{justify-content:flex-end!important}.gps-555482666422502210 .mobile\:gp-px-0{padding-left:0;padding-right:0}}@media (max-width:1024px){.gps-555482666422502210 .tablet\:\[\&\>\*\]\:\!gp-justify-end>*{justify-content:flex-end!important}}@media (max-width:767px){.gps-555482666422502210 .mobile\:\[\&\>\*\]\:\!gp-justify-end>*{justify-content:flex-end!important}}.gps-555482666422502210 .\[\&\>svg\]\:\!gp-h-\[var\(--height-desktop\)\]>svg{height:var(--height-desktop)!important}.gps-555482666422502210 .\[\&\>svg\]\:gp-h-full>svg{height:100%}.gps-555482666422502210 .\[\&\>svg\]\:\!gp-w-auto>svg{width:auto!important}.gps-555482666422502210 .\[\&\>svg\]\:gp-w-full>svg{width:100%}@media (max-width:1024px){.gps-555482666422502210 .tablet\:\[\&\>svg\]\:\!gp-h-\[var\(--height-tablet\)\]>svg{height:var(--height-tablet)!important}}@media (max-width:767px){.gps-555482666422502210 .mobile\:\[\&\>svg\]\:\!gp-h-\[var\(--height-mobile\)\]>svg{height:var(--height-mobile)!important}}.gps-555482666422502210 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-555482666422502210 .\[\&_p\]\:gp-inline p{display:inline}.gps-555482666422502210 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-555482666422502210 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      enableLazyloadImage="true" id="gojryj1vGZ" data-id="gojryj1vGZ"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-4xl);--pl:15px;--pb:var(--g-s-4xl);--pr:15px;--pt-mobile:12px;--pl-mobile:15px;--pb-mobile:12px;--pr-mobile:15px;--pt-tablet:var(--g-s-4xl);--pl-tablet:15px;--pb-tablet:var(--g-s-4xl);--pr-tablet:15px;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gojryj1vGZ gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gKMKxdDPFE gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="g3HQKpNJQi" data-id="g3HQKpNJQi"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:var(--g-s-l);--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g3HQKpNJQi gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="ghCIQmS7wH gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gFyXFra_Ly">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gFyXFra_Ly "
        style="--ta:center;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-3xl);--mb-mobile:24px;--mb-tablet:27px"
      >
      <div class="gp-flex" style="--jc:center">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:40px;--size-tablet:30px;--size-mobile:24px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;--tt:uppercase;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggFyXFra_Ly_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
    <gp-carousel data-id="gF5nsap-SC"  id="gp-root-carousel-gF5nsap-SC-{{section.id}}" class="  gp-group/carousel gp-flex" gp-data='{"id":"gF5nsap-SC-{{section.id}}","setting":{"childItem":["Slide 1","Slide 2","Slide 3","Slide 4","Slide 5"],"itemNumber":{"desktop":5,"tablet":2,"mobile":2},"sneakPeak":{"desktop":true},"sneakPeakType":{"desktop":"forward"},"sneakPeakOffsetForward":{"desktop":50},"sneakPeakOffsetCenter":{"desktop":50},"vertical":{"desktop":false},"controlOverContent":{"desktop":true},"dot":{"desktop":true,"tablet":true,"mobile":true},"dotStyle":{"desktop":"outside"},"dotSize":{"desktop":12},"dotGapToCarousel":{"desktop":16},"dotColor":{"desktop":"bg-1"},"dotActiveColor":{"desktop":"line-3"},"navigationStyle":{"desktop":"inside"},"arrowCustom":"<svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"currentColor\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M7.62204 5.13313C7.78476 4.95562 8.04858 4.95562 8.21129 5.13313L12.378 9.67859C12.5407 9.8561 12.5407 10.1439 12.378 10.3214L8.21129 14.8669C8.04858 15.0444 7.78476 15.0444 7.62204 14.8669C7.45932 14.6894 7.45932 14.4016 7.62204 14.224L11.4941 10L7.62204 5.77596C7.45932 5.59845 7.45932 5.31064 7.62204 5.13313Z\"/>\n              </svg>\n              ","arrowCustomColor":"#000000","arrowIconSize":{"desktop":24},"arrowButtonSize":{"desktop":{"shapeValue":"1/1","shapeLinked":true,"width":"32px","height":"32px","padding":{"linked":true}}},"arrowBackgroundColor":"transparent","arrowBorder":{"desktop":{"borderType":"none","border":"none","borderWidth":"1px","width":"1px 1px 1px 1px","isCustom":false}},"roundedArrow":{"desktop":{"radiusType":"small"}},"arrowGapToEachSide":"16","showWhenHover":false,"rtl":false,"autoplay":true,"autoplayTimeout":2,"pauseOnHover":true,"runPreview":false,"enableDrag":{"desktop":true},"loop":{"desktop":true},"animationMode":"ease-in","background":{"desktop":{"type":"color","color":"transparent","image":{"src":"","width":0,"height":0},"size":"cover","position":{"x":50,"y":50},"repeat":"no-repeat","attachment":"scroll"}},"label":true},"styles":{"fullWidth":{"desktop":false,"tablet":false,"mobile":false},"sizeSetting":{"desktop":{"shapeLinked":false,"width":"100%","height":"auto","gap":""}},"playSpeed":500,"align":{"desktop":"center"},"borderContent":{"borderType":"none","border":"none","width":"1px 1px 1px 1px","position":"all","borderWidth":"1px","color":"#121212","isCustom":true},"roundedContent":{"radiusType":"none"},"hasActiveShadow":false,"carouselShadow":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"#121212","angle":90},"spacing":{"desktop":16},"itemPadding":{"desktop":{"padding":{"type":"custom","top":"0px","left":"0px","bottom":"0px","right":"0px"}}}},"isHiddenArrowWhenDisabled":true}' style="--jc:center">
      <div class="gp-hidden gp-aspect-square gp-w-[12px] gp-rounded-full gp-shadow-md"></div>
      <div
        
        class="gp-relative gp-my-0 tablet:gp-px-0 gp-max-w-full mobile:gp-px-0 gp-carousel-wrapper mobile:gp-block tablet:gp-block gp-block gF5nsap-SC"
        style="--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto"
      >
        <div
          class="gp-h-full gp-relative gp-mx-auto gp-flex gp-items-center gp-justify-between mobile:!gp-flex-row tablet:!gp-flex-row !gp-flex-row"
          style="--h:auto;--h-tablet:auto;--h-mobile:auto;gap:16px"
        >
          
    <button
      type="button"
      aria-label='Carousel Back Arrow'
      class="gp-z-1 gp-carousel-action-back gem-slider-previous gF5nsap-SC-{{section.id}} gp-carousel-arrow-gF5nsap-SC gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-flex !gp-absolute gp-z-2 tablet:!gp-flex tablet:!gp-absolute tablet:gp-z-2 mobile:!gp-flex mobile:!gp-absolute mobile:gp-z-2"
      style="--left:16px;--top:initial;--right:initial;--bottom:;--left-tablet:16px;--top-tablet:initial;--right-tablet:initial;--bottom-tablet:;--left-mobile:16px;--top-mobile:initial;--right-mobile:initial;--bottom-mobile:;--d:flex;--d-tablet:flex;--d-mobile:flex;background-color:transparent;--w:32px;--h:32px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-180 tablet:gp-rotate-180 mobile:gp-rotate-180"
  style="--c:#000000;--w:24px;--w-tablet:24px;--w-mobile:24px;--h:24px;--h-tablet:24px;--h-mobile:24px"
  >
    <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M7.62204 5.13313C7.78476 4.95562 8.04858 4.95562 8.21129 5.13313L12.378 9.67859C12.5407 9.8561 12.5407 10.1439 12.378 10.3214L8.21129 14.8669C8.04858 15.0444 7.78476 15.0444 7.62204 14.8669C7.45932 14.6894 7.45932 14.4016 7.62204 14.224L11.4941 10L7.62204 5.77596C7.45932 5.59845 7.45932 5.31064 7.62204 5.13313Z"/>
              </svg>
              
    </div>
      <style>
    .gp-carousel-arrow-gF5nsap-SC {
      border-radius: var(--g-radius-small);
    }
    .gp-carousel-arrow-gF5nsap-SC::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      border-radius: var(--g-radius-small);
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .gp-carousel-arrow-gF5nsap-SC {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-gF5nsap-SC::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
    @media only screen and (max-width: 768px) {
      .gp-carousel-arrow-gF5nsap-SC {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-gF5nsap-SC::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
  </style>
    </button>
  
          <div id="gp-carousel-gF5nsap-SC-{{section.id}}" class="gem-slider gp-h-full gp-overflow-hidden gp-select-none mobile:!gp-flex-nowrap tablet:!gp-flex-nowrap !gp-flex-nowrap mobile:!gp-min-h-full tablet:!gp-min-h-full !gp-min-h-full"
          style="--cg-mobile:16px;--cg-tablet:16px;--cg:16px">
          
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      enableLazyloadImage="true" tag="CarouselItem" label="Carousel Item" type="component"
      style="--bgc:transparent;--minw:calc(100% / 4.5 - 12.444444444444445px);--minw-tablet:calc(100% / 1.5 - 5.333333333333333px);--minw-mobile:calc(100% / 1.5 - 5.333333333333333px);--maxw:calc(100% / 4.5 - 12.444444444444445px);--maxw-tablet:calc(100% / 1.5 - 5.333333333333333px);--maxw-mobile:calc(100% / 1.5 - 5.333333333333333px);--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ml:0px;--ml-tablet:0px;--ml-mobile:0px;--pl:0px;--pr:0px;--pt:0px;--pb:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-gF5nsap-SC gAU8m_1Y__"
      data-index="0"
    >
      <div
        class="gp-w-full",
        style="--shadow:none"
      >
      
    <div class="gp-flex gp-w-full" style="--h:494px;--h-tablet:494px;--h-mobile:460px;--d:flex;--d-mobile:flex;--d-tablet:flex">
      <gp-hero-banner
        gp-data='{"target":"_blank","background":{"desktop":{"attachment":"scroll","color":"bg-2","image":{"src":"https://cdn.shopify.com/s/files/1/0733/7908/6636/files/image_4.png?v=1740384367","width":327,"height":504,"backupFileKey":""},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"image"},"tablet":{"attachment":"scroll","color":"bg-2","image":{"src":"https://cdn.shopify.com/s/files/1/0733/7908/6636/files/image_4.png?v=1740384367","width":327,"height":504,"backupFileKey":""},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"image"},"mobile":{"attachment":"scroll","color":"bg-2","image":{"src":"https://cdn.shopify.com/s/files/1/0733/7908/6636/files/image_4.png?v=1740384367","width":327,"height":504,"backupFileKey":""},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"image"}},"uid":"goCRTl5L59","enableParallax":false,"speedParallax":0,"hoverEffect":false,"hoverEffectScale":110,"layout":{"desktop":{"cols":[12],"display":"fill"}},"contentPosition1Col":{"desktop":"bottom"},"contentPosition2Col":{"desktop":"bottom"}}'
        gp-href=""
        
        class="goCRTl5L59 gp-group/hero gp-flex gp-flex-col gp-transition-colors gp-duration-200 gp-ease-in-out gp-w-full"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:flex;--d-mobile:flex;--d-tablet:flex;--op:100%;--h:494px;--h-tablet:494px;--h-mobile:460px;--w:100%;--w-tablet:100%;--w-mobile:100%"
        data-id="goCRTl5L59"
      >
        
      <div
        class="gp-relative gp-flex gp-flex-col gp-items-center gp-overflow-hidden gp_haslazybg !gp-justify-end tablet:!gp-justify-end mobile:!gp-justify-end"
        style="--h:494px;--h-tablet:494px;--h-mobile:460px;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--bgc:var(--g-c-bg-2)"
      >
        
  <picture style="border-radius: inherit" class="gp-w-full">
  
      <source media="(max-width: 767px)" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/image_4.png?v=1740384367" />
      <source media="(max-width: 1024px)" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/image_4.png?v=1740384367" />
      <img
        title
        class="adaptive-hero-banner"
        src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/image_4.png?v=1740384367"
        alt=""
        fetchpriority="high"
        style="--d:none;--d-tablet:none;--d-mobile:none;--h:494px;--h-tablet:494px;--h-mobile:460px;--w:100%;--w-tablet:100%;--w-mobile:100%;--op:0;--z:-1"
      />
    
  </picture>
        <div class="gp-absolute gp-w-full gp-h-full">
          <div
            class="gp-overflow-hidden gp-relative gp-w-full gp-h-full"
            >
            <div
              aria-label="Background Image"
              role="banner"
              class='gp-hero-banner-bg gp-absolute gp-overflow-hidden gp-w-full gp-h-full top-0 left-0'
              style="clip-path:inset(0 0 0)"
            >
            <div class="hero-banner-bg-parallax gp-hero-banner-image-background"
              style="--bgc:var(--g-c-bg-2);--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bgi:url('https://cdn.shopify.com/s/files/1/0733/7908/6636/files/image_4.png?v=1740384367');--bgi-tablet:url('https://cdn.shopify.com/s/files/1/0733/7908/6636/files/image_4.png?v=1740384367');--bgi-mobile:url('https://cdn.shopify.com/s/files/1/0733/7908/6636/files/image_4.png?v=1740384367');--duration:0.5s;--scale:110;--pos:absolute;--pos-tablet:absolute;--pos-mobile:absolute;--h:100%;--h-tablet:100%;--h-mobile:100%;--w:100%;--w-tablet:100%;--w-mobile:100%;--top:;--top-tablet:;--top-mobile:"
            >
            
            
            </div></div>
          </div>
          <div
              aria-label="Overlay"
              role="banner"
              class="gp-absolute gp-inset-0 gp-left-[var(--left)] gp-top-[var(--top)] gp-transition-all gp-duration-300 group-hover/hero:gp-bg-[color:var(--hvr-bgc,_var(--bgc))] group-hover/hero:gp-opacity-[var(--hvr-op,_var(--op))]"
            style="--bgc:#121212;--top:0;--left:0;--op:60%">
            </div>
          </div>
          
       
      
    <div
      id data-id
        style="--cg:32px;--pc:end;--pc-tablet:end;--pc-mobile:end;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:var(--g-ct-w, 1200px);--w-mobile:var(--g-ct-w, 1200px)"
        class="gp-hero-banner-row gp-z-1 gp-w-full tablet:gp-relative mobile:gp-relative tablet:[&>*>*]:!gp-justify-undefined mobile:[&>*>*]:!gp-justify-undefined tablet:!gp-content-stretch tablet:[&>*]:!gp-justify-end mobile:!gp-content-stretch mobile:[&>*]:!gp-justify-end gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g3oo3kaE1u gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="gaHCK0FEJt" data-id="gaHCK0FEJt"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pl:var(--g-s-xl);--pb:var(--g-s-xl);--pr:var(--g-s-xl);--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gaHCK0FEJt gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gRD5DJlZ_8 gp-relative gp-flex gp-flex-col"
    >
      
    
      
      
    <div
    data-id="gQFFC5OHkm"
      
      data-id="gQFFC5OHkm"
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-s);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll">

    <div
            class="gp-flex gp-flex-wrap"
            style="--cg:4px;--jc:left">
            <div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gXo25WXzwa ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] g0qasS_oZ_"
    >
      <div 
      data-id="g0qasS_oZ_"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg style="width: 100%; height: 100%" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="g6nWVVpyF6 ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] g_xm4SdmWY"
    >
      <div 
      data-id="g_xm4SdmWY"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg style="width: 100%; height: 100%" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="g8NTf20Lu6 ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] g7KyiEWO0c"
    >
      <div 
      data-id="g7KyiEWO0c"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg style="width: 100%; height: 100%" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="glApGuyDyk ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] g1B0TLsPRX"
    >
      <div 
      data-id="g1B0TLsPRX"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg style="width: 100%; height: 100%" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gd4B_tWcpW ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gXwZUksRs3"
    >
      <div 
      data-id="gXwZUksRs3"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817759863701864">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M234.29,114.85l-45,38.83L203,211.75a16.4,16.4,0,0,1-24.5,17.82L128,198.49,77.47,229.57A16.4,16.4,0,0,1,53,211.75l13.76-58.07-45-38.83A16.46,16.46,0,0,1,31.08,86l59-4.76,22.76-55.08a16.36,16.36,0,0,1,30.27,0l22.75,55.08,59,4.76a16.46,16.46,0,0,1,9.37,28.86Z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div>
         </div>
    </div>
  
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gCfGbdmYjm">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gCfGbdmYjm "
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#F6F6F6;word-break:break-word;--bgc:rgba(77, 77, 77, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggCfGbdmYjm_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gFn6MfQIJS">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gFn6MfQIJS "
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:700;--ls:normal;--size:14px;--size-tablet:13px;--size-mobile:12px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--c:#FFFFFF;--tt:uppercase;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggFn6MfQIJS_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
    </div>
    </div>
   
    
      </div>
    
        
      </gp-hero-banner>
    </div>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-hero-banner.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>

  
      </div>
    </div>
  
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      enableLazyloadImage="true" tag="CarouselItem" label="Carousel Item" type="component"
      style="--bgc:transparent;--minw:calc(100% / 4.5 - 12.444444444444445px);--minw-tablet:calc(100% / 1.5 - 5.333333333333333px);--minw-mobile:calc(100% / 1.5 - 5.333333333333333px);--maxw:calc(100% / 4.5 - 12.444444444444445px);--maxw-tablet:calc(100% / 1.5 - 5.333333333333333px);--maxw-mobile:calc(100% / 1.5 - 5.333333333333333px);--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ml:0px;--ml-tablet:0px;--ml-mobile:0px;--pl:0px;--pr:0px;--pt:0px;--pb:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-gF5nsap-SC gluGaq4CEJ"
      data-index="1"
    >
      <div
        class="gp-w-full",
        style="--shadow:none"
      >
      
    <div class="gp-flex gp-w-full" style="--h:494px;--h-tablet:494px;--h-mobile:460px;--d:flex;--d-mobile:flex;--d-tablet:flex">
      <gp-hero-banner
        gp-data='{"target":"_blank","background":{"desktop":{"attachment":"scroll","color":"bg-2","image":{"src":"https://cdn.shopify.com/s/files/1/0733/7908/6636/files/review_gp_2.jpg?v=1711369212","width":1440,"height":1799,"backupFileKey":"","storage":"FILE_CONTENT"},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"image"},"tablet":{"attachment":"scroll","color":"bg-2","image":{"src":"https://cdn.shopify.com/s/files/1/0733/7908/6636/files/review_gp_2.jpg?v=1711369212","width":1440,"height":1799,"backupFileKey":"","storage":"FILE_CONTENT"},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"image"},"mobile":{"attachment":"scroll","color":"bg-2","image":{"src":"https://cdn.shopify.com/s/files/1/0733/7908/6636/files/review_gp_2.jpg?v=1711369212","width":1440,"height":1799,"backupFileKey":"","storage":"FILE_CONTENT"},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"image"}},"uid":"gytE7tEvm0","enableParallax":false,"speedParallax":0,"hoverEffect":false,"hoverEffectScale":110,"layout":{"desktop":{"cols":[12],"display":"fill"}},"contentPosition1Col":{"desktop":"bottom"},"contentPosition2Col":{"desktop":"bottom"}}'
        gp-href=""
        
        class="gytE7tEvm0 gp-group/hero gp-flex gp-flex-col gp-transition-colors gp-duration-200 gp-ease-in-out gp-w-full"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:flex;--d-mobile:flex;--d-tablet:flex;--op:100%;--h:494px;--h-tablet:494px;--h-mobile:460px;--w:100%;--w-tablet:100%;--w-mobile:100%"
        data-id="gytE7tEvm0"
      >
        
      <div
        class="gp-relative gp-flex gp-flex-col gp-items-center gp-overflow-hidden gp_haslazybg !gp-justify-end tablet:!gp-justify-end mobile:!gp-justify-end"
        style="--h:494px;--h-tablet:494px;--h-mobile:460px;--bgc:var(--g-c-bg-2)"
      >
        
  <picture style="border-radius: inherit" class="gp-w-full">
  
      <source media="(max-width: 767px)" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/review_gp_2.jpg?v=1711369212" />
      <source media="(max-width: 1024px)" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/review_gp_2.jpg?v=1711369212" />
      <img
        title
        class="adaptive-hero-banner"
        src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/review_gp_2.jpg?v=1711369212"
        alt=""
        fetchpriority="high"
        style="--d:none;--d-tablet:none;--d-mobile:none;--h:494px;--h-tablet:494px;--h-mobile:460px;--w:100%;--w-tablet:100%;--w-mobile:100%;--op:0;--z:-1"
      />
    
  </picture>
        <div class="gp-absolute gp-w-full gp-h-full">
          <div
            class="gp-overflow-hidden gp-relative gp-w-full gp-h-full"
            >
            <div
              aria-label="Background Image"
              role="banner"
              class='gp-hero-banner-bg gp-absolute gp-overflow-hidden gp-w-full gp-h-full top-0 left-0'
              style="clip-path:inset(0 0 0)"
            >
            <div class="hero-banner-bg-parallax gp-hero-banner-image-background"
              style="--bgc:var(--g-c-bg-2);--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bgi:url('https://cdn.shopify.com/s/files/1/0733/7908/6636/files/review_gp_2.jpg?v=1711369212');--bgi-tablet:url('https://cdn.shopify.com/s/files/1/0733/7908/6636/files/review_gp_2.jpg?v=1711369212');--bgi-mobile:url('https://cdn.shopify.com/s/files/1/0733/7908/6636/files/review_gp_2.jpg?v=1711369212');--duration:0.5s;--scale:110;--pos:absolute;--pos-tablet:absolute;--pos-mobile:absolute;--h:100%;--h-tablet:100%;--h-mobile:100%;--w:100%;--w-tablet:100%;--w-mobile:100%;--top:;--top-tablet:;--top-mobile:"
            >
            
            
            </div></div>
          </div>
          <div
              aria-label="Overlay"
              role="banner"
              class="gp-absolute gp-inset-0 gp-left-[var(--left)] gp-top-[var(--top)] gp-transition-all gp-duration-300 group-hover/hero:gp-bg-[color:var(--hvr-bgc,_var(--bgc))] group-hover/hero:gp-opacity-[var(--hvr-op,_var(--op))]"
            style="--bgc:#121212;--top:0;--left:0;--op:62%">
            </div>
          </div>
          
       
      
    <div
      id data-id
        style="--cg:32px;--pc:end;--pc-tablet:end;--pc-mobile:end;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:var(--g-ct-w, 1200px);--w-mobile:var(--g-ct-w, 1200px)"
        class="gp-hero-banner-row gp-z-1 gp-w-full tablet:gp-relative mobile:gp-relative tablet:[&>*>*]:!gp-justify-undefined mobile:[&>*>*]:!gp-justify-undefined tablet:!gp-content-stretch tablet:[&>*]:!gp-justify-end mobile:!gp-content-stretch mobile:[&>*]:!gp-justify-end gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="glPEwuvkD1 gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="gKPr65EZlW" data-id="gKPr65EZlW"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pl:var(--g-s-xl);--pb:var(--g-s-xl);--pr:var(--g-s-xl);--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gKPr65EZlW gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gia7PhLw8D gp-relative gp-flex gp-flex-col"
    >
      
    
      
      
    <div
    data-id="gnkAgU5OSz"
      
      data-id="gnkAgU5OSz"
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-s);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll">

    <div
            class="gp-flex gp-flex-wrap"
            style="--cg:4px;--jc:left">
            <div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gg73Tz0_cg ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gL2_FtUgw2"
    >
      <div 
      data-id="gL2_FtUgw2"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg style="width: 100%; height: 100%" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="ghU36f_asH ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gPP1-x5Cet"
    >
      <div 
      data-id="gPP1-x5Cet"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg style="width: 100%; height: 100%" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gOrhSlq0lD ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gbQKqs9BeR"
    >
      <div 
      data-id="gbQKqs9BeR"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg style="width: 100%; height: 100%" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="g-ck1aaPUH ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gLTLyfFhPE"
    >
      <div 
      data-id="gLTLyfFhPE"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg style="width: 100%; height: 100%" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gHrLQyr2Zm ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gpXb94YCL-"
    >
      <div 
      data-id="gpXb94YCL-"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg style="width: 100%; height: 100%" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div>
         </div>
    </div>
  
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gtMx5-hxZj">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gtMx5-hxZj "
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#F6F6F6;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggtMx5-hxZj_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gMa8OwHkBi">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gMa8OwHkBi "
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:13px;--size-tablet:13px;--size-mobile:12px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--c:#FFFFFF;--tt:uppercase;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggMa8OwHkBi_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
    </div>
    </div>
   
    
      </div>
    
        
      </gp-hero-banner>
    </div>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-hero-banner.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>

  
      </div>
    </div>
  
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      enableLazyloadImage="true" tag="CarouselItem" label="Carousel Item" type="component"
      style="--bgc:transparent;--minw:calc(100% / 4.5 - 12.444444444444445px);--minw-tablet:calc(100% / 1.5 - 5.333333333333333px);--minw-mobile:calc(100% / 1.5 - 5.333333333333333px);--maxw:calc(100% / 4.5 - 12.444444444444445px);--maxw-tablet:calc(100% / 1.5 - 5.333333333333333px);--maxw-mobile:calc(100% / 1.5 - 5.333333333333333px);--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ml:0px;--ml-tablet:0px;--ml-mobile:0px;--pl:0px;--pr:0px;--pt:0px;--pb:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-gF5nsap-SC gjXTr-9FYI"
      data-index="2"
    >
      <div
        class="gp-w-full",
        style="--shadow:none"
      >
      
    <div class="gp-flex gp-w-full" style="--h:494px;--h-tablet:494px;--h-mobile:460px;--d:flex;--d-mobile:flex;--d-tablet:flex">
      <gp-hero-banner
        gp-data='{"target":"_blank","background":{"desktop":{"attachment":"scroll","color":"bg-2","image":{"src":"https://cdn.shopify.com/s/files/1/0733/7908/6636/files/review_gp_4.jpg?v=1711369212","width":1440,"height":1800,"backupFileKey":"","storage":"FILE_CONTENT"},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"image"},"tablet":{"attachment":"scroll","color":"bg-2","image":{"src":"https://cdn.shopify.com/s/files/1/0733/7908/6636/files/review_gp_4.jpg?v=1711369212","width":1440,"height":1800,"backupFileKey":"","storage":"FILE_CONTENT"},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"image"},"mobile":{"attachment":"scroll","color":"bg-2","image":{"src":"https://cdn.shopify.com/s/files/1/0733/7908/6636/files/review_gp_4.jpg?v=1711369212","width":1440,"height":1800,"backupFileKey":"","storage":"FILE_CONTENT"},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"image"}},"uid":"gcUKHjxAQE","enableParallax":false,"speedParallax":0,"hoverEffect":false,"hoverEffectScale":110,"layout":{"desktop":{"cols":[12],"display":"fill"}},"contentPosition1Col":{"desktop":"bottom"},"contentPosition2Col":{"desktop":"bottom"}}'
        gp-href=""
        
        class="gcUKHjxAQE gp-group/hero gp-flex gp-flex-col gp-transition-colors gp-duration-200 gp-ease-in-out gp-w-full"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:flex;--d-mobile:flex;--d-tablet:flex;--op:100%;--h:494px;--h-tablet:494px;--h-mobile:460px;--w:100%;--w-tablet:100%;--w-mobile:100%"
        data-id="gcUKHjxAQE"
      >
        
      <div
        class="gp-relative gp-flex gp-flex-col gp-items-center gp-overflow-hidden gp_haslazybg !gp-justify-end tablet:!gp-justify-end mobile:!gp-justify-end"
        style="--h:494px;--h-tablet:494px;--h-mobile:460px;--bgc:var(--g-c-bg-2)"
      >
        
  <picture style="border-radius: inherit" class="gp-w-full">
  
      <source media="(max-width: 767px)" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/review_gp_4.jpg?v=1711369212" />
      <source media="(max-width: 1024px)" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/review_gp_4.jpg?v=1711369212" />
      <img
        title
        class="adaptive-hero-banner"
        src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/review_gp_4.jpg?v=1711369212"
        alt=""
        fetchpriority="high"
        style="--d:none;--d-tablet:none;--d-mobile:none;--h:494px;--h-tablet:494px;--h-mobile:460px;--w:100%;--w-tablet:100%;--w-mobile:100%;--op:0;--z:-1"
      />
    
  </picture>
        <div class="gp-absolute gp-w-full gp-h-full">
          <div
            class="gp-overflow-hidden gp-relative gp-w-full gp-h-full"
            >
            <div
              aria-label="Background Image"
              role="banner"
              class='gp-hero-banner-bg gp-absolute gp-overflow-hidden gp-w-full gp-h-full top-0 left-0'
              style="clip-path:inset(0 0 0)"
            >
            <div class="hero-banner-bg-parallax gp-hero-banner-image-background"
              style="--bgc:var(--g-c-bg-2);--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bgi:url('https://cdn.shopify.com/s/files/1/0733/7908/6636/files/review_gp_4.jpg?v=1711369212');--bgi-tablet:url('https://cdn.shopify.com/s/files/1/0733/7908/6636/files/review_gp_4.jpg?v=1711369212');--bgi-mobile:url('https://cdn.shopify.com/s/files/1/0733/7908/6636/files/review_gp_4.jpg?v=1711369212');--duration:0.5s;--scale:110;--pos:absolute;--pos-tablet:absolute;--pos-mobile:absolute;--h:100%;--h-tablet:100%;--h-mobile:100%;--w:100%;--w-tablet:100%;--w-mobile:100%;--top:;--top-tablet:;--top-mobile:"
            >
            
            
            </div></div>
          </div>
          <div
              aria-label="Overlay"
              role="banner"
              class="gp-absolute gp-inset-0 gp-left-[var(--left)] gp-top-[var(--top)] gp-transition-all gp-duration-300 group-hover/hero:gp-bg-[color:var(--hvr-bgc,_var(--bgc))] group-hover/hero:gp-opacity-[var(--hvr-op,_var(--op))]"
            style="--bgc:#121212;--top:0;--left:0;--op:60%">
            </div>
          </div>
          
       
      
    <div
      id data-id
        style="--cg:32px;--pc:end;--pc-tablet:end;--pc-mobile:end;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:var(--g-ct-w, 1200px);--w-mobile:var(--g-ct-w, 1200px)"
        class="gp-hero-banner-row gp-z-1 gp-w-full tablet:gp-relative mobile:gp-relative tablet:[&>*>*]:!gp-justify-undefined mobile:[&>*>*]:!gp-justify-undefined tablet:!gp-content-stretch tablet:[&>*]:!gp-justify-end mobile:!gp-content-stretch mobile:[&>*]:!gp-justify-end gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gEN4IPwz4M gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="g5xB7ud5T3" data-id="g5xB7ud5T3"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pl:var(--g-s-xl);--pb:var(--g-s-xl);--pr:var(--g-s-xl);--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g5xB7ud5T3 gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gTDUcTCDRP gp-relative gp-flex gp-flex-col"
    >
      
    
      
      
    <div
    data-id="g6poVPvXA_"
      
      data-id="g6poVPvXA_"
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-s);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll">

    <div
            class="gp-flex gp-flex-wrap"
            style="--cg:4px;--jc:left">
            <div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="g5nELhFO7m ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gE51b7yzFN"
    >
      <div 
      data-id="gE51b7yzFN"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg style="width: 100%; height: 100%" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="g6n54Wcuch ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gXkvImn8wD"
    >
      <div 
      data-id="gXkvImn8wD"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg style="width: 100%; height: 100%" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="g_8w-hdTIf ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gokgdNqC-8"
    >
      <div 
      data-id="gokgdNqC-8"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg style="width: 100%; height: 100%" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gZahJFwlVA ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gMHkCGByTk"
    >
      <div 
      data-id="gMHkCGByTk"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg style="width: 100%; height: 100%" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gOPOMQXcYR ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gd0e9O3Vei"
    >
      <div 
      data-id="gd0e9O3Vei"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg style="width: 100%; height: 100%" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div>
         </div>
    </div>
  
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gAN0m73OB6">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gAN0m73OB6 "
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#F6F6F6;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggAN0m73OB6_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gywryYmKo1">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gywryYmKo1 "
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:13px;--size-tablet:13px;--size-mobile:12px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--c:#FFFFFF;--tt:uppercase;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggywryYmKo1_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
    </div>
    </div>
   
    
      </div>
    
        
      </gp-hero-banner>
    </div>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-hero-banner.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>

  
      </div>
    </div>
  
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      enableLazyloadImage="true" tag="CarouselItem" label="Carousel Item" type="component"
      style="--bgc:transparent;--minw:calc(100% / 4.5 - 12.444444444444445px);--minw-tablet:calc(100% / 1.5 - 5.333333333333333px);--minw-mobile:calc(100% / 1.5 - 5.333333333333333px);--maxw:calc(100% / 4.5 - 12.444444444444445px);--maxw-tablet:calc(100% / 1.5 - 5.333333333333333px);--maxw-mobile:calc(100% / 1.5 - 5.333333333333333px);--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ml:0px;--ml-tablet:0px;--ml-mobile:0px;--pl:0px;--pr:0px;--pt:0px;--pb:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-gF5nsap-SC g7VTx_efBU"
      data-index="3"
    >
      <div
        class="gp-w-full",
        style="--shadow:none"
      >
      
    <div class="gp-flex gp-w-full" style="--h:494px;--h-tablet:494px;--h-mobile:460px;--d:flex;--d-mobile:flex;--d-tablet:flex">
      <gp-hero-banner
        gp-data='{"target":"_blank","background":{"desktop":{"attachment":"scroll","color":"bg-2","image":{"src":"https://cdn.shopify.com/s/files/1/0733/7908/6636/files/gempages_553400155311702965-d8744a09-3ff0-4043-a03a-1c86ee8aa245.jpg","width":1080,"height":1440,"backupFileKey":"gempages_553400155311702965-d8744a09-3ff0-4043-a03a-1c86ee8aa245.jpg","storage":"FILE_CONTENT"},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"image"},"tablet":{"attachment":"scroll","color":"bg-2","image":{"src":"https://cdn.shopify.com/s/files/1/0733/7908/6636/files/gempages_553400155311702965-d8744a09-3ff0-4043-a03a-1c86ee8aa245.jpg","width":1080,"height":1440,"backupFileKey":"gempages_553400155311702965-d8744a09-3ff0-4043-a03a-1c86ee8aa245.jpg","storage":"FILE_CONTENT"},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"image"},"mobile":{"attachment":"scroll","color":"bg-2","image":{"src":"https://cdn.shopify.com/s/files/1/0733/7908/6636/files/gempages_553400155311702965-d8744a09-3ff0-4043-a03a-1c86ee8aa245.jpg","width":1080,"height":1440,"backupFileKey":"gempages_553400155311702965-d8744a09-3ff0-4043-a03a-1c86ee8aa245.jpg","storage":"FILE_CONTENT"},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"image"}},"uid":"ggIaBMfgNa","enableParallax":false,"speedParallax":0,"hoverEffect":false,"hoverEffectScale":110,"layout":{"desktop":{"cols":[12],"display":"fill"}},"contentPosition1Col":{"desktop":"bottom"},"contentPosition2Col":{"desktop":"bottom"}}'
        gp-href=""
        
        class="ggIaBMfgNa gp-group/hero gp-flex gp-flex-col gp-transition-colors gp-duration-200 gp-ease-in-out gp-w-full"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:flex;--d-mobile:flex;--d-tablet:flex;--op:100%;--h:494px;--h-tablet:494px;--h-mobile:460px;--w:100%;--w-tablet:100%;--w-mobile:100%"
        data-id="ggIaBMfgNa"
      >
        
      <div
        class="gp-relative gp-flex gp-flex-col gp-items-center gp-overflow-hidden gp_haslazybg !gp-justify-end tablet:!gp-justify-end mobile:!gp-justify-end"
        style="--h:494px;--h-tablet:494px;--h-mobile:460px;--bgc:var(--g-c-bg-2)"
      >
        
  <picture style="border-radius: inherit" class="gp-w-full">
  
      <source media="(max-width: 767px)" srcset="{{ "gempages_553400155311702965-d8744a09-3ff0-4043-a03a-1c86ee8aa245_768x.jpg" | file_url }}" />
      <source media="(max-width: 1024px)" srcset="{{ "gempages_553400155311702965-d8744a09-3ff0-4043-a03a-1c86ee8aa245_1024x.jpg" | file_url }}" />
      <img
        title
        class="adaptive-hero-banner"
        src="{{ "gempages_553400155311702965-d8744a09-3ff0-4043-a03a-1c86ee8aa245.jpg" | file_url }}"
        alt=""
        fetchpriority="high"
        style="--d:none;--d-tablet:none;--d-mobile:none;--h:494px;--h-tablet:494px;--h-mobile:460px;--w:100%;--w-tablet:100%;--w-mobile:100%;--op:0;--z:-1"
      />
    
  </picture>
        <div class="gp-absolute gp-w-full gp-h-full">
          <div
            class="gp-overflow-hidden gp-relative gp-w-full gp-h-full"
            >
            <div
              aria-label="Background Image"
              role="banner"
              class='gp-hero-banner-bg gp-absolute gp-overflow-hidden gp-w-full gp-h-full top-0 left-0'
              style="clip-path:inset(0 0 0)"
            >
            <div class="hero-banner-bg-parallax gp-hero-banner-image-background"
              style="--bgc:var(--g-c-bg-2);--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bgi:url('{{ "gempages_553400155311702965-d8744a09-3ff0-4043-a03a-1c86ee8aa245.jpg" | file_url }}');--bgi-tablet:url('{{ "gempages_553400155311702965-d8744a09-3ff0-4043-a03a-1c86ee8aa245_1024x.jpg" | file_url }}');--bgi-mobile:url('{{ "gempages_553400155311702965-d8744a09-3ff0-4043-a03a-1c86ee8aa245_768x.jpg" | file_url }}');--duration:0.5s;--scale:110;--pos:absolute;--pos-tablet:absolute;--pos-mobile:absolute;--h:100%;--h-tablet:100%;--h-mobile:100%;--w:100%;--w-tablet:100%;--w-mobile:100%;--top:;--top-tablet:;--top-mobile:"
            >
            
            
            </div></div>
          </div>
          <div
              aria-label="Overlay"
              role="banner"
              class="gp-absolute gp-inset-0 gp-left-[var(--left)] gp-top-[var(--top)] gp-transition-all gp-duration-300 group-hover/hero:gp-bg-[color:var(--hvr-bgc,_var(--bgc))] group-hover/hero:gp-opacity-[var(--hvr-op,_var(--op))]"
            style="--bgc:#121212;--top:0;--left:0;--op:60%">
            </div>
          </div>
          
       
      
    <div
      id data-id
        style="--cg:32px;--pc:end;--pc-tablet:end;--pc-mobile:end;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:var(--g-ct-w, 1200px);--w-mobile:var(--g-ct-w, 1200px)"
        class="gp-hero-banner-row gp-z-1 gp-w-full tablet:gp-relative mobile:gp-relative tablet:[&>*>*]:!gp-justify-undefined mobile:[&>*>*]:!gp-justify-undefined tablet:!gp-content-stretch tablet:[&>*]:!gp-justify-end mobile:!gp-content-stretch mobile:[&>*]:!gp-justify-end gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gcdGge2e78 gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="g-Z_rm-sD7" data-id="g-Z_rm-sD7"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pl:var(--g-s-xl);--pb:var(--g-s-xl);--pr:var(--g-s-xl);--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g-Z_rm-sD7 gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gkOBQTUUjz gp-relative gp-flex gp-flex-col"
    >
      
    
      
      
    <div
    data-id="gJXjdHZdKu"
      
      data-id="gJXjdHZdKu"
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-s);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll">

    <div
            class="gp-flex gp-flex-wrap"
            style="--cg:4px;--jc:left">
            <div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gpaOQ-H6ut ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] g1O82-J0fI"
    >
      <div 
      data-id="g1O82-J0fI"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg style="width: 100%; height: 100%" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gZi778nhNM ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gYXMYmrVRj"
    >
      <div 
      data-id="gYXMYmrVRj"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg style="width: 100%; height: 100%" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gueBAELyfZ ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] g-IGzs-ygl"
    >
      <div 
      data-id="g-IGzs-ygl"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg style="width: 100%; height: 100%" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gt2nQns9jK ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] g0my6AyCVb"
    >
      <div 
      data-id="g0my6AyCVb"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg style="width: 100%; height: 100%" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gpHe8_l0NI ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gU_t0VD4Aa"
    >
      <div 
      data-id="gU_t0VD4Aa"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg style="width: 100%; height: 100%" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div>
         </div>
    </div>
  
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g6ImBzoRZF">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="g6ImBzoRZF "
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#F6F6F6;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg6ImBzoRZF_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gvLboGYKHc">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gvLboGYKHc "
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:700;--ls:normal;--size:14px;--size-tablet:13px;--size-mobile:12px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--c:#FFFFFF;--tt:uppercase;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggvLboGYKHc_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
    </div>
    </div>
   
    
      </div>
    
        
      </gp-hero-banner>
    </div>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-hero-banner.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>

  
      </div>
    </div>
  
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      enableLazyloadImage="true" tag="CarouselItem" label="Carousel Item" type="component"
      style="--bgc:transparent;--minw:calc(100% / 4.5 - 12.444444444444445px);--minw-tablet:calc(100% / 1.5 - 5.333333333333333px);--minw-mobile:calc(100% / 1.5 - 5.333333333333333px);--maxw:calc(100% / 4.5 - 12.444444444444445px);--maxw-tablet:calc(100% / 1.5 - 5.333333333333333px);--maxw-mobile:calc(100% / 1.5 - 5.333333333333333px);--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--ml:0px;--ml-tablet:0px;--ml-mobile:0px;--pl:0px;--pr:0px;--pt:0px;--pb:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-gF5nsap-SC gQlgCMvq3l"
      data-index="4"
    >
      <div
        class="gp-w-full",
        style="--shadow:none"
      >
      
    <div class="gp-flex gp-w-full" style="--h:494px;--h-tablet:494px;--h-mobile:460px;--d:flex;--d-mobile:flex;--d-tablet:flex">
      <gp-hero-banner
        gp-data='{"target":"_blank","background":{"desktop":{"attachment":"scroll","color":"bg-2","image":{"src":"https://cdn.shopify.com/s/files/1/0733/7908/6636/files/gempages_553400155311702965-33be555e-8f3a-48a8-887c-03ec57830814.jpg","width":1080,"height":1440,"backupFileKey":"gempages_553400155311702965-33be555e-8f3a-48a8-887c-03ec57830814.jpg","storage":"FILE_CONTENT"},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"image"},"tablet":{"attachment":"scroll","color":"bg-2","image":{"src":"https://cdn.shopify.com/s/files/1/0733/7908/6636/files/gempages_553400155311702965-33be555e-8f3a-48a8-887c-03ec57830814.jpg","width":1080,"height":1440,"backupFileKey":"gempages_553400155311702965-33be555e-8f3a-48a8-887c-03ec57830814.jpg","storage":"FILE_CONTENT"},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"image"},"mobile":{"attachment":"scroll","color":"bg-2","image":{"src":"https://cdn.shopify.com/s/files/1/0733/7908/6636/files/gempages_553400155311702965-33be555e-8f3a-48a8-887c-03ec57830814.jpg","width":1080,"height":1440,"backupFileKey":"gempages_553400155311702965-33be555e-8f3a-48a8-887c-03ec57830814.jpg","storage":"FILE_CONTENT"},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"image"}},"uid":"gCHG_tIllh","enableParallax":false,"speedParallax":0,"hoverEffect":false,"hoverEffectScale":110,"layout":{"desktop":{"cols":[12],"display":"fill"}},"contentPosition1Col":{"desktop":"bottom"},"contentPosition2Col":{"desktop":"bottom"}}'
        gp-href=""
        
        class="gCHG_tIllh gp-group/hero gp-flex gp-flex-col gp-transition-colors gp-duration-200 gp-ease-in-out gp-w-full"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:flex;--d-mobile:flex;--d-tablet:flex;--op:100%;--h:494px;--h-tablet:494px;--h-mobile:460px;--w:100%;--w-tablet:100%;--w-mobile:100%"
        data-id="gCHG_tIllh"
      >
        
      <div
        class="gp-relative gp-flex gp-flex-col gp-items-center gp-overflow-hidden gp_haslazybg !gp-justify-end tablet:!gp-justify-end mobile:!gp-justify-end"
        style="--h:494px;--h-tablet:494px;--h-mobile:460px;--bblr:0px;--bbrr:0px;--btlr:0px;--btrr:0px;--bgc:var(--g-c-bg-2)"
      >
        
  <picture style="border-radius: inherit" class="gp-w-full">
  
      <source media="(max-width: 767px)" srcset="{{ "gempages_553400155311702965-33be555e-8f3a-48a8-887c-03ec57830814_768x.jpg" | file_url }}" />
      <source media="(max-width: 1024px)" srcset="{{ "gempages_553400155311702965-33be555e-8f3a-48a8-887c-03ec57830814_1024x.jpg" | file_url }}" />
      <img
        title
        class="adaptive-hero-banner"
        src="{{ "gempages_553400155311702965-33be555e-8f3a-48a8-887c-03ec57830814.jpg" | file_url }}"
        alt=""
        fetchpriority="high"
        style="--d:none;--d-tablet:none;--d-mobile:none;--h:494px;--h-tablet:494px;--h-mobile:460px;--w:100%;--w-tablet:100%;--w-mobile:100%;--op:0;--z:-1"
      />
    
  </picture>
        <div class="gp-absolute gp-w-full gp-h-full">
          <div
            class="gp-overflow-hidden gp-relative gp-w-full gp-h-full"
            >
            <div
              aria-label="Background Image"
              role="banner"
              class='gp-hero-banner-bg gp-absolute gp-overflow-hidden gp-w-full gp-h-full top-0 left-0'
              style="clip-path:inset(0 0 0)"
            >
            <div class="hero-banner-bg-parallax gp-hero-banner-image-background"
              style="--bgc:var(--g-c-bg-2);--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bgi:url('{{ "gempages_553400155311702965-33be555e-8f3a-48a8-887c-03ec57830814.jpg" | file_url }}');--bgi-tablet:url('{{ "gempages_553400155311702965-33be555e-8f3a-48a8-887c-03ec57830814_1024x.jpg" | file_url }}');--bgi-mobile:url('{{ "gempages_553400155311702965-33be555e-8f3a-48a8-887c-03ec57830814_768x.jpg" | file_url }}');--duration:0.5s;--scale:110;--pos:absolute;--pos-tablet:absolute;--pos-mobile:absolute;--h:100%;--h-tablet:100%;--h-mobile:100%;--w:100%;--w-tablet:100%;--w-mobile:100%;--top:;--top-tablet:;--top-mobile:"
            >
            
            
            </div></div>
          </div>
          <div
              aria-label="Overlay"
              role="banner"
              class="gp-absolute gp-inset-0 gp-left-[var(--left)] gp-top-[var(--top)] gp-transition-all gp-duration-300 group-hover/hero:gp-bg-[color:var(--hvr-bgc,_var(--bgc))] group-hover/hero:gp-opacity-[var(--hvr-op,_var(--op))]"
            style="--bgc:#121212;--top:0;--left:0;--op:75%">
            </div>
          </div>
          
       
      
    <div
      id data-id
        style="--cg:32px;--pc:end;--pc-tablet:end;--pc-mobile:end;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:var(--g-ct-w, 1200px);--w-mobile:var(--g-ct-w, 1200px)"
        class="gp-hero-banner-row gp-z-1 gp-w-full tablet:gp-relative mobile:gp-relative tablet:[&>*>*]:!gp-justify-undefined mobile:[&>*>*]:!gp-justify-undefined tablet:!gp-content-stretch tablet:[&>*]:!gp-justify-end mobile:!gp-content-stretch mobile:[&>*]:!gp-justify-end gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gRw43_lwY1 gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="gJ182jF9hS" data-id="gJ182jF9hS"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pl:var(--g-s-xl);--pb:var(--g-s-xl);--pr:var(--g-s-xl);--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gJ182jF9hS gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gVm8XM6Yfd gp-relative gp-flex gp-flex-col"
    >
      
    
      
      
    <div
    data-id="g-zbAWuzJA"
      
      data-id="g-zbAWuzJA"
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-s);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll">

    <div
            class="gp-flex gp-flex-wrap"
            style="--cg:4px;--jc:left">
            <div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="glvWyuWla3 ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] g_QHSJ4KUu"
    >
      <div 
      data-id="g_QHSJ4KUu"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg style="width: 100%; height: 100%" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="g7ej-ax1nQ ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gn_Au4EB5o"
    >
      <div 
      data-id="gn_Au4EB5o"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg style="width: 100%; height: 100%" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="g4mO33U9bn ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] g69bp7e2c4"
    >
      <div 
      data-id="g69bp7e2c4"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg style="width: 100%; height: 100%" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="guKHD5m0-O ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] g5xaz6OzF0"
    >
      <div 
      data-id="g5xaz6OzF0"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg style="width: 100%; height: 100%" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M115.584 494.176c-12.352 6.336 -26.368 -4.768 -23.872 -18.944l26.56 -151.36L5.536 216.48c-10.528 -10.048 -5.056 -28.416 9.056 -30.4l156.736 -22.272L241.216 25.344c6.304 -12.48 23.36 -12.48 29.664 0l69.888 138.464 156.736 22.272c14.112 1.984 19.584 20.352 9.024 30.4l-112.704 107.392 26.56 151.36c2.496 14.176 -11.52 25.28 -23.872 18.944L256 421.984l-140.448 72.192z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%" class="gYXBWrD4Lw ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
      class="gp-leading-[0] gznjJ2dEPX"
    >
      <div 
      data-id="gznjJ2dEPX"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--pl:0px;--pr:0px;--pt:0px;--pb:0px;--bs:none;--bw:0px;--bc:transparent"
      >
      <div
      
    >
      <span
        style="--c:#FAAD14;--t:rotate(0deg);--w:14px;--h:14px;--minw:14px;--height-desktop:14px;--height-tablet:14px;--height-mobile:14px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817759863701864">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M234.29,114.85l-45,38.83L203,211.75a16.4,16.4,0,0,1-24.5,17.82L128,198.49,77.47,229.57A16.4,16.4,0,0,1,53,211.75l13.76-58.07-45-38.83A16.46,16.46,0,0,1,31.08,86l59-4.76,22.76-55.08a16.36,16.36,0,0,1,30.27,0l22.75,55.08,59,4.76a16.46,16.46,0,0,1,9.37,28.86Z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div>
         </div>
    </div>
  
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gyGA6fRzWM">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gyGA6fRzWM "
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#FFFFFF;word-break:break-word;--bgc:rgba(77, 77, 77, 0);--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggyGA6fRzWM_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gIvy1nbS9P">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gIvy1nbS9P "
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-body, body);--weight:700;--ls:normal;--size:14px;--size-tablet:13px;--size-mobile:12px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;--c:#FFFFFF;--tt:uppercase;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggIvy1nbS9P_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
    </div>
    </div>
   
    
      </div>
    
        
      </gp-hero-banner>
    </div>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-hero-banner.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>

  
      </div>
    </div>
  
          </div>
          
    <button
      type="button"
      aria-label='Carousel Next Arrow'
      class="gp-z-1 gp-carousel-action-next gem-slider-next gF5nsap-SC-{{section.id}} gp-carousel-arrow-gF5nsap-SC gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-flex !gp-absolute gp-z-2 tablet:!gp-flex tablet:!gp-absolute tablet:gp-z-2 mobile:!gp-flex mobile:!gp-absolute mobile:gp-z-2"
      style="--left:initial;--top:;--right:16px;--bottom:initial;--left-tablet:initial;--top-tablet:;--right-tablet:16px;--bottom-tablet:initial;--left-mobile:initial;--top-mobile:;--right-mobile:16px;--bottom-mobile:initial;--d:flex;--d-tablet:flex;--d-mobile:flex;background-color:transparent;--w:32px;--h:32px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-0 tablet:gp-rotate-0 mobile:gp-rotate-0"
  style="--c:#000000;--w:24px;--w-tablet:24px;--w-mobile:24px;--h:24px;--h-tablet:24px;--h-mobile:24px"
  >
    <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M7.62204 5.13313C7.78476 4.95562 8.04858 4.95562 8.21129 5.13313L12.378 9.67859C12.5407 9.8561 12.5407 10.1439 12.378 10.3214L8.21129 14.8669C8.04858 15.0444 7.78476 15.0444 7.62204 14.8669C7.45932 14.6894 7.45932 14.4016 7.62204 14.224L11.4941 10L7.62204 5.77596C7.45932 5.59845 7.45932 5.31064 7.62204 5.13313Z"/>
              </svg>
              
    </div>
      <style>
    .gp-carousel-arrow-gF5nsap-SC {
      border-radius: var(--g-radius-small);
    }
    .gp-carousel-arrow-gF5nsap-SC::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      border-radius: var(--g-radius-small);
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .gp-carousel-arrow-gF5nsap-SC {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-gF5nsap-SC::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
    @media only screen and (max-width: 768px) {
      .gp-carousel-arrow-gF5nsap-SC {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-gF5nsap-SC::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
  </style>
    </button>
  
        </div>
        <div
    class="gp-items-center gp-justify-center gp-gap-2 gp-text-center gp-carousel-dot-container gp-carousel-dot-container-gF5nsap-SC-{{section.id}} gp-static gp-flex-row gp-left-0 gp-right-0 tablet:gp-static tablet:gp-flex-row tablet:gp-left-0 tablet:gp-right-0 mobile:gp-static mobile:gp-flex-row mobile:gp-left-0 mobile:gp-right-0"
    style="--mt:16px;--mt-tablet:16px;--mt-mobile:16px;--d:flex;--d-tablet:flex;--d-mobile:flex"
 ></div>
      </div>
    </gp-carousel>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-carousel-v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>

  
    </div>
    </div>
  
            </section>
           
    
<style {% if section.settings.section_preload == "false" %} class="gps-link" type="text/disabled-css" {% endif %}>@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: normal;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmUiAw.woff) format('woff');
}
/* cyrillic-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmZiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmQiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmYiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmXiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* math */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalnoiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;
}
/* symbols */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjaln6iAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;
}
/* vietnamese */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmbiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmaiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmUiAz0klQmz24.woff) format('woff');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
</style>

{% schema %}
  {
    
    "name": "Section 5",
    "tag": "section",
    "class": "gps-555482666422502210 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/gardpro-us/apps/gempages-cro/app/shopify/edit?pageType=GP_STATIC&editorId=555482666321838914&sectionId=555482666422502210)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggFyXFra_Ly_text","label":"ggFyXFra_Ly_text","default":"WHAT DO OUR ADVENTURERS SAY?"},{"type":"html","id":"ggCfGbdmYjm_text","label":"ggCfGbdmYjm_text","default":"<p>\"Easy and quick buying process. It was dispatched within one day, and they kept me updated every step of the way. This waterproof wristwatch is everything I wanted and more—streamlined, stylish, and efficient. I absolutely love it!\"</p>"},{"type":"html","id":"ggFn6MfQIJS_text","label":"ggFn6MfQIJS_text","default":"<p>- Myrianthi Shapanis (vERIFIED BUYER)</p>"},{"type":"html","id":"ggtMx5-hxZj_text","label":"ggtMx5-hxZj_text","default":"<p>\"I just purchased the Ultra with the green band. The features and the pricing are great. I'm also impressed with the ruggedness and shockproofing built into this women's smartwatch. I have a small wrist, but it still looks great on!\"</p>"},{"type":"html","id":"ggMa8OwHkBi_text","label":"ggMa8OwHkBi_text","default":"<p>- MARY SARRO (vERIFIED BUYER)</p>"},{"type":"html","id":"ggAN0m73OB6_text","label":"ggAN0m73OB6_text","default":"<p>\"This watch has all the features I was looking for and more. Easy to read and so helpful with detailed instructions when I needed them. This ladies' sports watch is perfect for tracking workouts and staying on top of my health goals. I look forward to using all the features in one place!\"</p>"},{"type":"html","id":"ggywryYmKo1_text","label":"ggywryYmKo1_text","default":"<p>- Charlotte (vERIFIED BUYER)</p>"},{"type":"html","id":"gg6ImBzoRZF_text","label":"gg6ImBzoRZF_text","default":"<p>\"I love my watch….. I used all the features from pulse to blood pressure sats and it even monitors sleep, deep sleep, light sleep and REM time…. Its awesome!!! I love it!\"</p>"},{"type":"html","id":"ggvLboGYKHc_text","label":"ggvLboGYKHc_text","default":"<p>- JERALYNN G (vERIFIED BUYER)</p>"},{"type":"html","id":"ggyGA6fRzWM_text","label":"ggyGA6fRzWM_text","default":"<p><span style=\"background-color:rgba(0, 0, 0, 0);color:#F6F6F6;\">\"</span><span style=\"background-color:rgba(0, 0, 0, 0);color:#F6F6F6;font-size:16px;\">Love my new watch! Very easy to use once I worked it all out. I especially like that I can personalise the digital face with a photograph of choice.\"</span></p>"},{"type":"html","id":"ggIvy1nbS9P_text","label":"ggIvy1nbS9P_text","default":"<p>- VAL M (vERIFIED BUYER)</p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
