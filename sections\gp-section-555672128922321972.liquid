

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-555672128922321972.gps.gpsil [style*="--ai:"]{align-items:var(--ai)}.gps-555672128922321972.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-555672128922321972.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-555672128922321972.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-555672128922321972.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-555672128922321972.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-555672128922321972.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-555672128922321972.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-555672128922321972.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-555672128922321972.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-555672128922321972.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-555672128922321972.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-555672128922321972.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-555672128922321972.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-555672128922321972.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-555672128922321972.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-555672128922321972.gps.gpsil [style*="--bl:"]{border-left:var(--bl)}.gps-555672128922321972.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-555672128922321972.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-555672128922321972.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-555672128922321972.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-555672128922321972.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-555672128922321972.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-555672128922321972.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-555672128922321972.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-555672128922321972.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-555672128922321972.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-555672128922321972.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-555672128922321972.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-555672128922321972.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-555672128922321972.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-555672128922321972.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-555672128922321972.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-555672128922321972.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-555672128922321972.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-555672128922321972.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-555672128922321972.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-555672128922321972.gps.gpsil [style*="--mt:"]{margin-top:var(--mt)}.gps-555672128922321972.gps.gpsil [style*="--maxw:"]{max-width:var(--maxw)}.gps-555672128922321972.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-555672128922321972.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-555672128922321972.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-555672128922321972.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-555672128922321972.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-555672128922321972.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-555672128922321972.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-555672128922321972.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-555672128922321972.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-555672128922321972.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-555672128922321972.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-555672128922321972.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-555672128922321972.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-555672128922321972.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-555672128922321972.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-555672128922321972.gps.gpsil [style*="--aspect-tablet:"]{aspect-ratio:var(--aspect-tablet)}.gps-555672128922321972.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-555672128922321972.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-555672128922321972.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-555672128922321972.gps.gpsil [style*="--maxw-tablet:"]{max-width:var(--maxw-tablet)}.gps-555672128922321972.gps.gpsil [style*="--minw-tablet:"]{min-width:var(--minw-tablet)}.gps-555672128922321972.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-555672128922321972.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-555672128922321972.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-555672128922321972.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-555672128922321972.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-555672128922321972.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-555672128922321972.gps.gpsil [style*="--aspect-mobile:"]{aspect-ratio:var(--aspect-mobile)}.gps-555672128922321972.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-555672128922321972.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-555672128922321972.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-555672128922321972.gps.gpsil [style*="--jc-mobile:"]{justify-content:var(--jc-mobile)}.gps-555672128922321972.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-555672128922321972.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-555672128922321972.gps.gpsil [style*="--mt-mobile:"]{margin-top:var(--mt-mobile)}.gps-555672128922321972.gps.gpsil [style*="--maxw-mobile:"]{max-width:var(--maxw-mobile)}.gps-555672128922321972.gps.gpsil [style*="--minw-mobile:"]{min-width:var(--minw-mobile)}.gps-555672128922321972.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-555672128922321972.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-555672128922321972.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-555672128922321972.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-555672128922321972.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-555672128922321972.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-555672128922321972 .gp-g-paragraph-1{font-family:var(--g-p1-ff);font-size:var(--g-p1-size);font-style:var(--g-p1-fs);font-weight:var(--g-p1-weight);letter-spacing:var(--g-p1-ls);line-height:var(--g-p1-lh)}.gps-555672128922321972 .\!gp-relative{position:relative!important}.gps-555672128922321972 .gp-relative{position:relative}.gps-555672128922321972 .gp-z-1{z-index:1}.gps-555672128922321972 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-555672128922321972 .gp-mb-0{margin-bottom:0}.gps-555672128922321972 .gp-flex{display:flex}.gps-555672128922321972 .gp-inline-flex{display:inline-flex}.gps-555672128922321972 .gp-grid{display:grid}.gps-555672128922321972 .gp-contents{display:contents}.gps-555672128922321972 .\!gp-hidden{display:none!important}.gps-555672128922321972 .gp-hidden{display:none}.gps-555672128922321972 .gp-h-auto{height:auto}.gps-555672128922321972 .gp-h-full{height:100%}.gps-555672128922321972 .gp-w-full{width:100%}.gps-555672128922321972 .gp-max-w-full{max-width:100%}.gps-555672128922321972 .gp-flex-none{flex:none}.gps-555672128922321972 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-555672128922321972 .gp-flex-col{flex-direction:column}.gps-555672128922321972 .gp-flex-wrap{flex-wrap:wrap}.gps-555672128922321972 .gp-items-center{align-items:center}.gps-555672128922321972 .gp-justify-center{justify-content:center}.gps-555672128922321972 .gp-gap-y-0{row-gap:0}.gps-555672128922321972 .gp-overflow-hidden{overflow:hidden}.gps-555672128922321972 .gp-break-words{overflow-wrap:break-word}.gps-555672128922321972 .gp-rounded-none{border-radius:0}.gps-555672128922321972 .gp-text-center{text-align:center}.gps-555672128922321972 .gp-leading-\[0\]{line-height:0}.gps-555672128922321972 .gp-text-g-text-3{color:var(--g-c-text-3)}.gps-555672128922321972 .gp-no-underline{text-decoration-line:none}.gps-555672128922321972 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555672128922321972 .gp-duration-200{transition-duration:.2s}.gps-555672128922321972 .gp-duration-300{transition-duration:.3s}.gps-555672128922321972 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555672128922321972 .disabled\:gp-btn-disabled:disabled{cursor:default}.gps-555672128922321972 .disabled\:gp-pointer-events-none:disabled{pointer-events:none}.gps-555672128922321972 .disabled\:gp-opacity-30:disabled{opacity:.3}@media (hover:hover) and (pointer:fine){.gps-555672128922321972 .gp-group\/button:hover .group-hover\/button\:\!gp-text-inherit{color:inherit!important}}.gps-555672128922321972 .gp-group\/button:active .group-active\/button\:\!gp-text-inherit{color:inherit!important}.gps-555672128922321972 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-invisible{visibility:hidden}@media (max-width:1024px){.gps-555672128922321972 .tablet\:\!gp-hidden{display:none!important}.gps-555672128922321972 .tablet\:gp-hidden{display:none}.gps-555672128922321972 .tablet\:gp-h-auto{height:auto}.gps-555672128922321972 .tablet\:gp-flex-none{flex:none}}@media (max-width:767px){.gps-555672128922321972 .mobile\:\!gp-hidden{display:none!important}.gps-555672128922321972 .mobile\:gp-hidden{display:none}.gps-555672128922321972 .mobile\:gp-h-auto{height:auto}.gps-555672128922321972 .mobile\:gp-flex-none{flex:none}}.gps-555672128922321972 .\[\&\>svg\]\:\!gp-h-\[var\(--height-desktop\)\]>svg{height:var(--height-desktop)!important}.gps-555672128922321972 .\[\&\>svg\]\:\!gp-w-auto>svg{width:auto!important}@media (max-width:1024px){.gps-555672128922321972 .tablet\:\[\&\>svg\]\:\!gp-h-\[var\(--height-tablet\)\]>svg{height:var(--height-tablet)!important}}@media (max-width:767px){.gps-555672128922321972 .mobile\:\[\&\>svg\]\:\!gp-h-\[var\(--height-mobile\)\]>svg{height:var(--height-mobile)!important}}.gps-555672128922321972 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-555672128922321972 .\[\&_p\]\:gp-inline p{display:inline}.gps-555672128922321972 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}.gps-555672128922321972 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-555672128922321972 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      enableLazyloadImage="true" id="gH8xPd5NI7" data-id="gH8xPd5NI7"
        style="--blockPadding:base;--pt:84px;--pb:var(--g-s-2xl);--pt-tablet:84px;--pl-tablet:24px;--pb-tablet:var(--g-s-2xl);--pr-tablet:24px;--pt-mobile:48px;--pl-mobile:24px;--pb-mobile:var(--g-s-2xl);--pr-mobile:24px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gH8xPd5NI7 gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      enableLazyloadImage="true" tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gUfzizSAAQ gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="gcZS07ekvo" data-id="gcZS07ekvo"
        style="--mb:var(--g-s-l);--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:8px;--pc:start;--gtc:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gcZS07ekvo gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      enableLazyloadImage="true" tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gxq_aJGf4D gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="gWn3nTePXz" data-id="gWn3nTePXz"
        style="--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:50px;--pc:start;--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, auto);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gWn3nTePXz gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      enableLazyloadImage="true" tag="Col" label="Block" type="component"
      style="--jc:start"
      class="guheB2jx3e gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="g33_rquOFd" data-id="g33_rquOFd"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--pl:0px;--pr:0px;--pt-mobile:0px;--pl-mobile:0px;--pb-mobile:0px;--pr-mobile:0px;--pt-tablet:0px;--pl-tablet:15px;--pb-tablet:var(--g-s-3xl);--pr-tablet:15px;--cg:30px;--pc:start;--gtc:minmax(0, 12fr);--w:500px;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g33_rquOFd gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="grXLdvOcdt gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="gCkfQDB_7M" data-id="gCkfQDB_7M"
        style="--mb:var(--g-s-l);--mb-mobile:13px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:12px;--pc:start;--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, auto) minmax(0, auto);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gCkfQDB_7M gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      enableLazyloadImage="true" tag="Col" label="Block" type="component"
      style="--jc:start;--jc-mobile:center"
      class="gQCNKHBeEe gp-relative gp-flex gp-flex-col"
    >
      
    
    <div
    data-id="g127j5qwI-"
      
      data-id="g127j5qwI-"
      style="--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll">

    <div
            class="gp-flex gp-flex-wrap"
            style="--cg:4px;--jc:left;--ai:start">
            <div gp-el-wrapper style="--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%" class="gRv4xFJWl0 ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ta:left"
      class="gp-leading-[0] gyF_PbpDSC"
    >
      <div 
      data-id="gyF_PbpDSC"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:#FFB22D;--t:rotate(0deg);--w:22px;--w-tablet:22px;--w-mobile:22px;--h:22px;--h-tablet:22px;--h-mobile:22px;--minw:22px;--minw-tablet:22px;--minw-mobile:22px;--height-desktop:22px;--height-tablet:22px;--height-mobile:22px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" data-id="508414815326568800" viewBox="0 0 256 256"><path d="m234.29 114.85-45 38.83L203 211.75a16.4 16.4 0 0 1-24.5 17.82L128 198.49l-50.53 31.08A16.4 16.4 0 0 1 53 211.75l13.76-58.07-45-38.83A16.46 16.46 0 0 1 31.08 86l59-4.76 22.76-55.08a16.36 16.36 0 0 1 30.27 0l22.75 55.08 59 4.76a16.46 16.46 0 0 1 9.37 28.86Z"/></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%" class="gB0nHHD0Hv ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--mb:0px;--ta:left"
      class="gp-leading-[0] gjWukT8Kjx"
    >
      <div 
      data-id="gjWukT8Kjx"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:#ffb22d;--t:rotate(0deg);--w:22px;--w-tablet:22px;--w-mobile:22px;--h:22px;--h-tablet:22px;--h-mobile:22px;--minw:22px;--minw-tablet:22px;--minw-mobile:22px;--height-desktop:22px;--height-tablet:22px;--height-mobile:22px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" data-id="508414815326568800" viewBox="0 0 256 256"><path d="m234.29 114.85-45 38.83L203 211.75a16.4 16.4 0 0 1-24.5 17.82L128 198.49l-50.53 31.08A16.4 16.4 0 0 1 53 211.75l13.76-58.07-45-38.83A16.46 16.46 0 0 1 31.08 86l59-4.76 22.76-55.08a16.36 16.36 0 0 1 30.27 0l22.75 55.08 59 4.76a16.46 16.46 0 0 1 9.37 28.86Z"/></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%" class="g_Kan1QuXB ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ta:left"
      class="gp-leading-[0] gSQ1cqDFOH"
    >
      <div 
      data-id="gSQ1cqDFOH"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:#ffb22d;--t:rotate(0deg);--w:22px;--w-tablet:22px;--w-mobile:22px;--h:22px;--h-tablet:22px;--h-mobile:22px;--minw:22px;--minw-tablet:22px;--minw-mobile:22px;--height-desktop:22px;--height-tablet:22px;--height-mobile:22px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" data-id="508414815326568800" viewBox="0 0 256 256"><path d="m234.29 114.85-45 38.83L203 211.75a16.4 16.4 0 0 1-24.5 17.82L128 198.49l-50.53 31.08A16.4 16.4 0 0 1 53 211.75l13.76-58.07-45-38.83A16.46 16.46 0 0 1 31.08 86l59-4.76 22.76-55.08a16.36 16.36 0 0 1 30.27 0l22.75 55.08 59 4.76a16.46 16.46 0 0 1 9.37 28.86Z"/></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%" class="giK-2Qko1u ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ta:left"
      class="gp-leading-[0] gqzIcbdK3G"
    >
      <div 
      data-id="gqzIcbdK3G"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:#ffb22d;--t:rotate(0deg);--w:22px;--w-tablet:22px;--w-mobile:22px;--h:22px;--h-tablet:22px;--h-mobile:22px;--minw:22px;--minw-tablet:22px;--minw-mobile:22px;--height-desktop:22px;--height-tablet:22px;--height-mobile:22px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="currentColor" data-id="508414815326568800" viewBox="0 0 256 256"><path d="m234.29 114.85-45 38.83L203 211.75a16.4 16.4 0 0 1-24.5 17.82L128 198.49l-50.53 31.08A16.4 16.4 0 0 1 53 211.75l13.76-58.07-45-38.83A16.46 16.46 0 0 1 31.08 86l59-4.76 22.76-55.08a16.36 16.36 0 0 1 30.27 0l22.75 55.08 59 4.76a16.46 16.46 0 0 1 9.37 28.86Z"/></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div><div gp-el-wrapper style="--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%" class="g8v4Y-rMwW ">
      
    <div 
      
      class="gp-flex gp-w-full gp-flex-col gp-items-center"
    >
    
  <div
      
      style="--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ta:left"
      class="gp-leading-[0] gMR6eRo_ib"
    >
      <div 
      data-id="gMR6eRo_ib"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:#ffb22d;--t:rotate(0deg);--w:22px;--w-tablet:22px;--w-mobile:22px;--h:22px;--h-tablet:22px;--h-mobile:22px;--minw:22px;--minw-tablet:22px;--minw-mobile:22px;--height-desktop:22px;--height-tablet:22px;--height-mobile:22px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817759863701864">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M234.29,114.85l-45,38.83L203,211.75a16.4,16.4,0,0,1-24.5,17.82L128,198.49,77.47,229.57A16.4,16.4,0,0,1,53,211.75l13.76-58.07-45-38.83A16.46,16.46,0,0,1,31.08,86l59-4.76,22.76-55.08a16.36,16.36,0,0,1,30.27,0l22.75,55.08,59,4.76a16.46,16.46,0,0,1,9.37,28.86Z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div>
  
      </div>
         </div>
    </div>
  
    </div><div
      enableLazyloadImage="true" tag="Col" label="Block" type="component"
      style="--jc:start;--jc-mobile:center"
      class="gAy9FdWDPY gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g9qvbPcW-g">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="g9qvbPcW-g "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:0px;--mt-mobile:var(--g-s-l);--mb-mobile:0px"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:18px;--size-tablet:18px;--size-mobile:16px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.gg9qvbPcW-g_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gyAxVweGV0">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gyAxVweGV0 "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:var(--g-s-l);--mb-mobile:0px;--pl-mobile:auto;--pr-mobile:auto"
      >
      <div class="gp-flex" style="--jc:center">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--c:#242424;--fs:normal;--ff:var(--g-font-heading, heading);--weight:600;--ls:normal;--size:37px;--size-tablet:30px;--size-mobile:24px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.ggyAxVweGV0_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gPoDRKi8ng">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gPoDRKi8ng "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:29px;--mt-mobile:var(--g-s-l);--mb-mobile:25px"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#575757;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:18px;--size-tablet:18px;--size-mobile:16px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.ggPoDRKi8ng_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  <gp-button >
  <div
    style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:24px;--ta:left"
    
  >
    <style>
    .gtBeWNd9Vu.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 0px;
  border-color: transparent;
  
      
      border-bottom-left-radius: 8px;
      border-bottom-right-radius: 8px;
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
      
    }

    .gtBeWNd9Vu:hover::before {
      
      
      border-bottom-left-radius: 0px;
      border-bottom-right-radius: 0px;
      border-top-left-radius: 0px;
      border-top-right-radius: 0px;
      
    }

    .gtBeWNd9Vu:hover .gp-button-icon {
      color: undefined;
    }

     .gtBeWNd9Vu .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gtBeWNd9Vu:hover .gp-button-price {
      color: undefined;
    }

    .gtBeWNd9Vu .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gtBeWNd9Vu .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gtBeWNd9Vu:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#g5UWRTEtPs" target="_self" data-id="gtBeWNd9Vu" aria-label="<p>SHOP THE FEMALE COLLECTION</p>"
      
      data-state="idle"
      class="gtBeWNd9Vu gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center"
      style="--hvr-bg:#1180FF;--bg:#096de3;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--w:Auto;--w-tablet:Auto;--w-mobile:Auto;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:32px;--pr:32px;--pt:12px;--pb:12px;--pl-tablet:32px;--pr-tablet:32px;--pt-tablet:12px;--pb-tablet:12px;--pl-mobile:32px;--pr-mobile:32px;--pt-mobile:12px;--pb-mobile:12px;--c:var(--g-c-text-3, text-3);--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:150%;--fs:normal;--weight:600;--ls:1px;--lh-tablet:150%"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:16px;--size-tablet:16px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggtBeWNd9Vu_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>
 
  <script src="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-marquee.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  <gp-marquee
   data-id="gCyi0FzZkp"
    
  gp-data='{"setting":{"childItem":["Item 1","Item 2","Item 3","Item 4"],"itemWidthType":{"desktop":"FIT_CONTENT","tablet":"FIT_CONTENT","mobile":"FIT_CONTENT"},"activeItem":"0","hoverItem":"0","isShowIconSeparator":false,"iconSeparatorSvg":"<svg height=\"100%\" width=\"100%\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 256 256\" fill=\"currentColor\">\n              <path fill=\"currentColor\" strokelinecap=\"round\" strokelinejoin=\"round\" d=\"M232,128A104,104,0,1,1,128,24,104.13,104.13,0,0,1,232,128Z\"></path></svg>","stopOnHover":false,"speed":"0.4","direction":"left","isPreview":false,"hasItemShadow":false,"uid":"gCyi0FzZkp"},"styles":{"itemSpacing":{"desktop":"25px","tablet":"56px","mobile":"56px"},"itemMaxWidth":{"desktop":"300px","tablet":"300px","mobile":"300px"},"iconSeparatorSize":{"desktop":24,"tablet":24,"mobile":24},"iconSeparatorColor":"#0C0C0C","sizeSetting":{"desktop":{"shapeLinked":false,"width":"100%","height":"auto"}},"itemBorderStyle":{"borderType":"none","border":"none","borderWidth":"1px","width":"1px 1px 1px 1px","isCustom":false,"color":"#000000"},"itemCorner":{"radiusType":"none"},"itemBackgroundColor":{"desktop":"transparent"},"backgroundColor":{"desktop":"transparent"},"align":{"desktop":"center"},"itemShadow":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"#121212","angle":90}}}'
  class="gCyi0FzZkp"
  >
   <div
      class="gp-flex gp-w-full gp-relative"
      style="--jc:center"
    >
  <div class="!gp-relative gp-overflow-hidden" style="--h:auto;--h-tablet:auto;--h-mobile:auto;--w:100%;--w-tablet:100%;--w-mobile:100%;--bg:transparent;--mb:17px;--pt:12px;--pb:12px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%">
        <div
    class="gem-marquee gp-overflow-hidden gp-w-full gp-h-full"
     
    >
     <div class="gp-overflow-hidden gp-w-full gp-h-full">
       
   <div
      style="--pause-on-hover:running;--pause-on-click:running;--width:100%;--transform:none;overflow:hidden;min-width:100%"
      class="rfm-marquee-container"
    >
      <div
        class="rfm-marquee"
        style="--play:running;--direction:normal;--delay:0s;--iteration-count:infinite;--min-width:auto;--w:auto;--w-tablet:auto;--w-mobile:auto;--minw:auto;--minw-tablet:auto;--minw-mobile:auto"
      >
        <div class="rfm-initial-child-container">
          
            <div style="--transform:none;--w:auto;--w-tablet:auto;--w-mobile:auto;--minw:auto;--minw-tablet:auto;--minw-mobile:auto" class="rfm-child">
               <div class="gp-flex gem-child-marquee-item gp-items-center" style="[object Object]">
       <div
                  class="gp-inline-flex gp-items-center gp-relative"
                  style="--pr:25px;--pr-tablet:56px;--pr-mobile:56px;text-wrap:wrap;--w:auto;--w-tablet:auto;--w-mobile:auto;--minw:auto;--minw-tablet:auto;--minw-mobile:auto;--maxw:unset;--maxw-tablet:unset;--maxw-mobile:unset"
                >
                  <div
      class="ghlEOrZ3b4 gem-marquee-item gem-marquee-item-ghlEOrZ3b4 gp-overflow-hidden"
      style="--bg:transparent;--b:none;--bw:1px 1px 1px 1px;--bc:#000000;--shadow:none;--w:calc(100%);--w-tablet:calc(100%);--w-mobile:calc(100%)"
    >
    
     
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gfA571gy87">
    <div
      enableLazyloadImage="true" tag="Text" label="Text" type="component" isNotFullWidth="true" isNotLazyload="true"
        class="gfA571gy87 "
        style="--ta:left;--pt:8px;--pb:8px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--weight:bold;--c:var(--g-c-text-1, text-1);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggfA571gy87_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
                   
                </div> <div
                  class="gp-inline-flex gp-items-center gp-relative"
                  style="--pr:25px;--pr-tablet:56px;--pr-mobile:56px;text-wrap:wrap;--w:auto;--w-tablet:auto;--w-mobile:auto;--minw:auto;--minw-tablet:auto;--minw-mobile:auto;--maxw:unset;--maxw-tablet:unset;--maxw-mobile:unset"
                >
                  <div
      class="gmjCGL7izZ gem-marquee-item gem-marquee-item-gmjCGL7izZ gp-overflow-hidden"
      style="--bg:transparent;--b:none;--bw:1px 1px 1px 1px;--bc:#000000;--shadow:none;--w:calc(100%);--w-tablet:calc(100%);--w-mobile:calc(100%)"
    >
    
     
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gjc0EZac29">
    <div
      enableLazyloadImage="true" tag="Text" label="Text" type="component" isNotFullWidth="true" isNotLazyload="true"
        class="gjc0EZac29 "
        style="--ta:left;--pt:8px;--pb:8px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--weight:bold;--c:var(--g-c-text-1, text-1);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggjc0EZac29_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
                   
                </div> <div
                  class="gp-inline-flex gp-items-center gp-relative"
                  style="--pr:25px;--pr-tablet:56px;--pr-mobile:56px;text-wrap:wrap;--w:auto;--w-tablet:auto;--w-mobile:auto;--minw:auto;--minw-tablet:auto;--minw-mobile:auto;--maxw:unset;--maxw-tablet:unset;--maxw-mobile:unset"
                >
                  <div
      class="g9FnHsEK3c gem-marquee-item gem-marquee-item-g9FnHsEK3c gp-overflow-hidden"
      style="--bg:transparent;--b:none;--bw:1px 1px 1px 1px;--bc:#000000;--shadow:none;--w:calc(100%);--w-tablet:calc(100%);--w-mobile:calc(100%)"
    >
    
     
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g0NfbDZINY">
    <div
      enableLazyloadImage="true" tag="Text" label="Text Block" type="component" isNotFullWidth="true" isNotLazyload="true"
        class="g0NfbDZINY "
        style="--ta:left;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--weight:bold;--c:var(--g-c-text-1, text-1);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.gg0NfbDZINY_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
      <style id="custom-css-g0NfbDZINY">
        .g0NfbDZINY {

}
.g0NfbDZINY p {

}
      </style>
    
    </div>
                   
                </div> <div
                  class="gp-inline-flex gp-items-center gp-relative"
                  style="--pr:25px;--pr-tablet:56px;--pr-mobile:56px;text-wrap:wrap;--w:auto;--w-tablet:auto;--w-mobile:auto;--minw:auto;--minw-tablet:auto;--minw-mobile:auto;--maxw:unset;--maxw-tablet:unset;--maxw-mobile:unset"
                >
                  <div
      class="gPRAl8KGlI gem-marquee-item gem-marquee-item-gPRAl8KGlI gp-overflow-hidden"
      style="--bg:transparent;--b:none;--bw:1px 1px 1px 1px;--bc:#000000;--shadow:none;--w:calc(100%);--w-tablet:calc(100%);--w-mobile:calc(100%)"
    >
    
     
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gcG4JtcXuw">
    <div
      enableLazyloadImage="true" tag="Text" label="Text" type="component" isNotFullWidth="true" isNotLazyload="true"
        class="gcG4JtcXuw "
        style="--ta:left;--pt:8px;--pb:8px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--weight:bold;--c:var(--g-c-text-1, text-1);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggcG4JtcXuw_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
                   
                </div>
    </div>
            </div>
          
        </div>
      </div>
      <div
        class="rfm-marquee placeholder-marquee"
        style="--play:running;--direction:normal;--delay:0s;--iteration-count:infinite;--min-width:auto;--w:auto;--w-tablet:auto;--w-mobile:auto;--minw:auto;--minw-tablet:auto;--minw-mobile:auto"
      ></div>
    </div>
  
     </div>
    </div>
  
  </div>
   </div>
  </gp-marquee>

    </div>
    </div>
   
    
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="gv8GN9eRSF" data-id="gv8GN9eRSF"
        style="--d:none;--d-tablet:none;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:8px;--pc:start;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 6fr) minmax(0, 6fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gv8GN9eRSF gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      enableLazyloadImage="true" tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gi-zUb0DCy gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gdnxVnpChM"
    role="presentation"
    class="gp-group/image gdnxVnpChM gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ta:center"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_553400155311702965-92087396-f8f3-41b9-bca0-426680f5dd90.png" | file_url }}" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTI4IiBoZWlnaHQ9IjEzODQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTkyOC0xMzg0Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSI5MjgiIGhlaWdodD0iMTM4NCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iOTI4IiBoZWlnaHQ9IjEzODQiIGZpbGw9InVybCgjZy05MjgtMTM4NCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTkyOCIgdG89IjkyOCIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPgogIDwvc3ZnPg==" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_553400155311702965-92087396-f8f3-41b9-bca0-426680f5dd90.png" | file_url }}" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTI4IiBoZWlnaHQ9IjEzODQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTkyOC0xMzg0Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSI5MjgiIGhlaWdodD0iMTM4NCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iOTI4IiBoZWlnaHQ9IjEzODQiIGZpbGw9InVybCgjZy05MjgtMTM4NCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTkyOCIgdG89IjkyOCIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPgogIDwvc3ZnPg==" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex gp_lazyload"
        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTI4IiBoZWlnaHQ9IjEzODQiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTkyOC0xMzg0Ij4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSI5MjgiIGhlaWdodD0iMTM4NCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iOTI4IiBoZWlnaHQ9IjEzODQiIGZpbGw9InVybCgjZy05MjgtMTM4NCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTkyOCIgdG89IjkyOCIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPgogIDwvc3ZnPg=="
        data-src="{{ "gempages_553400155311702965-92087396-f8f3-41b9-bca0-426680f5dd90.png" | file_url }}"
        width="100%"
        alt=""
        style="--aspect:auto;--aspect-tablet:auto;--aspect-mobile:auto;--objf:cover;--h:auto;--h-tablet:auto;--h-mobile:auto;--b:none;--bc:#000000;--bw:1px 1px 1px 1px;--btrr:12px;--bblr:12px;--bbrr:12px;--btlr:12px;--radiusType:custom;--shadow:none"
      />
    
  </picture>
      </div>
  </div>
    </div><div
      enableLazyloadImage="true" tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gY1HpDsyIB gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="gOA_LnxbE0"
    role="presentation"
    class="gp-group/image gOA_LnxbE0 gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ta:center"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" data-srcSet="{{ "gempages_553400155311702965-be5b3214-feef-462d-95c5-45b2abf3228e.png" | file_url }}" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTI4IiBoZWlnaHQ9IjEzOTAiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTkyOC0xMzkwIj4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSI5MjgiIGhlaWdodD0iMTM5MCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iOTI4IiBoZWlnaHQ9IjEzOTAiIGZpbGw9InVybCgjZy05MjgtMTM5MCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTkyOCIgdG89IjkyOCIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPgogIDwvc3ZnPg==" />
      <source media="(max-width: 1024px)" data-srcSet="{{ "gempages_553400155311702965-be5b3214-feef-462d-95c5-45b2abf3228e.png" | file_url }}" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTI4IiBoZWlnaHQ9IjEzOTAiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTkyOC0xMzkwIj4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSI5MjgiIGhlaWdodD0iMTM5MCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iOTI4IiBoZWlnaHQ9IjEzOTAiIGZpbGw9InVybCgjZy05MjgtMTM5MCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTkyOCIgdG89IjkyOCIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPgogIDwvc3ZnPg==" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex gp_lazyload"
        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTI4IiBoZWlnaHQ9IjEzOTAiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTkyOC0xMzkwIj4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSI5MjgiIGhlaWdodD0iMTM5MCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iOTI4IiBoZWlnaHQ9IjEzOTAiIGZpbGw9InVybCgjZy05MjgtMTM5MCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTkyOCIgdG89IjkyOCIgZHVyPSIxcyIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiICAvPgogIDwvc3ZnPg=="
        data-src="{{ "gempages_553400155311702965-be5b3214-feef-462d-95c5-45b2abf3228e.png" | file_url }}"
        width="100%"
        alt=""
        style="--aspect:auto;--aspect-tablet:auto;--aspect-mobile:auto;--objf:cover;--h:auto;--h-tablet:auto;--h-mobile:auto;--b:none;--bc:#000000;--bw:1px 1px 1px 1px;--btrr:12px;--bblr:12px;--bbrr:12px;--btlr:12px;--radiusType:custom;--shadow:none"
      />
    
  </picture>
      </div>
  </div>
    </div>
    </div>
   
    
    </div><div
      enableLazyloadImage="true" tag="Col" label="Block" type="component"
      style="--jc:start"
      class="g8KjMG8zBE gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="gY5-4bdcXL" data-id="gY5-4bdcXL"
        style="--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:8px;--pc:start;--gtc:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gY5-4bdcXL gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      enableLazyloadImage="true" tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gUzblYyM8E gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="gMm2utm9-u" data-id="gMm2utm9-u"
        style="--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:8px;--pc:start;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gMm2utm9-u gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      enableLazyloadImage="true" tag="Col" label="Block" type="component"
      style="--jc:start;--o:1"
      class="gY0druos0r gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="g1ITHIUeY4"
    role="presentation"
    class="gp-group/image g1ITHIUeY4 gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--d:block;--d-tablet:block;--d-mobile:none;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ta:center"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" srcset="{{ "gempages_553400155311702965-be5b3214-feef-462d-95c5-45b2abf3228e.png" | file_url }}" />
      <source media="(max-width: 1024px)" srcset="{{ "gempages_553400155311702965-be5b3214-feef-462d-95c5-45b2abf3228e.png" | file_url }}" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="{{ "gempages_553400155311702965-be5b3214-feef-462d-95c5-45b2abf3228e.png" | file_url }}"
        alt=""
        fetchpriority="high"
        style="--aspect:auto;--aspect-tablet:auto;--aspect-mobile:auto;--objf:cover;--h:auto;--h-tablet:auto;--h-mobile:auto;--b:none;--bc:#000000;--bw:1px 1px 1px 1px;--btrr:12px;--bblr:12px;--bbrr:12px;--btlr:12px;--radiusType:custom;--shadow:none"
      />
    
  </picture>
      </div>
  </div>
    </div><div
      enableLazyloadImage="true" tag="Col" label="Block" type="component"
      style="--jc:start;--o:0"
      class="g3e2jItOHV gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="g2e5fQSVDZ"
    role="presentation"
    class="gp-group/image g2e5fQSVDZ gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--d:block;--d-tablet:block;--d-mobile:none;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ta:center"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" srcset="{{ "gempages_553400155311702965-92087396-f8f3-41b9-bca0-426680f5dd90.png" | file_url }}" />
      <source media="(max-width: 1024px)" srcset="{{ "gempages_553400155311702965-92087396-f8f3-41b9-bca0-426680f5dd90.png" | file_url }}" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="{{ "gempages_553400155311702965-92087396-f8f3-41b9-bca0-426680f5dd90.png" | file_url }}"
        alt=""
        fetchpriority="high"
        style="--aspect:auto;--aspect-tablet:auto;--aspect-mobile:auto;--objf:cover;--h:auto;--h-tablet:auto;--h-mobile:auto;--b:none;--bc:#000000;--bw:1px 1px 1px 1px;--btrr:12px;--bblr:12px;--bbrr:12px;--btlr:12px;--radiusType:custom;--shadow:none"
      />
    
  </picture>
      </div>
  </div>
    </div>
    </div>
   
    
    </div>
    </div>
   
    
    </div>
    </div>
   
    
    </div>
    </div>
   
    
    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 1",
    "tag": "section",
    "class": "gps-555672128922321972 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/gardpro-us/apps/gempages-cro/app/shopify/edit?pageType=GP_STATIC&editorId=555482666321838914&sectionId=555672128922321972)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"gg9qvbPcW-g_text","label":"gg9qvbPcW-g_text","default":"<p>1309+ Reviews</p>"},{"type":"html","id":"ggyAxVweGV0_text","label":"ggyAxVweGV0_text","default":"<strong>SMARTWATCHES FOR WOMEN - PRECISION, POWER AND STYLE IN EVERY GARDPRO</strong>"},{"type":"html","id":"ggPoDRKi8ng_text","label":"ggPoDRKi8ng_text","default":"<p>Discover our range of <strong>smartwatches for women</strong>—where style meets advanced health tracking. From fitness tracker watches for women to ladies' sports watches, stay on top of your wellness journey with heart rate, sleep, and activity monitoring designed to keep you moving.</p>"},{"type":"html","id":"ggtBeWNd9Vu_label","label":"ggtBeWNd9Vu_label","default":"<p>SHOP THE FEMALE COLLECTION</p>"},{"type":"html","id":"ggfA571gy87_text","label":"ggfA571gy87_text","default":"<p>ADVANCED HEALTH TRACKING</p>"},{"type":"html","id":"ggjc0EZac29_text","label":"ggjc0EZac29_text","default":"<p>LONG-LASTING BATTERY</p>"},{"type":"html","id":"gg0NfbDZINY_text","label":"gg0NfbDZINY_text","default":"<p>DURABLE &amp; WATERPROOF</p>"},{"type":"html","id":"ggcG4JtcXuw_text","label":"ggcG4JtcXuw_text","default":"<p>IOS &amp; ANDROID COMPATIBILITY</p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
