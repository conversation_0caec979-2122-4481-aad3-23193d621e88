

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-555716577706902305.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-555716577706902305.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-555716577706902305.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-555716577706902305.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-555716577706902305.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-555716577706902305.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-555716577706902305.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-555716577706902305.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-555716577706902305.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-555716577706902305.gps.gpsil [style*="--bl:"]{border-left:var(--bl)}.gps-555716577706902305.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-555716577706902305.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-555716577706902305.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-555716577706902305.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-555716577706902305.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-555716577706902305.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-555716577706902305.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-555716577706902305.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-555716577706902305.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-555716577706902305.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-555716577706902305.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-555716577706902305.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-555716577706902305.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-555716577706902305.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-555716577706902305.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-555716577706902305.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-555716577706902305.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-555716577706902305.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-555716577706902305.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-555716577706902305.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-555716577706902305.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-555716577706902305.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-555716577706902305.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-555716577706902305.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-555716577706902305.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-555716577706902305.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-555716577706902305.gps.gpsil [style*="--ts:"]{text-shadow:var(--ts)}.gps-555716577706902305.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-555716577706902305.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-555716577706902305.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-555716577706902305.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-555716577706902305.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-555716577706902305.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-555716577706902305.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-555716577706902305.gps.gpsil [style*="--minw-tablet:"]{min-width:var(--minw-tablet)}.gps-555716577706902305.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-555716577706902305.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-555716577706902305.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-555716577706902305.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-555716577706902305.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-555716577706902305.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-555716577706902305.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-555716577706902305.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-555716577706902305.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-555716577706902305.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-555716577706902305.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-555716577706902305.gps.gpsil [style*="--minw-mobile:"]{min-width:var(--minw-mobile)}.gps-555716577706902305.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-555716577706902305.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-555716577706902305.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-555716577706902305.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-555716577706902305 .gp-g-paragraph-1{font-family:var(--g-p1-ff);font-size:var(--g-p1-size);font-style:var(--g-p1-fs);font-weight:var(--g-p1-weight);letter-spacing:var(--g-p1-ls);line-height:var(--g-p1-lh)}.gps-555716577706902305 .gp-relative{position:relative}.gps-555716577706902305 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-555716577706902305 .gp-mb-0{margin-bottom:0}.gps-555716577706902305 .gp-flex{display:flex}.gps-555716577706902305 .gp-inline-flex{display:inline-flex}.gps-555716577706902305 .gp-grid{display:grid}.gps-555716577706902305 .\!gp-hidden{display:none!important}.gps-555716577706902305 .gp-hidden{display:none}.gps-555716577706902305 .gp-max-w-full{max-width:100%}.gps-555716577706902305 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-555716577706902305 .gp-flex-col{flex-direction:column}.gps-555716577706902305 .gp-items-center{align-items:center}.gps-555716577706902305 .gp-justify-center{justify-content:center}.gps-555716577706902305 .gp-gap-y-0{row-gap:0}.gps-555716577706902305 .gp-overflow-hidden{overflow:hidden}.gps-555716577706902305 .gp-leading-\[0\]{line-height:0}.gps-555716577706902305 .gp-text-g-line-3{color:var(--g-c-line-3)}.gps-555716577706902305 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555716577706902305 .gp-duration-200{transition-duration:.2s}.gps-555716577706902305 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}@media (max-width:1024px){.gps-555716577706902305 .tablet\:\!gp-hidden{display:none!important}.gps-555716577706902305 .tablet\:gp-hidden{display:none}}@media (max-width:767px){.gps-555716577706902305 .mobile\:\!gp-hidden{display:none!important}.gps-555716577706902305 .mobile\:gp-hidden{display:none}}.gps-555716577706902305 .\[\&\>svg\]\:\!gp-h-\[var\(--height-desktop\)\]>svg{height:var(--height-desktop)!important}.gps-555716577706902305 .\[\&\>svg\]\:\!gp-w-auto>svg{width:auto!important}@media (max-width:1024px){.gps-555716577706902305 .tablet\:\[\&\>svg\]\:\!gp-h-\[var\(--height-tablet\)\]>svg{height:var(--height-tablet)!important}}@media (max-width:767px){.gps-555716577706902305 .mobile\:\[\&\>svg\]\:\!gp-h-\[var\(--height-mobile\)\]>svg{height:var(--height-mobile)!important}}.gps-555716577706902305 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-555716577706902305 .\[\&_p\]\:gp-inline p{display:inline}.gps-555716577706902305 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-555716577706902305 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      enableLazyloadImage="true" id="grxeTvKBwg" data-id="grxeTvKBwg"
        style="--blockPadding:base;--pt:var(--g-s-2xl);--pb:var(--g-s-2xl);--pt-tablet:var(--g-s-2xl);--pl-tablet:24px;--pb-tablet:var(--g-s-2xl);--pr-tablet:24px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="grxeTvKBwg gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      enableLazyloadImage="true" tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gWfr6Lr2xj gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="gM4mfd08_F" data-id="gM4mfd08_F"
        style="--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:8px;--pc:start;--gtc:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gM4mfd08_F gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      enableLazyloadImage="true" tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gztakU9u_U gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gEQU3HB3kw">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gEQU3HB3kw "
        style="--ta:center;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:16px;--mb-mobile:23px"
      >
      <div class="gp-flex" style="--jc:center">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:70%;--w-tablet:70%;--w-mobile:70%;--ta:center;--fs:normal;--ff:var(--g-font-heading, heading);--weight:700;--ls:normal;--size:40px;--size-tablet:30px;--size-mobile:24px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;--tt:uppercase;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggEQU3HB3kw_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gvbPz-EfDX">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gvbPz-EfDX "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:64px;--mb-mobile:32px"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:70%;--w-tablet:70%;--w-mobile:100%;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:#424242;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:18px;--size-tablet:18px;--size-mobile:16px;--lh:150%;--lh-tablet:150%;--lh-mobile:150%;word-break:break-word;overflow:hidden"
        >{{ section.settings.ggvbPz-EfDX_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="gT7iSAj0Yl" data-id="gT7iSAj0Yl"
        style="--mb:34px;--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:25px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr) minmax(0, 3fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gT7iSAj0Yl gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      enableLazyloadImage="true" tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gymVvPYTyx gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--mb:28px;--mb-mobile:17px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ta:center"
      class="gp-leading-[0] ghQFhDRXn_"
    >
      <div 
      data-id="ghQFhDRXn_"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-line-3, line-3);--t:rotate(0deg);--w:80px;--w-tablet:80px;--w-mobile:60px;--h:80px;--h-tablet:80px;--h-mobile:60px;--minw:80px;--minw-tablet:80px;--minw-mobile:60px;--height-desktop:80px;--height-tablet:80px;--height-mobile:60px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-line-3"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817623414997352">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M172.53,49.06a252.86,252.86,0,0,0-41.09-38,6,6,0,0,0-6.88,0,252.86,252.86,0,0,0-41.09,38C56.34,80.26,42,113.09,42,144a86,86,0,0,0,172,0C214,113.09,199.66,80.26,172.53,49.06ZM128,218a74.09,74.09,0,0,1-74-74c0-59.62,59-108.93,74-120.51C143,35.07,202,84.38,202,144A74.09,74.09,0,0,1,128,218Zm53.92-65A55.58,55.58,0,0,1,137,197.92a7,7,0,0,1-1,.08,6,6,0,0,1-1-11.92c17.38-2.92,32.13-17.68,35.08-35.08a6,6,0,1,1,11.84,2Z" /></svg></span>
    </div>
      </div>
    </div>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g6PS-RS5uV">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="g6PS-RS5uV "
        style="--ta:center;--mb:9px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:center">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:90%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-heading, heading);--weight:600;--size:22px;--size-tablet:24px;--size-mobile:20px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:var(--g-c-text-2, text-2);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.gg6PS-RS5uV_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g2UwXnPoSa">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="g2UwXnPoSa "
        style="--ta:left;--mb-mobile:17px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:var(--g-c-text-1, text-1);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.gg2UwXnPoSa_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div><div
      enableLazyloadImage="true" tag="Col" label="Block" type="component"
      style="--jc:start"
      class="giBkmCOH5C gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--mb:28px;--mb-mobile:17px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ta:center"
      class="gp-leading-[0] g1Gmehca5n"
    >
      <div 
      data-id="g1Gmehca5n"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-line-3, line-3);--t:rotate(0deg);--w:80px;--w-tablet:80px;--w-mobile:60px;--h:80px;--h-tablet:80px;--h-mobile:60px;--minw:80px;--minw-tablet:80px;--minw-mobile:60px;--height-desktop:80px;--height-tablet:80px;--height-mobile:60px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-line-3"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817641569911144">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M152,78a30,30,0,1,0-30-30A30,30,0,0,0,152,78Zm0-48a18,18,0,1,1-18,18A18,18,0,0,1,152,30Zm46,114v88a6,6,0,0,1-12,0V149.81c-26.23-1.73-34.76-14.89-42.35-26.59-3.43-5.3-6.68-10.31-11.5-14.52L117,143.61l38.52,27.51A6,6,0,0,1,158,176v56a6,6,0,0,1-12,0V179.09l-33.92-24.23L77.5,234.39a6,6,0,0,1-11-4.78L124,97.29a6,6,0,0,1,8.25-2.94,38.89,38.89,0,0,1,7.65,5.21c6.15,5.34,10,11.33,13.79,17.13C161.44,128.59,167.54,138,192,138A6,6,0,0,1,198,144ZM72,150a6,6,0,0,0,5.52-3.64l24-56a6,6,0,0,0-3.16-7.88l-28-12a6,6,0,0,0-7.87,3.16l-24,56a6,6,0,0,0,3.15,7.87l28,12A6,6,0,0,0,72,150ZM51.88,128.85l19.27-45,17,7.27-19.27,45Z" /></svg></span>
    </div>
      </div>
    </div>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gU7xcFMOUb">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gU7xcFMOUb "
        style="--ta:center;--mb:9px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:center">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:80%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-heading, heading);--weight:600;--size:22px;--size-tablet:24px;--size-mobile:20px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:var(--g-c-text-2, text-2);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggU7xcFMOUb_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="go4-v2ixLT">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="go4-v2ixLT "
        style="--ta:left;--mb-mobile:17px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:var(--g-c-text-1, text-1);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggo4-v2ixLT_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div><div
      enableLazyloadImage="true" tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gG18psZgmc gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--mb:28px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ta:center"
      class="gp-leading-[0] gpQkiA6lqG"
    >
      <div 
      data-id="gpQkiA6lqG"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-line-3, line-3);--t:rotate(0deg);--w:80px;--w-tablet:80px;--w-mobile:60px;--h:80px;--h-tablet:80px;--h-mobile:60px;--minw:80px;--minw-tablet:80px;--minw-mobile:60px;--height-desktop:80px;--height-tablet:80px;--height-mobile:60px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-line-3"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817649805558120">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M244.52,100.05l-56-64A6,6,0,0,0,184,34H72a6,6,0,0,0-4.52,2l-56,64a6,6,0,0,0,.13,8l112,120a6,6,0,0,0,8.78,0l112-120A6,6,0,0,0,244.52,100.05ZM75.94,110l34.6,86.49L29.81,110Zm91.2,0L128,207.84,88.86,110ZM92,98l36-48,36,48Zm88.06,12h46.13l-80.73,86.49Zm46.72-12H179L140,46h41.28ZM74.72,46H116L77,98H29.22Z" /></svg></span>
    </div>
      </div>
    </div>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gZF9hq6Jo0">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gZF9hq6Jo0 "
        style="--ta:center;--mb:9px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:center">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:90%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-heading, heading);--weight:600;--size:22px;--size-tablet:24px;--size-mobile:20px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:var(--g-c-text-2, text-2);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggZF9hq6Jo0_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gR_i4N5rVn">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gR_i4N5rVn "
        style="--ta:left;--mb-mobile:17px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:var(--g-c-text-1, text-1);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggR_i4N5rVn_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div><div
      enableLazyloadImage="true" tag="Col" label="Block" type="component"
      style="--jc:start"
      class="guKFCbznN3 gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--mb:28px;--mb-mobile:17px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ta:center"
      class="gp-leading-[0] gi0TEHiGMV"
    >
      <div 
      data-id="gi0TEHiGMV"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:var(--g-c-line-3, line-3);--t:rotate(0deg);--w:80px;--w-tablet:80px;--w-mobile:60px;--h:80px;--h-tablet:80px;--h-mobile:60px;--minw:80px;--minw-tablet:80px;--minw-mobile:60px;--height-desktop:80px;--height-tablet:80px;--height-mobile:60px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)] gp-text-g-line-3"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817641570042216">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M172,86a26,26,0,1,0-26-26A26,26,0,0,0,172,86Zm0-40a14,14,0,1,1-14,14A14,14,0,0,1,172,46ZM38.32,85.76l111.47,32.51,6,6A6,6,0,0,0,160,126h40a6,6,0,0,0,0-12H162.49L132.24,83.75a6,6,0,0,0-8.48,0L112.59,94.92,41.68,74.24a6,6,0,1,0-3.36,11.52ZM128,96.48l4.14,4.14-6.41-1.87Zm107.33,106.1a67.79,67.79,0,0,1-56.7,8.69L22.32,165.75a6,6,0,1,1,3.36-11.52l77.13,22.46L132.55,147l-46.2-13.2a6,6,0,0,1,3.3-11.54l56,16a6,6,0,0,1,2.59,10L116,180.52l66,19.23a55.79,55.79,0,0,0,46.68-7.15,6,6,0,1,1,6.66,10Z" /></svg></span>
    </div>
      </div>
    </div>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="ggHhL8MMXR">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="ggHhL8MMXR "
        style="--ta:center;--mb:9px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:center">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:80%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-heading, heading);--weight:600;--size:22px;--size-tablet:24px;--size-mobile:20px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:var(--g-c-text-2, text-2);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.gggHhL8MMXR_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gceMqvXG5h">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gceMqvXG5h "
        style="--ta:left;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--c:var(--g-c-text-1, text-1);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggceMqvXG5h_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
    </div>
    </div>
   
    
    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 3",
    "tag": "section",
    "class": "gps-555716577706902305 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/gardpro-us/apps/gempages-cro/app/shopify/edit?pageType=GP_STATIC&editorId=555716577622754081&sectionId=555716577706902305)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggEQU3HB3kw_text","label":"ggEQU3HB3kw_text","default":"YOUR PERSONAL HEALTH COACH - ALWAYS WITHIN REACH"},{"type":"html","id":"ggvbPz-EfDX_text","label":"ggvbPz-EfDX_text","default":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(0,0,0);font-size:18px;\">Track your wellness in real-time with fitness watches for men designed to keep you at peak performance. Whether you're monitoring your sleep, hitting your daily goals, or taking on rugged adventures, these sport watches for men offer everything you need to thrive.</span></p>"},{"type":"html","id":"gg6PS-RS5uV_text","label":"gg6PS-RS5uV_text","default":"UNMATCHED WATER RESISTANCE"},{"type":"html","id":"gg2UwXnPoSa_text","label":"gg2UwXnPoSa_text","default":"<p>Engineered for the depths—dive up to 30 meters with confidence and explore the underwater world without limits.</p>"},{"type":"html","id":"ggU7xcFMOUb_text","label":"ggU7xcFMOUb_text","default":"ADVENTURE-READY DESIGN"},{"type":"html","id":"ggo4-v2ixLT_text","label":"ggo4-v2ixLT_text","default":"<p>Built to withstand the elements—whether you're scaling mountains, braving the ocean, or navigating the unknown, this watch is your ultimate gents smart watch.</p>"},{"type":"html","id":"ggZF9hq6Jo0_text","label":"ggZF9hq6Jo0_text","default":"EXTREME DURABILITY"},{"type":"html","id":"ggR_i4N5rVn_text","label":"ggR_i4N5rVn_text","default":"<p>Built to withstand the toughest conditions, from intense workouts to harsh outdoor environments.</p>"},{"type":"html","id":"gggHhL8MMXR_text","label":"gggHhL8MMXR_text","default":"PERFORMANCE TRACKING"},{"type":"html","id":"ggceMqvXG5h_text","label":"ggceMqvXG5h_text","default":"<p>Stay ahead of your goals with real-time heart rate, step count, and calorie tracking, ensuring peak performance.</p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
