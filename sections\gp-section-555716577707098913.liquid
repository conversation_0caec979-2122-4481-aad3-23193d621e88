

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-555716577707098913.gps.gpsil [style*="--ai:"]{align-items:var(--ai)}.gps-555716577707098913.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-555716577707098913.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-555716577707098913.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-555716577707098913.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-555716577707098913.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-555716577707098913.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-555716577707098913.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-555716577707098913.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-555716577707098913.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-555716577707098913.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-555716577707098913.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-555716577707098913.gps.gpsil [style*="--bbw:"]{border-bottom-width:var(--bbw)}.gps-555716577707098913.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-555716577707098913.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-555716577707098913.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-555716577707098913.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-555716577707098913.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-555716577707098913.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-555716577707098913.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-555716577707098913.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-555716577707098913.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-555716577707098913.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-555716577707098913.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-555716577707098913.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-555716577707098913.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-555716577707098913.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-555716577707098913.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-555716577707098913.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-555716577707098913.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-555716577707098913.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-555716577707098913.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-555716577707098913.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-555716577707098913.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-555716577707098913.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-555716577707098913.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-555716577707098913.gps.gpsil [style*="--mr:"]{margin-right:var(--mr)}.gps-555716577707098913.gps.gpsil [style*="--maxw:"]{max-width:var(--maxw)}.gps-555716577707098913.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-555716577707098913.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-555716577707098913.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-555716577707098913.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-555716577707098913.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-555716577707098913.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-555716577707098913.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-555716577707098913.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-555716577707098913.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-555716577707098913.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-555716577707098913.gps.gpsil [style*="--ts:"]{text-shadow:var(--ts)}.gps-555716577707098913.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-555716577707098913.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-555716577707098913.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-555716577707098913.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-555716577707098913.gps.gpsil [style*="--bbw-tablet:"]{border-bottom-width:var(--bbw-tablet)}.gps-555716577707098913.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-555716577707098913.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-555716577707098913.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-555716577707098913.gps.gpsil [style*="--maxw-tablet:"]{max-width:var(--maxw-tablet)}.gps-555716577707098913.gps.gpsil [style*="--minw-tablet:"]{min-width:var(--minw-tablet)}.gps-555716577707098913.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-555716577707098913.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-555716577707098913.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-555716577707098913.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-555716577707098913.gps.gpsil [style*="--bbw-mobile:"]{border-bottom-width:var(--bbw-mobile)}.gps-555716577707098913.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-555716577707098913.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-555716577707098913.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-555716577707098913.gps.gpsil [style*="--jc-mobile:"]{justify-content:var(--jc-mobile)}.gps-555716577707098913.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-555716577707098913.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-555716577707098913.gps.gpsil [style*="--maxw-mobile:"]{max-width:var(--maxw-mobile)}.gps-555716577707098913.gps.gpsil [style*="--minw-mobile:"]{min-width:var(--minw-mobile)}.gps-555716577707098913.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-555716577707098913.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-555716577707098913.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-555716577707098913.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-555716577707098913.gps.gpsil [style*="--ta-mobile:"]{text-align:var(--ta-mobile)}.gps-555716577707098913.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-555716577707098913.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-555716577707098913 .gp-g-paragraph-1{font-family:var(--g-p1-ff);font-size:var(--g-p1-size);font-style:var(--g-p1-fs);font-weight:var(--g-p1-weight);letter-spacing:var(--g-p1-ls);line-height:var(--g-p1-lh)}.gps-555716577707098913 .\!gp-relative{position:relative!important}.gps-555716577707098913 .gp-relative{position:relative}.gps-555716577707098913 .gp-z-1{z-index:1}.gps-555716577707098913 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-555716577707098913 .gp-mb-0{margin-bottom:0}.gps-555716577707098913 .gp-block{display:block}.gps-555716577707098913 .gp-flex{display:flex}.gps-555716577707098913 .gp-inline-flex{display:inline-flex}.gps-555716577707098913 .gp-grid{display:grid}.gps-555716577707098913 .\!gp-hidden{display:none!important}.gps-555716577707098913 .gp-hidden{display:none}.gps-555716577707098913 .gp-h-full{height:100%}.gps-555716577707098913 .gp-w-full{width:100%}.gps-555716577707098913 .gp-max-w-full{max-width:100%}.gps-555716577707098913 .gp-flex-none{flex:none}.gps-555716577707098913 .gp-flex-col{flex-direction:column}.gps-555716577707098913 .gp-items-center{align-items:center}.gps-555716577707098913 .gp-justify-start{justify-content:flex-start}.gps-555716577707098913 .gp-justify-center{justify-content:center}.gps-555716577707098913 .gp-gap-y-0{row-gap:0}.gps-555716577707098913 .gp-overflow-hidden{overflow:hidden}.gps-555716577707098913 .gp-break-words{overflow-wrap:break-word}.gps-555716577707098913 .gp-rounded-none{border-radius:0}.gps-555716577707098913 .gp-text-center{text-align:center}.gps-555716577707098913 .gp-leading-\[0\]{line-height:0}.gps-555716577707098913 .gp-text-g-text-3{color:var(--g-c-text-3)}.gps-555716577707098913 .gp-no-underline{text-decoration-line:none}.gps-555716577707098913 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555716577707098913 .gp-duration-200{transition-duration:.2s}.gps-555716577707098913 .gp-duration-300{transition-duration:.3s}.gps-555716577707098913 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555716577707098913 .disabled\:gp-btn-disabled:disabled{cursor:default}.gps-555716577707098913 .disabled\:gp-pointer-events-none:disabled{pointer-events:none}.gps-555716577707098913 .disabled\:gp-opacity-30:disabled{opacity:.3}@media (hover:hover) and (pointer:fine){.gps-555716577707098913 .gp-group\/button:hover .group-hover\/button\:\!gp-text-inherit{color:inherit!important}}.gps-555716577707098913 .gp-group\/button:active .group-active\/button\:\!gp-text-inherit{color:inherit!important}.gps-555716577707098913 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-invisible{visibility:hidden}@media (max-width:1024px){.gps-555716577707098913 .tablet\:gp-block{display:block}.gps-555716577707098913 .tablet\:\!gp-hidden{display:none!important}.gps-555716577707098913 .tablet\:gp-hidden{display:none}}@media (max-width:767px){.gps-555716577707098913 .mobile\:gp-block{display:block}.gps-555716577707098913 .mobile\:\!gp-hidden{display:none!important}.gps-555716577707098913 .mobile\:gp-hidden{display:none}.gps-555716577707098913 .mobile\:gp-justify-center{justify-content:center}}.gps-555716577707098913 .\[\&\>svg\]\:\!gp-h-\[var\(--height-desktop\)\]>svg{height:var(--height-desktop)!important}.gps-555716577707098913 .\[\&\>svg\]\:\!gp-w-auto>svg{width:auto!important}@media (max-width:1024px){.gps-555716577707098913 .tablet\:\[\&\>svg\]\:\!gp-h-\[var\(--height-tablet\)\]>svg{height:var(--height-tablet)!important}}@media (max-width:767px){.gps-555716577707098913 .mobile\:\[\&\>svg\]\:\!gp-h-\[var\(--height-mobile\)\]>svg{height:var(--height-mobile)!important}}.gps-555716577707098913 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-555716577707098913 .\[\&_p\]\:gp-inline p{display:inline}.gps-555716577707098913 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}.gps-555716577707098913 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-555716577707098913 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      enableLazyloadImage="true" id="gTv-jKdSyZ" data-id="gTv-jKdSyZ"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--pt:var(--g-s-4xl);--pl:15px;--pb:var(--g-s-4xl);--pr:15px;--pt-mobile:var(--g-s-4xl);--pl-mobile:24px;--pb-mobile:var(--g-s-4xl);--pr-mobile:24px;--cg:30px;--pc:start;--gtc:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gTv-jKdSyZ gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:start;--o:0"
      class="gGOi3OP_vM gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="gkFqYZyXJJ" data-id="gkFqYZyXJJ"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:0px;--cg:50px;--pc:start;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gkFqYZyXJJ gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g0Vdumr-a0 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="glPxaj6Wuj">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="glPxaj6Wuj "
        style="--tt:default;--ta:left;--ta-mobile:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:var(--g-s-xl);--pr:130px;--pl-mobile:0px;--pr-mobile:0px;--pr-tablet:var(--g-s-2xl)"
      >
      <div class="gp-flex" style="--jc:left;--jc-mobile:center">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:left;--ta-mobile:left;--c:#242424;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-heading, heading);--weight:700;--ls:normal;--size:37px;--size-tablet:28px;--size-mobile:26px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--tt:uppercase;word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.gglPxaj6Wuj_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
  <gp-button >
  <div
    style="--mb:17px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--bblr:8px;--bbrr:8px;--btlr:8px;--btrr:8px;--shadow:none;--op:100%;--ta:left"
    
  >
    <style>
    .gVXswEMC83.gp-button-base::before {
      content: "";
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      border-radius: var(--g-radius-small);
    }

    .gVXswEMC83:hover::before {
      
      
    }

    .gVXswEMC83:hover .gp-button-icon {
      color: undefined;
    }

     .gVXswEMC83 .gp-button-icon {
      color: var(--g-c-text-3, text-3);
    }

    .gVXswEMC83:hover .gp-button-price {
      color: undefined;
    }

    .gVXswEMC83 .gp-button-price {
      color: var(--g-c-text-3, text-3);
    }

    .gVXswEMC83 .gp-product-dot-price {
       color: var(--g-c-text-3, text-3);
    }

    .gVXswEMC83:hover .gp-product-dot-price {
      color: undefined;
    }
  </style>
    <a
      href="#guP0xNZBba" target="_self" data-id="gVXswEMC83" aria-label="<p>Shop the Men&#039;s Collection</p>"
      
      data-state="idle"
      class="gVXswEMC83 gp-button-base gp-group/button gp-rounded-none gp-max-w-full gp-relative gp-inline-flex gp-items-center gp-justify-center gp-no-underline gp-transition-colors gp-duration-300 disabled:gp-btn-disabled disabled:gp-opacity-30 disabled:gp-pointer-events-none gp-text-g-text-3 gp-text-center"
      style="--bg:#096de3;--hvr-bg:#1180FF;--radius:var(--g-radius-small);--shadow:none;--w:Auto;--w-tablet:Auto;--w-mobile:100%;--h:Auto;--h-tablet:Auto;--h-mobile:Auto;--pl:32px;--pr:32px;--pt:12px;--pb:12px;--pl-mobile:32px;--pr-mobile:32px;--pt-mobile:12px;--pb-mobile:12px;--c:var(--g-c-text-3, text-3);--size:16px;--size-tablet:16px;--size-mobile:14px;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:600;--lh:180%;--lh-tablet:180%;--lh-mobile:180%;--tt:uppercase"
    >
      
    <div
    class="gp-inline-flex">
    
      <span
        data-gp-text
        class="gp-content-product-button group-hover/button:!gp-text-inherit group-active/button:!gp-text-inherit  gp-button-text-only gp-break-words group-data-[state=loading]/button:gp-invisible [&_p]:gp-whitespace-pre-line gp-z-1 gp-h-full gp-flex gp-items-center gp-overflow-hidden 
        "
        style="--size:16px;--size-tablet:16px;--size-mobile:14px;--c:var(--g-c-text-3, text-3)"
      >
        {{ section.settings.ggVXswEMC83_label }}
      </span>
      </div>
    
    </a>
  </div>
  </gp-button>

       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="gi_qED1QJL" data-id="gi_qED1QJL"
        style="--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:8px;--pc:start;--gtc:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gi_qED1QJL gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" tag="Col" label="Block" type="component"
      style="--jc:start"
      class="gIySuxnc6Z gp-relative gp-flex gp-flex-col"
    >
      <div gp-el-wrapper style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--pt:var(--g-s-2xl);--pb:var(--g-s-2xl);--mb-mobile:var(--g-s-l)" class="ggxyzB9vbC ">
      
    <div
    data-id="ggxyzB9vbC"
      class="gp-flex gp-justify-start mobile:gp-justify-center"
    >
      <div
        style="--w:70px;--w-tablet:70px;--w-mobile:100%;--bs:solid;--bbw:2px;--bbw-tablet:2px;--bbw-mobile:2px;--t:gp-rotate(0deg);--bc:#242424"
        class="gp-block tablet:gp-block mobile:gp-block"
      ></div>
      <div
        class="gp-items-center gp-hidden tablet:gp-hidden mobile:gp-hidden"
        style="--w:70px;--w-tablet:70px;--w-mobile:100%;--bc:#242424;--t:gp-rotate(0deg)"
      >
        
    <div
      class="gp-hidden"
      style="--w:70px;--w-tablet:70px;--w-mobile:100%;--bc:#242424;--bs:solid;--bbw:2px;--bbw-tablet:2px;--bbw-mobile:2px;--minw:70px"
    ></div>
  
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none"
              style="--mr:var(--g-s-xs);--w:20px;--h:20px;--t:gp-rotate(0);--c:var(--g-c-text-1, text-1)"
            >
              <svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" className="w-6 h-6"  viewBox="0 0 512 512" fill="currentColor">              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M428.8 137.6h-86.177a115.52 115.52 0 0 0 2.176-22.4c0-47.914-35.072-83.2-92-83.2-45.314 0-57.002 48.537-75.707 78.784-7.735 12.413-16.994 23.317-25.851 33.253l-.131.146-.129.148C135.662 161.807 127.764 168 120.8 168h-2.679c-5.747-4.952-13.536-8-22.12-8H32c-17.673 0-32 12.894-32 28.8v230.4C0 435.106 14.327 448 32 448h64c8.584 0 16.373-3.048 22.12-8h2.679c28.688 0 67.137 40 127.2 40h21.299c62.542 0 98.8-38.658 99.94-91.145 12.482-17.813 18.491-40.785 15.985-62.791A93.148 93.148 0 0 0 393.152 304H428.8c45.435 0 83.2-37.584 83.2-83.2 0-45.099-38.101-83.2-83.2-83.2zm0 118.4h-91.026c12.837 14.669 14.415 42.825-4.95 61.05 11.227 19.646 1.687 45.624-12.925 53.625 6.524 39.128-10.076 61.325-50.6 61.325H248c-45.491 0-77.21-35.913-120-39.676V215.571c25.239-2.964 42.966-21.222 59.075-39.596 11.275-12.65 21.725-25.3 30.799-39.875C232.355 112.712 244.006 80 252.8 80c23.375 0 44 8.8 44 35.2 0 35.2-26.4 53.075-26.4 70.4h158.4c18.425 0 35.2 16.5 35.2 35.2 0 18.975-16.225 35.2-35.2 35.2zM88 384c0 13.255-10.745 24-24 24s-24-10.745-24-24 10.745-24 24-24 24 10.745 24 24z" /></svg>
            </span>
          
        
            <span
              class="gp-inline-flex gp-items-center gp-justify-center gp-flex-none gp-g-paragraph-1"
              style="--tt:horizontal;--c:var(--g-c-text-1, text-1);--mr:var(--g-s-s)"
            >
              Title
            </span>
          
        
    <div
      class="gp-flex"
      style="--w:70px;--w-tablet:70px;--w-mobile:100%;--bc:#242424;--bs:solid;--bbw:2px;--bbw-tablet:2px;--bbw-mobile:2px;--minw:70px"
    ></div>
  
      </div>
    </div>
  
      </div> 
  <script src="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-marquee.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  <gp-marquee
   data-id="gLET0Cd71P"
    
  gp-data='{"setting":{"childItem":["Item 1","Item 2","Item 3","Item 4"],"itemWidthType":{"desktop":"FIT_CONTENT","tablet":"FIT_CONTENT","mobile":"FIT_CONTENT"},"activeItem":"0","hoverItem":"0","isShowIconSeparator":false,"iconSeparatorSvg":"<svg height=\"100%\" width=\"100%\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 256 256\" fill=\"currentColor\">\n              <path fill=\"currentColor\" strokelinecap=\"round\" strokelinejoin=\"round\" d=\"M232,128A104,104,0,1,1,128,24,104.13,104.13,0,0,1,232,128Z\"></path></svg>","stopOnHover":false,"speed":"0.4","direction":"left","isPreview":false,"hasItemShadow":false,"uid":"gLET0Cd71P"},"styles":{"itemSpacing":{"desktop":"25px","tablet":"56px","mobile":"56px"},"itemMaxWidth":{"desktop":"300px","tablet":"300px","mobile":"300px"},"iconSeparatorSize":{"desktop":24,"tablet":24,"mobile":24},"iconSeparatorColor":"#0C0C0C","sizeSetting":{"desktop":{"shapeLinked":false,"width":"100%","height":"auto"}},"itemBorderStyle":{"borderType":"none","border":"none","borderWidth":"1px","width":"1px 1px 1px 1px","isCustom":false,"color":"#000000"},"itemCorner":{"radiusType":"none"},"itemBackgroundColor":{"desktop":"transparent"},"backgroundColor":{"desktop":"transparent"},"align":{"desktop":"center"},"itemShadow":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"#121212","angle":90}}}'
  class="gLET0Cd71P"
  >
   <div
      class="gp-flex gp-w-full gp-relative"
      style="--jc:center"
    >
  <div class="!gp-relative gp-overflow-hidden" style="--h:auto;--h-tablet:auto;--h-mobile:auto;--w:100%;--w-tablet:100%;--w-mobile:100%;--bg:transparent;--mb:17px;--pt:12px;--pb:12px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%">
        <div
    class="gem-marquee gp-overflow-hidden gp-w-full gp-h-full"
     
    >
     <div class="gp-overflow-hidden gp-w-full gp-h-full">
       
   <div
      style="--pause-on-hover:running;--pause-on-click:running;--width:100%;--transform:none;overflow:hidden;min-width:100%"
      class="rfm-marquee-container"
    >
      <div
        class="rfm-marquee"
        style="--play:running;--direction:normal;--delay:0s;--iteration-count:infinite;--min-width:auto;--w:auto;--w-tablet:auto;--w-mobile:auto;--minw:auto;--minw-tablet:auto;--minw-mobile:auto"
      >
        <div class="rfm-initial-child-container">
          
            <div style="--transform:none;--w:auto;--w-tablet:auto;--w-mobile:auto;--minw:auto;--minw-tablet:auto;--minw-mobile:auto" class="rfm-child">
               <div class="gp-flex gem-child-marquee-item gp-items-center" style="[object Object]">
       <div
                  class="gp-inline-flex gp-items-center gp-relative"
                  style="--pr:25px;--pr-tablet:56px;--pr-mobile:56px;text-wrap:wrap;--w:auto;--w-tablet:auto;--w-mobile:auto;--minw:auto;--minw-tablet:auto;--minw-mobile:auto;--maxw:unset;--maxw-tablet:unset;--maxw-mobile:unset"
                >
                  <div
      class="g4v5luY9XT gem-marquee-item gem-marquee-item-g4v5luY9XT gp-overflow-hidden"
      style="--bg:transparent;--b:none;--bw:1px 1px 1px 1px;--bc:#000000;--shadow:none;--w:calc(100%);--w-tablet:calc(100%);--w-mobile:calc(100%)"
    >
    
     
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="ggsQOXFBPX">
    <div
      enableLazyloadImage="true" tag="Text" label="Text" type="component" isNotFullWidth="true" isNotLazyload="true"
        class="ggsQOXFBPX "
        style="--ta:left;--pt:8px;--pb:8px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--weight:bold;--c:var(--g-c-text-1, text-1);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.gggsQOXFBPX_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
                   
                </div> <div
                  class="gp-inline-flex gp-items-center gp-relative"
                  style="--pr:25px;--pr-tablet:56px;--pr-mobile:56px;text-wrap:wrap;--w:auto;--w-tablet:auto;--w-mobile:auto;--minw:auto;--minw-tablet:auto;--minw-mobile:auto;--maxw:unset;--maxw-tablet:unset;--maxw-mobile:unset"
                >
                  <div
      class="gtGP9XE5CQ gem-marquee-item gem-marquee-item-gtGP9XE5CQ gp-overflow-hidden"
      style="--bg:transparent;--b:none;--bw:1px 1px 1px 1px;--bc:#000000;--shadow:none;--w:calc(100%);--w-tablet:calc(100%);--w-mobile:calc(100%)"
    >
    
     
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gZRyNkK4dg">
    <div
      enableLazyloadImage="true" tag="Text" label="Text" type="component" isNotFullWidth="true" isNotLazyload="true"
        class="gZRyNkK4dg "
        style="--ta:left;--pt:8px;--pb:8px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--weight:bold;--c:var(--g-c-text-1, text-1);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggZRyNkK4dg_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
                   
                </div> <div
                  class="gp-inline-flex gp-items-center gp-relative"
                  style="--pr:25px;--pr-tablet:56px;--pr-mobile:56px;text-wrap:wrap;--w:auto;--w-tablet:auto;--w-mobile:auto;--minw:auto;--minw-tablet:auto;--minw-mobile:auto;--maxw:unset;--maxw-tablet:unset;--maxw-mobile:unset"
                >
                  <div
      class="g9gInVML9C gem-marquee-item gem-marquee-item-g9gInVML9C gp-overflow-hidden"
      style="--bg:transparent;--b:none;--bw:1px 1px 1px 1px;--bc:#000000;--shadow:none;--w:calc(100%);--w-tablet:calc(100%);--w-mobile:calc(100%)"
    >
    
     
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gOl6PujOj7">
    <div
      enableLazyloadImage="true" tag="Text" label="Text Block" type="component" isNotFullWidth="true" isNotLazyload="true"
        class="gOl6PujOj7 "
        style="--ta:left;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--weight:bold;--c:var(--g-c-text-1, text-1);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggOl6PujOj7_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
      <style id="custom-css-gOl6PujOj7">
        .gOl6PujOj7 {

}
.gOl6PujOj7 p {

}
      </style>
    
    </div>
                   
                </div> <div
                  class="gp-inline-flex gp-items-center gp-relative"
                  style="--pr:25px;--pr-tablet:56px;--pr-mobile:56px;text-wrap:wrap;--w:auto;--w-tablet:auto;--w-mobile:auto;--minw:auto;--minw-tablet:auto;--minw-mobile:auto;--maxw:unset;--maxw-tablet:unset;--maxw-mobile:unset"
                >
                  <div
      class="g04f8mqJJH gem-marquee-item gem-marquee-item-g04f8mqJJH gp-overflow-hidden"
      style="--bg:transparent;--b:none;--bw:1px 1px 1px 1px;--bc:#000000;--shadow:none;--w:calc(100%);--w-tablet:calc(100%);--w-mobile:calc(100%)"
    >
    
     
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gzSMKninFx">
    <div
      enableLazyloadImage="true" tag="Text" label="Text" type="component" isNotFullWidth="true" isNotLazyload="true"
        class="gzSMKninFx "
        style="--ta:left;--pt:8px;--pb:8px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--weight:bold;--c:var(--g-c-text-1, text-1);word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggzSMKninFx_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
                   
                </div>
    </div>
            </div>
          
        </div>
      </div>
      <div
        class="rfm-marquee placeholder-marquee"
        style="--play:running;--direction:normal;--delay:0s;--iteration-count:infinite;--min-width:auto;--w:auto;--w-tablet:auto;--w-mobile:auto;--minw:auto;--minw-tablet:auto;--minw-mobile:auto"
      ></div>
    </div>
  
     </div>
    </div>
  
  </div>
   </div>
  </gp-marquee>

    </div>
    </div>
   
    
    </div><div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g-9mRM3Gb4 gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="gSDBt3wcDB" data-id="gSDBt3wcDB"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--mb:0px;--pb:var(--g-s-3xl);--mb-mobile:0px;--pb-mobile:0px;--cg:30px;--pc:start;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gSDBt3wcDB gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:start;--o:0"
      class="gmIOIpBl8r gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--mb:13px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ta:left"
      class="gp-leading-[0] g1IMZAQjE1"
    >
      <div 
      data-id="g1IMZAQjE1"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:#096DE3;--t:rotate(0deg);--w:33px;--w-tablet:33px;--w-mobile:33px;--h:33px;--h-tablet:33px;--h-mobile:33px;--minw:33px;--minw-tablet:33px;--minw-mobile:33px;--height-desktop:33px;--height-tablet:33px;--height-mobile:33px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817562043810152">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M236,208a12,12,0,0,1-12,12H32a12,12,0,0,1-12-12V48a12,12,0,0,1,24,0v85.55L88.1,95a12,12,0,0,1,15.1-.57l56.22,42.16L216.1,87A12,12,0,1,1,231.9,105l-64,56a12,12,0,0,1-15.1.57L96.58,119.44,44,165.45V196H224A12,12,0,0,1,236,208Z" /></svg></span>
    </div>
      </div>
    </div>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gNfxYKA6N2">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gNfxYKA6N2 "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:left;--ta-mobile:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:21px;--size-tablet:19px;--size-mobile:17px;--lh:150%;--lh-tablet:130%;--lh-mobile:150%;--c:#242424;--tt:uppercase;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggNfxYKA6N2_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gycE1OzIP9">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gycE1OzIP9 "
        style="--tt:default;--ta:left;--ta-mobile:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb-mobile:var(--g-s-xl)"
      >
      <div class="gp-flex" style="--jc:left;--jc-mobile:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--ta-mobile:left;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:#424242;--size:16px;--size-tablet:14px;--size-mobile:14px;word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.ggycE1OzIP9_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div><div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="g2vX0QyJzW gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--mb:13px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ta:left"
      class="gp-leading-[0] gTOUuQujx8"
    >
      <div 
      data-id="gTOUuQujx8"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:#096de3;--t:rotate(0deg);--w:33px;--w-tablet:33px;--w-mobile:33px;--h:33px;--h-tablet:33px;--h-mobile:33px;--minw:33px;--minw-tablet:33px;--minw-mobile:33px;--height-desktop:33px;--height-tablet:33px;--height-mobile:33px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817595011105128">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M178.39,158c-11,19.06-29.39,30-50.39,30s-39.36-10.93-50.39-30a12,12,0,0,1,20.78-12c3.89,6.73,12.91,18,29.61,18s25.72-11.28,29.61-18a12,12,0,1,1,20.78,12ZM236,128A108,108,0,1,1,128,20,108.12,108.12,0,0,1,236,128Zm-24,0a84,84,0,1,0-84,84A84.09,84.09,0,0,0,212,128ZM92,124a16,16,0,1,0-16-16A16,16,0,0,0,92,124Zm72-32a16,16,0,1,0,16,16A16,16,0,0,0,164,92Z" /></svg></span>
    </div>
      </div>
    </div>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g3H4Ol5UDn">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="g3H4Ol5UDn "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:left;--ta-mobile:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:21px;--size-tablet:19px;--size-mobile:17px;--lh:150%;--lh-tablet:130%;--lh-mobile:150%;--c:#242424;--tt:uppercase;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.gg3H4Ol5UDn_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gOXJI5kkrU">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gOXJI5kkrU "
        style="--tt:default;--ta:left;--ta-mobile:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb-mobile:var(--g-s-xl)"
      >
      <div class="gp-flex" style="--jc:left;--jc-mobile:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--ta-mobile:left;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:#424242;--size:16px;--size-tablet:14px;--size-mobile:14px;word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.ggOXJI5kkrU_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="gR1DkVhESp" data-id="gR1DkVhESp"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--cg:30px;--pc:start;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gR1DkVhESp gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:start;--o:0"
      class="gcSOh3ViNr gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--mb:13px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ta:left"
      class="gp-leading-[0] gKUj93c20J"
    >
      <div 
      data-id="gKUj93c20J"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:#096de3;--t:rotate(0deg);--w:33px;--w-tablet:33px;--w-mobile:33px;--h:33px;--h-tablet:33px;--h-mobile:33px;--minw:33px;--minw-tablet:33px;--minw-mobile:33px;--height-desktop:33px;--height-tablet:33px;--height-mobile:33px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817746260001128">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M254.88,195.92l-54.56-92.08A15.87,15.87,0,0,0,186.55,96h0a15.85,15.85,0,0,0-13.76,7.84l-15.64,26.39a4,4,0,0,0,0,4.07l26.8,45.47a8.13,8.13,0,0,1-1.89,10.55,8,8,0,0,1-11.8-2.26L101.79,71.88a16,16,0,0,0-27.58,0L1.11,195.94a8,8,0,0,0,1,9.52A8.23,8.23,0,0,0,8.23,208H247.77a8.29,8.29,0,0,0,6.09-2.55A8,8,0,0,0,254.88,195.92ZM64.43,120,88,80l23.57,40ZM140,52a24,24,0,1,1,24,24A24,24,0,0,1,140,52Z" /></svg></span>
    </div>
      </div>
    </div>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gn1KOLgcn2">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gn1KOLgcn2 "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:left;--ta-mobile:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:21px;--size-tablet:19px;--size-mobile:17px;--lh:150%;--lh-tablet:130%;--lh-mobile:150%;--c:#242424;--tt:uppercase;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggn1KOLgcn2_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gM31bzm_Ad">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gM31bzm_Ad "
        style="--tt:default;--ta:left;--ta-mobile:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb-mobile:var(--g-s-xl)"
      >
      <div class="gp-flex" style="--jc:left;--jc-mobile:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--ta-mobile:left;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:#424242;--size:16px;--size-tablet:14px;--size-mobile:14px;word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.ggM31bzm_Ad_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div><div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gZGSJ2lQhg gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--mb:13px;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ta:left"
      class="gp-leading-[0] g0dvuFVpeh"
    >
      <div 
      data-id="g0dvuFVpeh"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:#096de3;--t:rotate(0deg);--w:33px;--w-tablet:33px;--w-mobile:33px;--h:33px;--h-tablet:33px;--h-mobile:33px;--minw:33px;--minw-tablet:33px;--minw-mobile:33px;--height-desktop:33px;--height-tablet:33px;--height-mobile:33px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817554949669224">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M154.21,133.69a12,12,0,0,1,.52,11.68l-16,32a12,12,0,1,1-21.46-10.74L124.58,152H112a12,12,0,0,1-10.73-17.37l16-32a12,12,0,1,1,21.46,10.74L131.42,128H144A12,12,0,0,1,154.21,133.69ZM104,24h48a12,12,0,0,0,0-24H104a12,12,0,0,0,0,24ZM204,60V228a28,28,0,0,1-28,28H80a28,28,0,0,1-28-28V60A28,28,0,0,1,80,32h96A28,28,0,0,1,204,60Zm-24,0a4,4,0,0,0-4-4H80a4,4,0,0,0-4,4V228a4,4,0,0,0,4,4h96a4,4,0,0,0,4-4Z" /></svg></span>
    </div>
      </div>
    </div>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gUwgy9nj-o">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gUwgy9nj-o "
        style="--ta:left;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:var(--g-s-l)"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:left;--ta-mobile:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:700;--ls:normal;--size:21px;--size-tablet:19px;--size-mobile:17px;--lh:150%;--lh-tablet:130%;--lh-mobile:150%;--c:#242424;--tt:uppercase;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggUwgy9nj-o_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gNbfTCHHGF">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gNbfTCHHGF "
        style="--tt:default;--ta:left;--ta-mobile:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small)"
      >
      <div class="gp-flex" style="--jc:left;--jc-mobile:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--ta-mobile:left;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:#424242;--size:16px;--size-tablet:14px;--size-mobile:14px;word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.ggNbfTCHHGF_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
    </div>
    </div>
   
    
    </div>
    </div>
  
            </section>
           
    
<style {% if section.settings.section_preload == "false" %} class="gps-link" type="text/disabled-css" {% endif %}>@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: normal;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmUiAw.woff) format('woff');
}
/* cyrillic-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmZiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmQiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmYiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmXiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* math */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalnoiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;
}
/* symbols */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjaln6iAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;
}
/* vietnamese */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmbiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmaiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmUiAz0klQmz24.woff) format('woff');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
</style>

{% schema %}
  {
    
    "name": "Section 5",
    "tag": "section",
    "class": "gps-555716577707098913 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/gardpro-us/apps/gempages-cro/app/shopify/edit?pageType=GP_STATIC&editorId=555716577622754081&sectionId=555716577707098913)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"gglPxaj6Wuj_text","label":"gglPxaj6Wuj_text","default":"Your Personal Health Coach. <span style=\"color:#076EE3;\">Always Within Reach.</span>"},{"type":"html","id":"ggVXswEMC83_label","label":"ggVXswEMC83_label","default":"<p>Shop the Men's Collection</p>"},{"type":"html","id":"gggsQOXFBPX_text","label":"gggsQOXFBPX_text","default":"<p>ADVANCED HEALTH TRACKING</p>"},{"type":"html","id":"ggZRyNkK4dg_text","label":"ggZRyNkK4dg_text","default":"<p>LONG-LASTING BATTERY</p>"},{"type":"html","id":"ggOl6PujOj7_text","label":"ggOl6PujOj7_text","default":"<p>DURABLE &amp; WATERPROOF</p>"},{"type":"html","id":"ggzSMKninFx_text","label":"ggzSMKninFx_text","default":"<p>IOS &amp; ANDROID COMPATIBILITY</p>"},{"type":"html","id":"ggNfxYKA6N2_text","label":"ggNfxYKA6N2_text","default":"<p><span style=\"color:#000000;\">Comprehensive Health Analytics</span></p>"},{"type":"html","id":"ggycE1OzIP9_text","label":"ggycE1OzIP9_text","default":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(0,0,0);font-size:18px;\">Stay fully informed with health watches for men that measure your heart health, oxygen levels, and ECG.</span></p>"},{"type":"html","id":"gg3H4Ol5UDn_text","label":"gg3H4Ol5UDn_text","default":"<p><span style=\"color:#000000;\">Your Personal Wellness Guide</span></p>"},{"type":"html","id":"ggOXJI5kkrU_text","label":"ggOXJI5kkrU_text","default":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(0,0,0);font-size:18px;\">From guided workouts to relaxation cues, these workout watches help you maintain balance—mind and body in sync.</span></p>"},{"type":"html","id":"ggn1KOLgcn2_text","label":"ggn1KOLgcn2_text","default":"<p><span style=\"color:#2B2B2B;\">Adventure-Grade Toughness</span></p>"},{"type":"html","id":"ggM31bzm_Ad_text","label":"ggM31bzm_Ad_text","default":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(0,0,0);font-size:18px;\">Built for every challenge, these rugged smartwatches withstand water, shock, and rugged terrain—perfect for the trailblazer.</span></p>"},{"type":"html","id":"ggUwgy9nj-o_text","label":"ggUwgy9nj-o_text","default":"<p>Elevated Endurance &amp; Smarts</p>"},{"type":"html","id":"ggNbfTCHHGF_text","label":"ggNbfTCHHGF_text","default":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(0,0,0);font-size:18px;\">A powerful battery, precise altitude metrics, and voice-activated controls support you in reaching every new summit.</span></p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
