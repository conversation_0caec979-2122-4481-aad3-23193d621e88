

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-555716577707164449.gps.gpsil [style*="--ai:"]{align-items:var(--ai)}.gps-555716577707164449.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-555716577707164449.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-555716577707164449.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-555716577707164449.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-555716577707164449.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-555716577707164449.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-555716577707164449.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-555716577707164449.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-555716577707164449.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-555716577707164449.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-555716577707164449.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-555716577707164449.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-555716577707164449.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-555716577707164449.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-555716577707164449.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-555716577707164449.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-555716577707164449.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-555716577707164449.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-555716577707164449.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-555716577707164449.gps.gpsil [style*="--bottom:"]{bottom:var(--bottom)}.gps-555716577707164449.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-555716577707164449.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-555716577707164449.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-555716577707164449.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-555716577707164449.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-555716577707164449.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-555716577707164449.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-555716577707164449.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-555716577707164449.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-555716577707164449.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-555716577707164449.gps.gpsil [style*="--left:"]{left:var(--left)}.gps-555716577707164449.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-555716577707164449.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-555716577707164449.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-555716577707164449.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-555716577707164449.gps.gpsil [style*="--ml:"]{margin-left:var(--ml)}.gps-555716577707164449.gps.gpsil [style*="--mt:"]{margin-top:var(--mt)}.gps-555716577707164449.gps.gpsil [style*="--maxw:"]{max-width:var(--maxw)}.gps-555716577707164449.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-555716577707164449.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-555716577707164449.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-555716577707164449.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-555716577707164449.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-555716577707164449.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-555716577707164449.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-555716577707164449.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-555716577707164449.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-555716577707164449.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-555716577707164449.gps.gpsil [style*="--right:"]{right:var(--right)}.gps-555716577707164449.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-555716577707164449.gps.gpsil [style*="--ts:"]{text-shadow:var(--ts)}.gps-555716577707164449.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-555716577707164449.gps.gpsil [style*="--top:"]{top:var(--top)}.gps-555716577707164449.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-555716577707164449.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-555716577707164449.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-555716577707164449.gps.gpsil [style*="--bottom-tablet:"]{bottom:var(--bottom-tablet)}.gps-555716577707164449.gps.gpsil [style*="--cg-tablet:"]{-moz-column-gap:var(--cg-tablet);column-gap:var(--cg-tablet)}.gps-555716577707164449.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-555716577707164449.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-555716577707164449.gps.gpsil [style*="--left-tablet:"]{left:var(--left-tablet)}.gps-555716577707164449.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-555716577707164449.gps.gpsil [style*="--ml-tablet:"]{margin-left:var(--ml-tablet)}.gps-555716577707164449.gps.gpsil [style*="--maxw-tablet:"]{max-width:var(--maxw-tablet)}.gps-555716577707164449.gps.gpsil [style*="--minw-tablet:"]{min-width:var(--minw-tablet)}.gps-555716577707164449.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-555716577707164449.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-555716577707164449.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-555716577707164449.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-555716577707164449.gps.gpsil [style*="--right-tablet:"]{right:var(--right-tablet)}.gps-555716577707164449.gps.gpsil [style*="--top-tablet:"]{top:var(--top-tablet)}.gps-555716577707164449.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-555716577707164449.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-555716577707164449.gps.gpsil [style*="--bottom-mobile:"]{bottom:var(--bottom-mobile)}.gps-555716577707164449.gps.gpsil [style*="--cg-mobile:"]{-moz-column-gap:var(--cg-mobile);column-gap:var(--cg-mobile)}.gps-555716577707164449.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-555716577707164449.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-555716577707164449.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-555716577707164449.gps.gpsil [style*="--left-mobile:"]{left:var(--left-mobile)}.gps-555716577707164449.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-555716577707164449.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-555716577707164449.gps.gpsil [style*="--ml-mobile:"]{margin-left:var(--ml-mobile)}.gps-555716577707164449.gps.gpsil [style*="--mt-mobile:"]{margin-top:var(--mt-mobile)}.gps-555716577707164449.gps.gpsil [style*="--maxw-mobile:"]{max-width:var(--maxw-mobile)}.gps-555716577707164449.gps.gpsil [style*="--minw-mobile:"]{min-width:var(--minw-mobile)}.gps-555716577707164449.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-555716577707164449.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-555716577707164449.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-555716577707164449.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-555716577707164449.gps.gpsil [style*="--right-mobile:"]{right:var(--right-mobile)}.gps-555716577707164449.gps.gpsil [style*="--top-mobile:"]{top:var(--top-mobile)}.gps-555716577707164449.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-555716577707164449.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-555716577707164449 .gp-rotate-0,.gps-555716577707164449 .gp-rotate-180,.gps-555716577707164449 .mobile\:gp-rotate-0,.gps-555716577707164449 .mobile\:gp-rotate-180,.gps-555716577707164449 .tablet\:gp-rotate-0,.gps-555716577707164449 .tablet\:gp-rotate-180{--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1}.gps-555716577707164449 .gp-shadow-md{--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000}.gps-555716577707164449 .gp-g-paragraph-1{font-family:var(--g-p1-ff);font-size:var(--g-p1-size);font-style:var(--g-p1-fs);font-weight:var(--g-p1-weight);letter-spacing:var(--g-p1-ls);line-height:var(--g-p1-lh)}.gps-555716577707164449 .gp-static{position:static}.gps-555716577707164449 .\!gp-absolute{position:absolute!important}.gps-555716577707164449 .gp-absolute{position:absolute}.gps-555716577707164449 .gp-relative{position:relative}.gps-555716577707164449 .gp-left-0{left:0}.gps-555716577707164449 .gp-right-0{right:0}.gps-555716577707164449 .gp-z-1{z-index:1}.gps-555716577707164449 .gp-z-2{z-index:2}.gps-555716577707164449 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-555716577707164449 .gp-my-0{margin-bottom:0;margin-top:0}.gps-555716577707164449 .gp-mb-0{margin-bottom:0}.gps-555716577707164449 .gp-block{display:block}.gps-555716577707164449 .\!gp-flex{display:flex!important}.gps-555716577707164449 .gp-flex{display:flex}.gps-555716577707164449 .gp-grid{display:grid}.gps-555716577707164449 .gp-contents{display:contents}.gps-555716577707164449 .\!gp-hidden{display:none!important}.gps-555716577707164449 .gp-hidden{display:none}.gps-555716577707164449 .gp-aspect-square{aspect-ratio:1/1}.gps-555716577707164449 .gp-h-auto{height:auto}.gps-555716577707164449 .gp-h-full{height:100%}.gps-555716577707164449 .\!gp-min-h-full{min-height:100%!important}.gps-555716577707164449 .gp-w-\[12px\]{width:12px}.gps-555716577707164449 .gp-w-full{width:100%}.gps-555716577707164449 .gp-max-w-full{max-width:100%}.gps-555716577707164449 .gp-flex-none{flex:none}.gps-555716577707164449 .gp-shrink-0{flex-shrink:0}.gps-555716577707164449 .gp-rotate-0{--tw-rotate:0deg}.gps-555716577707164449 .gp-rotate-0,.gps-555716577707164449 .gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-555716577707164449 .gp-rotate-180{--tw-rotate:180deg}.gps-555716577707164449 .gp-cursor-pointer{cursor:pointer}.gps-555716577707164449 .gp-select-none{-webkit-user-select:none;-moz-user-select:none;user-select:none}.gps-555716577707164449 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-555716577707164449 .\!gp-flex-row{flex-direction:row!important}.gps-555716577707164449 .gp-flex-row{flex-direction:row}.gps-555716577707164449 .gp-flex-col{flex-direction:column}.gps-555716577707164449 .\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-555716577707164449 .gp-items-center{align-items:center}.gps-555716577707164449 .gp-justify-center{justify-content:center}.gps-555716577707164449 .gp-justify-between{justify-content:space-between}.gps-555716577707164449 .gp-gap-2{gap:8px}.gps-555716577707164449 .gp-gap-y-0{row-gap:0}.gps-555716577707164449 .gp-overflow-hidden{overflow:hidden}.gps-555716577707164449 .gp-rounded-full{border-radius:9999px}.gps-555716577707164449 .gp-text-center{text-align:center}.gps-555716577707164449 .gp-text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128/var(--tw-text-opacity))}.gps-555716577707164449 .gp-shadow-md{--tw-shadow:0 4px 6px -1px rgba(0,0,0,.1),0 2px 4px -2px rgba(0,0,0,.1);--tw-shadow-colored:0 4px 6px -1px var(--tw-shadow-color),0 2px 4px -2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.gps-555716577707164449 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555716577707164449 .gp-duration-200{transition-duration:.2s}.gps-555716577707164449 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}@media (max-width:1024px){.gps-555716577707164449 .tablet\:gp-static{position:static}.gps-555716577707164449 .tablet\:\!gp-absolute{position:absolute!important}.gps-555716577707164449 .tablet\:gp-absolute{position:absolute}.gps-555716577707164449 .tablet\:gp-left-0{left:0}.gps-555716577707164449 .tablet\:gp-right-0{right:0}.gps-555716577707164449 .tablet\:gp-z-2{z-index:2}.gps-555716577707164449 .tablet\:gp-block{display:block}.gps-555716577707164449 .tablet\:\!gp-flex{display:flex!important}.gps-555716577707164449 .tablet\:\!gp-hidden{display:none!important}.gps-555716577707164449 .tablet\:gp-hidden{display:none}.gps-555716577707164449 .tablet\:gp-h-auto{height:auto}.gps-555716577707164449 .tablet\:\!gp-min-h-full{min-height:100%!important}.gps-555716577707164449 .tablet\:gp-flex-none{flex:none}.gps-555716577707164449 .tablet\:gp-rotate-0{--tw-rotate:0deg}.gps-555716577707164449 .tablet\:gp-rotate-0,.gps-555716577707164449 .tablet\:gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-555716577707164449 .tablet\:gp-rotate-180{--tw-rotate:180deg}.gps-555716577707164449 .tablet\:\!gp-flex-row{flex-direction:row!important}.gps-555716577707164449 .tablet\:gp-flex-row{flex-direction:row}.gps-555716577707164449 .tablet\:\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-555716577707164449 .tablet\:gp-px-0{padding-left:0;padding-right:0}}@media (max-width:767px){.gps-555716577707164449 .mobile\:gp-static{position:static}.gps-555716577707164449 .mobile\:\!gp-absolute{position:absolute!important}.gps-555716577707164449 .mobile\:gp-left-0{left:0}.gps-555716577707164449 .mobile\:gp-right-0{right:0}.gps-555716577707164449 .mobile\:gp-z-2{z-index:2}.gps-555716577707164449 .mobile\:gp-block{display:block}.gps-555716577707164449 .mobile\:\!gp-flex{display:flex!important}.gps-555716577707164449 .mobile\:\!gp-hidden{display:none!important}.gps-555716577707164449 .mobile\:gp-hidden{display:none}.gps-555716577707164449 .mobile\:gp-h-auto{height:auto}.gps-555716577707164449 .mobile\:\!gp-min-h-full{min-height:100%!important}.gps-555716577707164449 .mobile\:gp-flex-none{flex:none}.gps-555716577707164449 .mobile\:gp-rotate-0{--tw-rotate:0deg}.gps-555716577707164449 .mobile\:gp-rotate-0,.gps-555716577707164449 .mobile\:gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-555716577707164449 .mobile\:gp-rotate-180{--tw-rotate:180deg}.gps-555716577707164449 .mobile\:\!gp-flex-row{flex-direction:row!important}.gps-555716577707164449 .mobile\:gp-flex-row{flex-direction:row}.gps-555716577707164449 .mobile\:\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-555716577707164449 .mobile\:gp-px-0{padding-left:0;padding-right:0}}.gps-555716577707164449 .\[\&\>svg\]\:gp-h-full>svg{height:100%}.gps-555716577707164449 .\[\&\>svg\]\:gp-w-full>svg{width:100%}.gps-555716577707164449 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-555716577707164449 .\[\&_p\]\:gp-inline p{display:inline}.gps-555716577707164449 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-555716577707164449 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      enableLazyloadImage="true" id="gdHZzJu_tG" data-id="gdHZzJu_tG"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:var(--g-s-4xl);--pl:15px;--pb:var(--g-s-4xl);--pr:15px;--pt-mobile:32px;--pl-mobile:24px;--pb-mobile:32px;--pr-mobile:24px;--pt-tablet:var(--g-s-4xl);--pl-tablet:15px;--pb-tablet:var(--g-s-4xl);--pr-tablet:15px;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gdHZzJu_tG gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gb4Fd42zxO gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="gerlvm8Bxw" data-id="gerlvm8Bxw"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gerlvm8Bxw gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gADLa6H4Wk gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="grru5yfXXI" data-id="grru5yfXXI"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:63px;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="grru5yfXXI gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gwTzjoZAqj gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="g8MyVynCpW" data-id="g8MyVynCpW"
        style="--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--radius:var(--g-radius-small);--mb:var(--g-s-s);--cg:16px;--pc:center;--gtc:minmax(0, auto);--gtc-mobile:minmax(0, auto);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g8MyVynCpW gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--ai:normal;--jc:center;--o:0"
      class="g86v5MK6Nf gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gwjswY0Y8j">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gwjswY0Y8j "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small)"
      >
      <div class="gp-flex" style="--jc:center">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:center;--c:#242424;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-heading, heading);--weight:700;--ls:normal;--size:40px;--size-tablet:28px;--size-mobile:24px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.ggwjswY0Y8j_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gmC-d-m4fy">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gmC-d-m4fy "
        style="--tt:default;--ta:center;--bs:none;--bw:0px;--bc:transparent;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--radius:var(--g-radius-small);--mb:var(--g-s-l);--mb-mobile:0px"
      >
      <div class="gp-flex" style="--jc:center">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-g-paragraph-1 gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--line-clamp:0;--line-clamp-tablet:0;--line-clamp-mobile:0;--c:#575757;word-break:break-word;--radius:var(--g-radius-small);overflow:hidden"
        >{{ section.settings.ggmC-d-m4fy_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
    <gp-carousel data-id="gCNwj6c4Bb"  id="gp-root-carousel-gCNwj6c4Bb-{{section.id}}" class="  gp-group/carousel gp-flex" gp-data='{"id":"gCNwj6c4Bb-{{section.id}}","setting":{"childItem":["Slide 1","Slide 2","Slide 3","Slide 4"],"itemNumber":{"desktop":3,"tablet":2,"mobile":2},"sneakPeak":{"desktop":true},"sneakPeakType":{"desktop":"center","mobile":"forward"},"sneakPeakOffsetForward":{"desktop":50},"sneakPeakOffsetCenter":{"desktop":50},"vertical":{"desktop":false},"controlOverContent":{"desktop":true},"dot":{"desktop":true,"tablet":true,"mobile":true},"dotStyle":{"desktop":"inside","mobile":"outside"},"dotSize":{"desktop":12},"dotGapToCarousel":{"desktop":16},"dotColor":{"desktop":"bg-1"},"dotActiveColor":{"desktop":"line-3"},"navigationStyle":{"desktop":"inside","mobile":"inside"},"arrowCustom":"<svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"currentColor\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M7.62204 5.13313C7.78476 4.95562 8.04858 4.95562 8.21129 5.13313L12.378 9.67859C12.5407 9.8561 12.5407 10.1439 12.378 10.3214L8.21129 14.8669C8.04858 15.0444 7.78476 15.0444 7.62204 14.8669C7.45932 14.6894 7.45932 14.4016 7.62204 14.224L11.4941 10L7.62204 5.77596C7.45932 5.59845 7.45932 5.31064 7.62204 5.13313Z\"/>\n              </svg>\n              ","arrowCustomColor":"#000000","arrowIconSize":{"desktop":24},"arrowButtonSize":{"desktop":{"shapeValue":"1/1","shapeLinked":true,"width":"32px","height":"32px","padding":{"linked":true}}},"arrowBackgroundColor":"transparent","arrowBorder":{"desktop":{"borderType":"none","border":"none","borderWidth":"1px","width":"1px 1px 1px 1px","isCustom":false}},"roundedArrow":{"desktop":{"radiusType":"small"}},"arrowGapToEachSide":"16","showWhenHover":false,"rtl":false,"autoplay":true,"autoplayTimeout":2,"pauseOnHover":true,"runPreview":false,"enableDrag":{"desktop":false,"mobile":true},"loop":{"desktop":false},"animationMode":"ease-in","background":{"desktop":{"type":"color","color":"transparent","image":{"src":"","width":0,"height":0},"size":"cover","position":{"x":50,"y":50},"repeat":"no-repeat","attachment":"scroll"}},"label":true},"styles":{"fullWidth":{"desktop":false,"tablet":false,"mobile":false},"sizeSetting":{"desktop":{"shapeLinked":false,"width":"default","height":"auto"}},"playSpeed":500,"align":{"desktop":"center"},"borderContent":{"borderType":"none","border":"none","width":"1px 1px 1px 1px","position":"all","borderWidth":"1px","color":"#121212","isCustom":true},"roundedContent":{"radiusType":"none"},"hasActiveShadow":false,"carouselShadow":{"type":"shadow-1","distance":"4px","blur":"12px","spread":"0px","color":"#121212","angle":90},"spacing":{"desktop":16},"itemPadding":{"desktop":{"padding":{"type":"custom","top":"0px","left":"0px","bottom":"0px","right":"0px"}}}},"isHiddenArrowWhenDisabled":true}' style="--jc:center">
      <div class="gp-hidden gp-aspect-square gp-w-[12px] gp-rounded-full gp-shadow-md"></div>
      <div
        
        class="gp-relative gp-my-0 tablet:gp-px-0 gp-max-w-full mobile:gp-px-0 gp-carousel-wrapper mobile:gp-block tablet:gp-block gp-block gCNwj6c4Bb"
        style="--mb:63px;--mb-mobile:0px;--d:none;--d-tablet:none;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--w:var(--g-ct-w);--w-tablet:var(--g-ct-w);--w-mobile:var(--g-ct-w);--h:auto;--h-tablet:auto;--h-mobile:auto"
      >
        <div
          class="gp-h-full gp-relative gp-mx-auto gp-flex gp-items-center gp-justify-between mobile:!gp-flex-row tablet:!gp-flex-row !gp-flex-row"
          style="--h:100%;--h-tablet:100%;--h-mobile:auto;gap:16px"
        >
          
    <button
      type="button"
      aria-label='Carousel Back Arrow'
      class="gp-z-1 gp-carousel-action-back gem-slider-previous gCNwj6c4Bb-{{section.id}} gp-carousel-arrow-gCNwj6c4Bb gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-flex !gp-absolute gp-z-2 tablet:!gp-flex tablet:!gp-absolute tablet:gp-z-2 mobile:!gp-flex mobile:!gp-absolute mobile:gp-z-2"
      style="--left:16px;--top:initial;--right:initial;--bottom:;--left-tablet:16px;--top-tablet:initial;--right-tablet:initial;--bottom-tablet:;--left-mobile:16px;--top-mobile:initial;--right-mobile:initial;--bottom-mobile:;--d:flex;--d-tablet:flex;--d-mobile:flex;background-color:transparent;--w:32px;--h:32px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-180 tablet:gp-rotate-180 mobile:gp-rotate-180"
  style="--c:#000000;--w:24px;--w-tablet:24px;--w-mobile:24px;--h:24px;--h-tablet:24px;--h-mobile:24px"
  >
    <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M7.62204 5.13313C7.78476 4.95562 8.04858 4.95562 8.21129 5.13313L12.378 9.67859C12.5407 9.8561 12.5407 10.1439 12.378 10.3214L8.21129 14.8669C8.04858 15.0444 7.78476 15.0444 7.62204 14.8669C7.45932 14.6894 7.45932 14.4016 7.62204 14.224L11.4941 10L7.62204 5.77596C7.45932 5.59845 7.45932 5.31064 7.62204 5.13313Z"/>
              </svg>
              
    </div>
      <style>
    .gp-carousel-arrow-gCNwj6c4Bb {
      border-radius: var(--g-radius-small);
    }
    .gp-carousel-arrow-gCNwj6c4Bb::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      border-radius: var(--g-radius-small);
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .gp-carousel-arrow-gCNwj6c4Bb {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-gCNwj6c4Bb::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
    @media only screen and (max-width: 768px) {
      .gp-carousel-arrow-gCNwj6c4Bb {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-gCNwj6c4Bb::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
  </style>
    </button>
  
          <div id="gp-carousel-gCNwj6c4Bb-{{section.id}}" class="gem-slider gp-h-full gp-overflow-hidden gp-select-none mobile:!gp-flex-nowrap tablet:!gp-flex-nowrap !gp-flex-nowrap mobile:!gp-min-h-full tablet:!gp-min-h-full !gp-min-h-full"
          style="--cg-mobile:16px;--cg-tablet:16px;--cg:16px">
          
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      enableLazyloadImage="true" tag="CarouselItem" label="Carousel Item" type="component"
      style="--bgc:transparent;--minw:calc(100% / 2 - 8px);--minw-tablet:calc(100% / 1 - 0px);--minw-mobile:calc(100% / 1.5 - 5.333333333333333px);--maxw:calc(100% / 2 - 8px);--maxw-tablet:calc(100% / 1 - 0px);--maxw-mobile:calc(100% / 1.5 - 5.333333333333333px);--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ml:calc((100% / 2 - 8px + 16px) * 1.5 - (100% / 2 - 8px + 16px));--ml-tablet:calc((100% / 1 - 0px + 16px) * 1.5 - (100% / 1 - 0px + 16px));--ml-mobile:0px;--pl:0px;--pr:0px;--pt:0px;--pb:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-gCNwj6c4Bb gz9sa-y4cU"
      data-index="0"
    >
      <div
        class="gp-w-full gp-h-full",
        style="--shadow:none"
      >
      <div
    
     data-id="g-tl1OUE8J"
    role="presentation"
    class="gp-group/image g-tl1OUE8J gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:20px;--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/image_2023_08_21T04_29_54_024Z_768x.png?v=1692879045" />
      <source media="(max-width: 1024px)" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/image_2023_08_21T04_29_54_024Z_1024x.png?v=1692879045" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/image_2023_08_21T04_29_54_024Z.png?v=1692879045"
        alt=""
        fetchpriority="high"
        style="--aspect:auto;--objf:cover;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto;--btrr:12px;--bblr:12px;--bbrr:12px;--btlr:12px;--radiusType:custom"
      />
    
  </picture>
      </div>
  </div>
      </div>
    </div>
  
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      enableLazyloadImage="true" tag="CarouselItem" label="Carousel Item" type="component"
      style="--bgc:transparent;--minw:calc(100% / 2 - 8px);--minw-tablet:calc(100% / 1 - 0px);--minw-mobile:calc(100% / 1.5 - 5.333333333333333px);--maxw:calc(100% / 2 - 8px);--maxw-tablet:calc(100% / 1 - 0px);--maxw-mobile:calc(100% / 1.5 - 5.333333333333333px);--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ml:0px;--ml-tablet:0px;--ml-mobile:0px;--pl:0px;--pr:0px;--pt:0px;--pb:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-gCNwj6c4Bb gl7a2EP0IL"
      data-index="1"
    >
      <div
        class="gp-w-full gp-h-full",
        style="--shadow:none"
      >
      <div
    
     data-id="geLtHKWslh"
    role="presentation"
    class="gp-group/image geLtHKWslh gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/health_series_bg_768x.svg?v=**********" />
      <source media="(max-width: 1024px)" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/health_series_bg_1024x.svg?v=**********" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/health_series_bg.svg?v=**********"
        alt=""
        fetchpriority="high"
        style="--aspect:auto;--objf:cover;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
      </div>
    </div>
  
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      enableLazyloadImage="true" tag="CarouselItem" label="Carousel Item" type="component"
      style="--bgc:transparent;--minw:calc(100% / 2 - 8px);--minw-tablet:calc(100% / 1 - 0px);--minw-mobile:calc(100% / 1.5 - 5.333333333333333px);--maxw:calc(100% / 2 - 8px);--maxw-tablet:calc(100% / 1 - 0px);--maxw-mobile:calc(100% / 1.5 - 5.333333333333333px);--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ml:0px;--ml-tablet:0px;--ml-mobile:0px;--pl:0px;--pr:0px;--pt:0px;--pb:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-gCNwj6c4Bb g-FqWmszP8"
      data-index="2"
    >
      <div
        class="gp-w-full gp-h-full",
        style="--shadow:none"
      >
      <div
    
     data-id="gCRZ6lBUMB"
    role="presentation"
    class="gp-group/image gCRZ6lBUMB gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mt:52px;--mb:20px;--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Untitled_design_22_768x.jpg?v=1703431825" />
      <source media="(max-width: 1024px)" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Untitled_design_22_1024x.jpg?v=1703431825" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Untitled_design_22.jpg?v=1703431825"
        alt=""
        fetchpriority="high"
        style="--aspect:auto;--objf:cover;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto;--btrr:12px;--bblr:12px;--bbrr:12px;--btlr:12px;--radiusType:custom"
      />
    
  </picture>
      </div>
  </div>
      </div>
    </div>
  
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      enableLazyloadImage="true" tag="CarouselItem" label="Carousel Item" type="component"
      style="--bgc:transparent;--minw:calc(100% / 2 - 8px);--minw-tablet:calc(100% / 1 - 0px);--minw-mobile:calc(100% / 1.5 - 5.333333333333333px);--maxw:calc(100% / 2 - 8px);--maxw-tablet:calc(100% / 1 - 0px);--maxw-mobile:calc(100% / 1.5 - 5.333333333333333px);--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--ml:0px;--ml-tablet:0px;--ml-mobile:0px;--pl:0px;--pr:0px;--pt:0px;--pb:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-gCNwj6c4Bb g3hhtEhZsD"
      data-index="3"
    >
      <div
        class="gp-w-full gp-h-full",
        style="--shadow:none"
      >
      <div
    
     data-id="gDmyKLN42E"
    role="presentation"
    class="gp-group/image gDmyKLN42E gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/smart_watches_bg_768x.svg?v=1722523515" />
      <source media="(max-width: 1024px)" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/smart_watches_bg_1024x.svg?v=1722523515" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/smart_watches_bg.svg?v=1722523515"
        alt=""
        fetchpriority="high"
        style="--aspect:auto;--objf:cover;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
      </div>
    </div>
  
          </div>
          
    <button
      type="button"
      aria-label='Carousel Next Arrow'
      class="gp-z-1 gp-carousel-action-next gem-slider-next gCNwj6c4Bb-{{section.id}} gp-carousel-arrow-gCNwj6c4Bb gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-flex !gp-absolute gp-z-2 tablet:!gp-flex tablet:!gp-absolute tablet:gp-z-2 mobile:!gp-flex mobile:!gp-absolute mobile:gp-z-2"
      style="--left:initial;--top:;--right:16px;--bottom:initial;--left-tablet:initial;--top-tablet:;--right-tablet:16px;--bottom-tablet:initial;--left-mobile:initial;--top-mobile:;--right-mobile:16px;--bottom-mobile:initial;--d:flex;--d-tablet:flex;--d-mobile:flex;background-color:transparent;--w:32px;--h:32px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-0 tablet:gp-rotate-0 mobile:gp-rotate-0"
  style="--c:#000000;--w:24px;--w-tablet:24px;--w-mobile:24px;--h:24px;--h-tablet:24px;--h-mobile:24px"
  >
    <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M7.62204 5.13313C7.78476 4.95562 8.04858 4.95562 8.21129 5.13313L12.378 9.67859C12.5407 9.8561 12.5407 10.1439 12.378 10.3214L8.21129 14.8669C8.04858 15.0444 7.78476 15.0444 7.62204 14.8669C7.45932 14.6894 7.45932 14.4016 7.62204 14.224L11.4941 10L7.62204 5.77596C7.45932 5.59845 7.45932 5.31064 7.62204 5.13313Z"/>
              </svg>
              
    </div>
      <style>
    .gp-carousel-arrow-gCNwj6c4Bb {
      border-radius: var(--g-radius-small);
    }
    .gp-carousel-arrow-gCNwj6c4Bb::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      border-radius: var(--g-radius-small);
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .gp-carousel-arrow-gCNwj6c4Bb {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-gCNwj6c4Bb::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
    @media only screen and (max-width: 768px) {
      .gp-carousel-arrow-gCNwj6c4Bb {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-gCNwj6c4Bb::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
  </style>
    </button>
  
        </div>
        <div
    class="gp-items-center gp-justify-center gp-gap-2 gp-text-center gp-carousel-dot-container gp-carousel-dot-container-gCNwj6c4Bb-{{section.id}} gp-absolute gp-flex-row gp-left-0 gp-right-0 tablet:gp-absolute tablet:gp-flex-row tablet:gp-left-0 tablet:gp-right-0 mobile:gp-static mobile:gp-flex-row mobile:gp-left-0 mobile:gp-right-0"
    style="--bottom:16px;--bottom-tablet:16px;--mt-mobile:16px;--d:flex;--d-tablet:flex;--d-mobile:flex"
 ></div>
      </div>
    </gp-carousel>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-carousel-v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>

  
    <gp-carousel data-id="gSnoVrcSLk"  id="gp-root-carousel-gSnoVrcSLk-{{section.id}}" class="  gp-group/carousel gp-flex" gp-data='{"id":"gSnoVrcSLk-{{section.id}}","setting":{"animationMode":"ease-in","arrowBackgroundColor":"transparent","arrowBorder":{"desktop":{"border":"none","borderType":"none","borderWidth":"1px","isCustom":false,"width":"1px 1px 1px 1px"}},"arrowButtonSize":{"desktop":{"height":"32px","padding":{"linked":true},"shapeLinked":true,"shapeValue":"1/1","width":"32px"}},"arrowCustom":"<svg width=\"20\" height=\"20\" viewBox=\"0 0 20 20\" fill=\"currentColor\" xmlns=\"http://www.w3.org/2000/svg\">\n              <path fill-rule=\"evenodd\" clip-rule=\"evenodd\" d=\"M7.62204 5.13313C7.78476 4.95562 8.04858 4.95562 8.21129 5.13313L12.378 9.67859C12.5407 9.8561 12.5407 10.1439 12.378 10.3214L8.21129 14.8669C8.04858 15.0444 7.78476 15.0444 7.62204 14.8669C7.45932 14.6894 7.45932 14.4016 7.62204 14.224L11.4941 10L7.62204 5.77596C7.45932 5.59845 7.45932 5.31064 7.62204 5.13313Z\"/>\n              </svg>\n              ","arrowCustomColor":"#000000","arrowGapToEachSide":"16","arrowIconSize":{"desktop":24},"autoplay":false,"autoplayTimeout":2,"background":{"desktop":{"attachment":"scroll","color":"transparent","image":{"height":0,"src":"","width":0},"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"childItem":["Slide 1","Slide 2","Slide 3"],"controlOverContent":{"desktop":true},"dot":{"desktop":true,"mobile":true,"tablet":true},"dotActiveColor":{"desktop":"line-3"},"dotColor":{"desktop":"bg-1"},"dotGapToCarousel":{"desktop":16},"dotSize":{"desktop":12},"dotStyle":{"desktop":"none"},"enableDrag":{"desktop":true},"itemNumber":{"desktop":3,"mobile":2,"tablet":3},"label":true,"loop":{"desktop":false},"navigationStyle":{"desktop":"none"},"pauseOnHover":true,"roundedArrow":{"desktop":{"radiusType":"small"}},"rtl":false,"runPreview":false,"showWhenHover":false,"sneakPeak":{"desktop":false,"mobile":true},"sneakPeakOffsetCenter":{"desktop":50},"sneakPeakOffsetForward":{"desktop":50},"sneakPeakType":{"desktop":"forward"},"vertical":{"desktop":false}},"styles":{"align":{"desktop":"center"},"playSpeed":500,"sizeSetting":{"desktop":{"height":"auto","width":"100%"},"mobile":{"width":"100%"},"tablet":{"width":"100%"}},"spacing":{"desktop":"20"}},"isHiddenArrowWhenDisabled":true}' style="--jc:center">
      <div class="gp-hidden gp-aspect-square gp-w-[12px] gp-rounded-full gp-shadow-md"></div>
      <div
        
        class="gp-relative gp-my-0 tablet:gp-px-0 gp-max-w-full mobile:gp-px-0 gp-carousel-wrapper mobile:gp-block tablet:gp-block gp-block gSnoVrcSLk"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:none;--d-tablet:block;--op:100%;--mb:0px;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto"
      >
        <div
          class="gp-h-full gp-relative gp-mx-auto gp-flex gp-items-center gp-justify-between mobile:!gp-flex-row tablet:!gp-flex-row !gp-flex-row"
          style="--h:100%;--h-tablet:100%;--h-mobile:100%;gap:16px"
        >
          
    <button
      type="button"
      aria-label='Carousel Back Arrow'
      class="gp-z-1 gp-carousel-action-back gem-slider-previous gSnoVrcSLk-{{section.id}} gp-carousel-arrow-gSnoVrcSLk gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-hidden tablet:!gp-hidden mobile:!gp-hidden"
      style="--left:initial;--top:initial;--right:initial;--bottom:;--left-tablet:initial;--top-tablet:initial;--right-tablet:initial;--bottom-tablet:;--left-mobile:initial;--top-mobile:initial;--right-mobile:initial;--bottom-mobile:;--d:none;--d-tablet:none;--d-mobile:none;background-color:transparent;--w:32px;--h:32px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-180 tablet:gp-rotate-180 mobile:gp-rotate-180"
  style="--c:#000000;--w:24px;--w-tablet:24px;--w-mobile:24px;--h:24px;--h-tablet:24px;--h-mobile:24px"
  >
    <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M7.62204 5.13313C7.78476 4.95562 8.04858 4.95562 8.21129 5.13313L12.378 9.67859C12.5407 9.8561 12.5407 10.1439 12.378 10.3214L8.21129 14.8669C8.04858 15.0444 7.78476 15.0444 7.62204 14.8669C7.45932 14.6894 7.45932 14.4016 7.62204 14.224L11.4941 10L7.62204 5.77596C7.45932 5.59845 7.45932 5.31064 7.62204 5.13313Z"/>
              </svg>
              
    </div>
      <style>
    .gp-carousel-arrow-gSnoVrcSLk {
      border-radius: var(--g-radius-small);
    }
    .gp-carousel-arrow-gSnoVrcSLk::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      border-radius: var(--g-radius-small);
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .gp-carousel-arrow-gSnoVrcSLk {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-gSnoVrcSLk::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
    @media only screen and (max-width: 768px) {
      .gp-carousel-arrow-gSnoVrcSLk {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-gSnoVrcSLk::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
  </style>
    </button>
  
          <div id="gp-carousel-gSnoVrcSLk-{{section.id}}" class="gem-slider gp-h-full gp-overflow-hidden gp-select-none mobile:!gp-flex-nowrap tablet:!gp-flex-nowrap !gp-flex-nowrap mobile:!gp-min-h-full tablet:!gp-min-h-full !gp-min-h-full"
          style="--cg-mobile:20px;--cg-tablet:20px;--cg:20px">
          
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      enableLazyloadImage="true" label="Carousel Item" tag="CarouselItem" type="component"
      style="--bgc:transparent;--minw:calc(100% / 3 - 13.333333333333334px);--minw-tablet:calc(100% / 3 - 13.333333333333334px);--minw-mobile:calc(100% / 1.5 - 6.666666666666667px);--maxw:calc(100% / 3 - 13.333333333333334px);--maxw-tablet:calc(100% / 3 - 13.333333333333334px);--maxw-mobile:calc(100% / 1.5 - 6.666666666666667px);--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ml:0px;--ml-tablet:0px;--ml-mobile:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-gSnoVrcSLk gieTEOHBfq"
      data-index="0"
    >
      <div
        class="gp-w-full gp-h-full",
        
      >
      <div
    
     data-id="gXk_35tiLT"
    role="presentation"
    class="gp-group/image gXk_35tiLT gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:20px;--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/image_2023_08_21T04_29_54_024Z_768x.png?v=1692879045" />
      <source media="(max-width: 1024px)" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/image_2023_08_21T04_29_54_024Z_1024x.png?v=1692879045" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/image_2023_08_21T04_29_54_024Z.png?v=1692879045"
        alt=""
        fetchpriority="high"
        style="--aspect:auto;--objf:cover;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto;--btrr:24px;--bblr:24px;--bbrr:24px;--btlr:24px;--radiusType:custom"
      />
    
  </picture>
      </div>
  </div><div
    
     data-id="gNu4sPhv33"
    role="presentation"
    class="gp-group/image gNu4sPhv33 gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/health_series_bg_768x.svg?v=**********" />
      <source media="(max-width: 1024px)" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/health_series_bg_1024x.svg?v=**********" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/health_series_bg.svg?v=**********"
        alt=""
        fetchpriority="high"
        style="--aspect:auto;--objf:cover;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
      </div>
    </div>
  
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      enableLazyloadImage="true" label="Carousel Item" tag="CarouselItem" type="component"
      style="--bgc:transparent;--minw:calc(100% / 3 - 13.333333333333334px);--minw-tablet:calc(100% / 3 - 13.333333333333334px);--minw-mobile:calc(100% / 1.5 - 6.666666666666667px);--maxw:calc(100% / 3 - 13.333333333333334px);--maxw-tablet:calc(100% / 3 - 13.333333333333334px);--maxw-mobile:calc(100% / 1.5 - 6.666666666666667px);--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ml:0px;--ml-tablet:0px;--ml-mobile:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-gSnoVrcSLk gGbWRTCyUy"
      data-index="1"
    >
      <div
        class="gp-w-full gp-h-full",
        
      >
      <div
    
     data-id="g4xwQxLo-M"
    role="presentation"
    class="gp-group/image g4xwQxLo-M gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mt:52px;--mb:20px;--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Untitled_design_22_768x.jpg?v=1703431825" />
      <source media="(max-width: 1024px)" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Untitled_design_22_1024x.jpg?v=1703431825" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Untitled_design_22.jpg?v=1703431825"
        alt=""
        fetchpriority="high"
        style="--aspect:auto;--objf:cover;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto;--btrr:24px;--bblr:24px;--bbrr:24px;--btlr:24px;--radiusType:custom"
      />
    
  </picture>
      </div>
  </div><div
    
     data-id="grURMqlQsw"
    role="presentation"
    class="gp-group/image grURMqlQsw gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:0px;--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/smart_watches_bg_768x.svg?v=1722523515" />
      <source media="(max-width: 1024px)" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/smart_watches_bg_1024x.svg?v=1722523515" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/smart_watches_bg.svg?v=1722523515"
        alt=""
        fetchpriority="high"
        style="--aspect:auto;--objf:cover;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto"
      />
    
  </picture>
      </div>
  </div>
      </div>
    </div>
  
    <div
      media-type="{{media.media_type}}"
      media-poster="{{media.preview_image.src}}"
      media-source-url="{{media.sources.first.url}}"
      media-last-source-url="{{mediaSourceUrl}}"
      external-id="{{media.external_id}}"
      grid-index="{{forloop.index}}"
      
      enableLazyloadImage="true" label="Carousel Item" tag="CarouselItem" type="component"
      style="--bgc:transparent;--minw:calc(100% / 3 - 13.333333333333334px);--minw-tablet:calc(100% / 3 - 13.333333333333334px);--minw-mobile:calc(100% / 1.5 - 6.666666666666667px);--maxw:calc(100% / 3 - 13.333333333333334px);--maxw-tablet:calc(100% / 3 - 13.333333333333334px);--maxw-mobile:calc(100% / 1.5 - 6.666666666666667px);--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--ml:0px;--ml-tablet:0px;--ml-mobile:0px"
      class="gem-slider-item gp-w-full gem-slider-item- gp-child-item-gSnoVrcSLk gKQDoOWFTy"
      data-index="2"
    >
      <div
        class="gp-w-full gp-h-full",
        
      >
      <div
    
     data-id="gAcBXDk2Rz"
    role="presentation"
    class="gp-group/image gAcBXDk2Rz gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:20px;--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Untitled_design_50_768x.jpg?v=1727426429" />
      <source media="(max-width: 1024px)" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Untitled_design_50_1024x.jpg?v=1727426429" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/Untitled_design_50.jpg?v=1727426429"
        alt=""
        fetchpriority="high"
        style="--aspect:auto;--objf:cover;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto;--btrr:24px;--bblr:24px;--bbrr:24px;--btlr:24px;--radiusType:custom"
      />
    
  </picture>
      </div>
  </div><div
    
     data-id="ge_gPGdNl2"
    role="presentation"
    class="gp-group/image ge_gPGdNl2 gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:0px;--ta:left"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:left"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/pdp_col_2_768x.svg?v=1722524196" />
      <source media="(max-width: 1024px)" srcset="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/pdp_col_2_1024x.svg?v=1722524196" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="https://cdn.shopify.com/s/files/1/0733/7908/6636/files/pdp_col_2.svg?v=1722524196"
        alt=""
        fetchpriority="high"
        style="--aspect:auto;--objf:cover;--w:100%;--w-tablet:100%;--w-mobile:100%;--h:auto;--h-tablet:auto;--h-mobile:auto;--btrr:24px;--bblr:24px;--bbrr:24px;--btlr:24px;--radiusType:custom"
      />
    
  </picture>
      </div>
  </div>
      </div>
    </div>
  
          </div>
          
    <button
      type="button"
      aria-label='Carousel Next Arrow'
      class="gp-z-1 gp-carousel-action-next gem-slider-next gSnoVrcSLk-{{section.id}} gp-carousel-arrow-gSnoVrcSLk gp-items-center gp-justify-center gp-overflow-hidden gp-shrink-0  gp-cursor-pointer gp-text-gray-500 !gp-hidden tablet:!gp-hidden mobile:!gp-hidden"
      style="--left:initial;--top:;--right:initial;--bottom:initial;--left-tablet:initial;--top-tablet:;--right-tablet:initial;--bottom-tablet:initial;--left-mobile:initial;--top-mobile:;--right-mobile:initial;--bottom-mobile:initial;--d:none;--d-tablet:none;--d-mobile:none;background-color:transparent;--w:32px;--h:32px"
      onClick={onClick}
      
    >
      <div 
  class="gp-flex gp-items-center gp-justify-center [&>svg]:gp-h-full [&>svg]:gp-w-full gp-rotate-0 tablet:gp-rotate-0 mobile:gp-rotate-0"
  style="--c:#000000;--w:24px;--w-tablet:24px;--w-mobile:24px;--h:24px;--h-tablet:24px;--h-mobile:24px"
  >
    <svg width="20" height="20" viewBox="0 0 20 20" fill="currentColor" xmlns="http://www.w3.org/2000/svg">
              <path fill-rule="evenodd" clip-rule="evenodd" d="M7.62204 5.13313C7.78476 4.95562 8.04858 4.95562 8.21129 5.13313L12.378 9.67859C12.5407 9.8561 12.5407 10.1439 12.378 10.3214L8.21129 14.8669C8.04858 15.0444 7.78476 15.0444 7.62204 14.8669C7.45932 14.6894 7.45932 14.4016 7.62204 14.224L11.4941 10L7.62204 5.77596C7.45932 5.59845 7.45932 5.31064 7.62204 5.13313Z"/>
              </svg>
              
    </div>
      <style>
    .gp-carousel-arrow-gSnoVrcSLk {
      border-radius: var(--g-radius-small);
    }
    .gp-carousel-arrow-gSnoVrcSLk::before {
      content: '';
      height: 100%;
      width: 100%;
      position: absolute;
      pointer-events: none;
      z-index: 10;
      border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
      border-radius: var(--g-radius-small);
    }
    @media only screen and (max-width: 1024px) and (min-width: 768px) {
      .gp-carousel-arrow-gSnoVrcSLk {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-gSnoVrcSLk::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
    @media only screen and (max-width: 768px) {
      .gp-carousel-arrow-gSnoVrcSLk {
        border-radius: var(--g-radius-small);
      }
      .gp-carousel-arrow-gSnoVrcSLk::before {
        border-style: none;
  border-width: 1px 1px 1px 1px;
  
  
        border-radius: var(--g-radius-small);
      }
    }
  </style>
    </button>
  
        </div>
        <div
    class="gp-items-center gp-justify-center gp-gap-2 gp-text-center gp-carousel-dot-container gp-carousel-dot-container-gSnoVrcSLk-{{section.id}} gp-static gp-flex-row gp-left-0 gp-right-0 tablet:gp-static tablet:gp-flex-row tablet:gp-left-0 tablet:gp-right-0 mobile:gp-static mobile:gp-flex-row mobile:gp-left-0 mobile:gp-right-0"
    style="--bottom:16px;--bottom-tablet:16px;--bottom-mobile:16px;--d:none;--d-tablet:none;--d-mobile:none"
 ></div>
      </div>
    </gp-carousel>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-carousel-v2.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>

  
    </div>
    </div>
   
    
    </div>
    </div>
  
            </section>
           
    
<style {% if section.settings.section_preload == "false" %} class="gps-link" type="text/disabled-css" {% endif %}>@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: normal;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmUiAw.woff) format('woff');
}
/* cyrillic-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmZiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmQiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmYiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmXiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* math */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalnoiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;
}
/* symbols */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjaln6iAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;
}
/* vietnamese */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmbiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmaiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 700;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWuYjalmUiAz0klQmz24.woff) format('woff');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
</style>

{% schema %}
  {
    
    "name": "Section 7",
    "tag": "section",
    "class": "gps-555716577707164449 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/gardpro-us/apps/gempages-cro/app/shopify/edit?pageType=GP_STATIC&editorId=555716577622754081&sectionId=555716577707164449)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"ggwjswY0Y8j_text","label":"ggwjswY0Y8j_text","default":"ELEVATE EVERY SINGLE MOMENT"},{"type":"html","id":"ggmC-d-m4fy_text","label":"ggmC-d-m4fy_text","default":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(0,0,0);font-size:18px;\">From workouts to adventures, our men's smartwatches are built to keep you moving every step of the way.</span></p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
