

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-555716577707229985.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-555716577707229985.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-555716577707229985.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-555716577707229985.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-555716577707229985.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-555716577707229985.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-555716577707229985.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-555716577707229985.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-555716577707229985.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-555716577707229985.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-555716577707229985.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-555716577707229985.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-555716577707229985.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-555716577707229985.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-555716577707229985.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-555716577707229985.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-555716577707229985.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-555716577707229985.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-555716577707229985.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-555716577707229985.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-555716577707229985.gps.gpsil [style*="--bottom:"]{bottom:var(--bottom)}.gps-555716577707229985.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-555716577707229985.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-555716577707229985.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-555716577707229985.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-555716577707229985.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-555716577707229985.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-555716577707229985.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-555716577707229985.gps.gpsil [style*="--gg:"]{grid-gap:var(--gg)}.gps-555716577707229985.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-555716577707229985.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-555716577707229985.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-555716577707229985.gps.gpsil [style*="--left:"]{left:var(--left)}.gps-555716577707229985.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-555716577707229985.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-555716577707229985.gps.gpsil [style*="--tdt:"]{text-decoration-thickness:var(--tdt)}.gps-555716577707229985.gps.gpsil [style*="--tdc:"]{text-decoration-color:var(--tdc)}.gps-555716577707229985.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-555716577707229985.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-555716577707229985.gps.gpsil [style*="--ml:"]{margin-left:var(--ml)}.gps-555716577707229985.gps.gpsil [style*="--mt:"]{margin-top:var(--mt)}.gps-555716577707229985.gps.gpsil [style*="--maxw:"]{max-width:var(--maxw)}.gps-555716577707229985.gps.gpsil [style*="--minw:"]{min-width:var(--minw)}.gps-555716577707229985.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-555716577707229985.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-555716577707229985.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-555716577707229985.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-555716577707229985.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-555716577707229985.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-555716577707229985.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-555716577707229985.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-555716577707229985.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-555716577707229985.gps.gpsil [style*="--pos:"]{position:var(--pos)}.gps-555716577707229985.gps.gpsil [style*="--right:"]{right:var(--right)}.gps-555716577707229985.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-555716577707229985.gps.gpsil [style*="--ts:"]{text-shadow:var(--ts)}.gps-555716577707229985.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-555716577707229985.gps.gpsil [style*="--top:"]{top:var(--top)}.gps-555716577707229985.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-555716577707229985.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-555716577707229985.gps.gpsil [style*="--z:"]{z-index:var(--z)}.gps-555716577707229985.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-555716577707229985.gps.gpsil [style*="--aspect-tablet:"]{aspect-ratio:var(--aspect-tablet)}.gps-555716577707229985.gps.gpsil [style*="--bgi-tablet:"]{background-image:var(--bgi-tablet)}.gps-555716577707229985.gps.gpsil [style*="--bottom-tablet:"]{bottom:var(--bottom-tablet)}.gps-555716577707229985.gps.gpsil [style*="--cg-tablet:"]{-moz-column-gap:var(--cg-tablet);column-gap:var(--cg-tablet)}.gps-555716577707229985.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-555716577707229985.gps.gpsil [style*="--gtc-tablet:"]{grid-template-columns:var(--gtc-tablet)}.gps-555716577707229985.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-555716577707229985.gps.gpsil [style*="--left-tablet:"]{left:var(--left-tablet)}.gps-555716577707229985.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-555716577707229985.gps.gpsil [style*="--mb-tablet:"]{margin-bottom:var(--mb-tablet)}.gps-555716577707229985.gps.gpsil [style*="--ml-tablet:"]{margin-left:var(--ml-tablet)}.gps-555716577707229985.gps.gpsil [style*="--maxw-tablet:"]{max-width:var(--maxw-tablet)}.gps-555716577707229985.gps.gpsil [style*="--minw-tablet:"]{min-width:var(--minw-tablet)}.gps-555716577707229985.gps.gpsil [style*="--o-tablet:"]{order:var(--o-tablet)}.gps-555716577707229985.gps.gpsil [style*="--pc-tablet:"]{place-content:var(--pc-tablet)}.gps-555716577707229985.gps.gpsil [style*="--pb-tablet:"]{padding-bottom:var(--pb-tablet)}.gps-555716577707229985.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-555716577707229985.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-555716577707229985.gps.gpsil [style*="--pt-tablet:"]{padding-top:var(--pt-tablet)}.gps-555716577707229985.gps.gpsil [style*="--pos-tablet:"]{position:var(--pos-tablet)}.gps-555716577707229985.gps.gpsil [style*="--right-tablet:"]{right:var(--right-tablet)}.gps-555716577707229985.gps.gpsil [style*="--ta-tablet:"]{text-align:var(--ta-tablet)}.gps-555716577707229985.gps.gpsil [style*="--top-tablet:"]{top:var(--top-tablet)}.gps-555716577707229985.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-555716577707229985.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-555716577707229985.gps.gpsil [style*="--aspect-mobile:"]{aspect-ratio:var(--aspect-mobile)}.gps-555716577707229985.gps.gpsil [style*="--bgi-mobile:"]{background-image:var(--bgi-mobile)}.gps-555716577707229985.gps.gpsil [style*="--bottom-mobile:"]{bottom:var(--bottom-mobile)}.gps-555716577707229985.gps.gpsil [style*="--cg-mobile:"]{-moz-column-gap:var(--cg-mobile);column-gap:var(--cg-mobile)}.gps-555716577707229985.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-555716577707229985.gps.gpsil [style*="--gg-mobile:"]{grid-gap:var(--gg-mobile)}.gps-555716577707229985.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-555716577707229985.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-555716577707229985.gps.gpsil [style*="--jc-mobile:"]{justify-content:var(--jc-mobile)}.gps-555716577707229985.gps.gpsil [style*="--left-mobile:"]{left:var(--left-mobile)}.gps-555716577707229985.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-555716577707229985.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-555716577707229985.gps.gpsil [style*="--ml-mobile:"]{margin-left:var(--ml-mobile)}.gps-555716577707229985.gps.gpsil [style*="--maxw-mobile:"]{max-width:var(--maxw-mobile)}.gps-555716577707229985.gps.gpsil [style*="--minw-mobile:"]{min-width:var(--minw-mobile)}.gps-555716577707229985.gps.gpsil [style*="--o-mobile:"]{order:var(--o-mobile)}.gps-555716577707229985.gps.gpsil [style*="--pc-mobile:"]{place-content:var(--pc-mobile)}.gps-555716577707229985.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-555716577707229985.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-555716577707229985.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-555716577707229985.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-555716577707229985.gps.gpsil [style*="--pos-mobile:"]{position:var(--pos-mobile)}.gps-555716577707229985.gps.gpsil [style*="--right-mobile:"]{right:var(--right-mobile)}.gps-555716577707229985.gps.gpsil [style*="--ta-mobile:"]{text-align:var(--ta-mobile)}.gps-555716577707229985.gps.gpsil [style*="--top-mobile:"]{top:var(--top-mobile)}.gps-555716577707229985.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-555716577707229985.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-555716577707229985 .-gp-translate-x-1\/2,.gps-555716577707229985 .-gp-translate-y-1\/2,.gps-555716577707229985 .gp-rotate-0,.gps-555716577707229985 .gp-rotate-180,.gps-555716577707229985 .mobile\:gp-rotate-0,.gps-555716577707229985 .mobile\:gp-rotate-180,.gps-555716577707229985 .tablet\:gp-rotate-0,.gps-555716577707229985 .tablet\:gp-rotate-180{--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1}.gps-555716577707229985 .gp-shadow-md{--tw-ring-offset-shadow:0 0 #0000;--tw-ring-shadow:0 0 #0000;--tw-shadow:0 0 #0000;--tw-shadow-colored:0 0 #0000}.gps-555716577707229985 .gp-g-paragraph-1{font-family:var(--g-p1-ff);font-size:var(--g-p1-size);font-style:var(--g-p1-fs);font-weight:var(--g-p1-weight);letter-spacing:var(--g-p1-ls);line-height:var(--g-p1-lh)}.gps-555716577707229985 .gp-invisible{visibility:hidden}.gps-555716577707229985 .gp-static{position:static}.gps-555716577707229985 .gp-absolute{position:absolute}.gps-555716577707229985 .gp-relative{position:relative}.gps-555716577707229985 .gp-left-0{left:0}.gps-555716577707229985 .gp-left-1\/2{left:50%}.gps-555716577707229985 .gp-right-0{right:0}.gps-555716577707229985 .gp-top-0{top:0}.gps-555716577707229985 .gp-top-1\/2{top:50%}.gps-555716577707229985 .gp-z-0{z-index:0}.gps-555716577707229985 .gp-z-1{z-index:1}.gps-555716577707229985 .gp-z-\[90\]{z-index:90}.gps-555716577707229985 .\!gp-m-0{margin:0!important}.gps-555716577707229985 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-555716577707229985 .gp-my-0{margin-bottom:0;margin-top:0}.gps-555716577707229985 .gp-mb-0{margin-bottom:0}.gps-555716577707229985 .gp-block{display:block}.gps-555716577707229985 .gp-flex{display:flex}.gps-555716577707229985 .gp-inline-flex{display:inline-flex}.gps-555716577707229985 .gp-grid{display:grid}.gps-555716577707229985 .\!gp-hidden{display:none!important}.gps-555716577707229985 .gp-hidden{display:none}.gps-555716577707229985 .gp-aspect-\[56\/32\]{aspect-ratio:56/32}.gps-555716577707229985 .gp-aspect-square{aspect-ratio:1/1}.gps-555716577707229985 .gp-h-0{height:0}.gps-555716577707229985 .gp-h-5{height:20px}.gps-555716577707229985 .gp-h-full{height:100%}.gps-555716577707229985 .\!gp-min-h-full{min-height:100%!important}.gps-555716577707229985 .\!gp-w-full{width:100%!important}.gps-555716577707229985 .gp-w-14{width:56px}.gps-555716577707229985 .gp-w-5{width:20px}.gps-555716577707229985 .gp-w-\[12px\]{width:12px}.gps-555716577707229985 .gp-w-full{width:100%}.gps-555716577707229985 .\!gp-min-w-full{min-width:100%!important}.gps-555716577707229985 .\!gp-max-w-full{max-width:100%!important}.gps-555716577707229985 .gp-max-w-full{max-width:100%}.gps-555716577707229985 .gp-flex-1{flex:1 1 0%}.gps-555716577707229985 .gp-shrink-0{flex-shrink:0}.gps-555716577707229985 .-gp-translate-x-1\/2{--tw-translate-x:-50%}.gps-555716577707229985 .-gp-translate-x-1\/2,.gps-555716577707229985 .-gp-translate-y-1\/2{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-555716577707229985 .-gp-translate-y-1\/2{--tw-translate-y:-50%}.gps-555716577707229985 .gp-rotate-0{--tw-rotate:0deg}.gps-555716577707229985 .gp-rotate-0,.gps-555716577707229985 .gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-555716577707229985 .gp-rotate-180{--tw-rotate:180deg}.gps-555716577707229985 .gp-cursor-default{cursor:default}.gps-555716577707229985 .gp-cursor-pointer{cursor:pointer}.gps-555716577707229985 .gp-select-none{-webkit-user-select:none;-moz-user-select:none;user-select:none}.gps-555716577707229985 .\!gp-flex-row{flex-direction:row!important}.gps-555716577707229985 .gp-flex-row{flex-direction:row}.gps-555716577707229985 .gp-flex-col{flex-direction:column}.gps-555716577707229985 .\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-555716577707229985 .gp-items-start{align-items:flex-start}.gps-555716577707229985 .gp-items-center{align-items:center}.gps-555716577707229985 .\!gp-justify-center{justify-content:center!important}.gps-555716577707229985 .gp-justify-center{justify-content:center}.gps-555716577707229985 .gp-justify-between{justify-content:space-between}.gps-555716577707229985 .gp-gap-2{gap:8px}.gps-555716577707229985 .gp-gap-y-0{row-gap:0}.gps-555716577707229985 .gp-overflow-hidden{overflow:hidden}.gps-555716577707229985 .gp-break-words{overflow-wrap:break-word}.gps-555716577707229985 .\!gp-rounded-none{border-radius:0!important}.gps-555716577707229985 .gp-rounded{border-radius:4px}.gps-555716577707229985 .gp-rounded-full{border-radius:9999px}.gps-555716577707229985 .gp-rounded-none{border-radius:0}.gps-555716577707229985 .gp-bg-black\/80{background-color:rgba(0,0,0,.8)}.gps-555716577707229985 .gp-object-cover{-o-object-fit:cover;object-fit:cover}.gps-555716577707229985 .gp-px-0{padding-left:0;padding-right:0}.gps-555716577707229985 .\!gp-pb-0{padding-bottom:0!important}.gps-555716577707229985 .gp-text-center{text-align:center}.gps-555716577707229985 .gp-leading-\[0\]{line-height:0}.gps-555716577707229985 .gp-text-g-text-2{color:var(--g-c-text-2)}.gps-555716577707229985 .gp-text-g-text-3{color:var(--g-c-text-3)}.gps-555716577707229985 .gp-text-gray-500{--tw-text-opacity:1;color:rgb(107 114 128/var(--tw-text-opacity))}.gps-555716577707229985 .gp-text-white{--tw-text-opacity:1;color:rgb(255 255 255/var(--tw-text-opacity))}.gps-555716577707229985 .gp-no-underline{text-decoration-line:none}.gps-555716577707229985 .gp-decoration-g-text-1{text-decoration-color:var(--g-c-text-1)}.gps-555716577707229985 .gp-opacity-25{opacity:.25}.gps-555716577707229985 .gp-opacity-30{opacity:.3}.gps-555716577707229985 .gp-opacity-75{opacity:.75}.gps-555716577707229985 .gp-shadow-md{--tw-shadow:0 4px 6px -1px rgba(0,0,0,.1),0 2px 4px -2px rgba(0,0,0,.1);--tw-shadow-colored:0 4px 6px -1px var(--tw-shadow-color),0 2px 4px -2px var(--tw-shadow-color);box-shadow:var(--tw-ring-offset-shadow,0 0 #0000),var(--tw-ring-shadow,0 0 #0000),var(--tw-shadow)}.gps-555716577707229985 .gp-outline-1{outline-width:1px}.gps-555716577707229985 .-gp-outline-offset-1{outline-offset:-1px}.gps-555716577707229985 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555716577707229985 .gp-transition-opacity{transition-duration:.15s;transition-property:opacity;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555716577707229985 .gp-duration-200{transition-duration:.2s}.gps-555716577707229985 .gp-duration-300{transition-duration:.3s}.gps-555716577707229985 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-555716577707229985 .disabled\:gp-btn-disabled:disabled{cursor:default}@media (hover:hover) and (pointer:fine){.gps-555716577707229985 .hover\:gp-bg-\[\#ef0800\]:hover{--tw-bg-opacity:1;background-color:rgb(239 8 0/var(--tw-bg-opacity))}}.gps-555716577707229985 .disabled\:gp-pointer-events-none:disabled{pointer-events:none}.gps-555716577707229985 .disabled\:gp-opacity-30:disabled{opacity:.3}@media (hover:hover) and (pointer:fine){.gps-555716577707229985 .gp-group\/button:hover .group-hover\/button\:\!gp-text-inherit{color:inherit!important}}.gps-555716577707229985 .gp-group\/button:active .group-active\/button\:\!gp-text-inherit{color:inherit!important}.gps-555716577707229985 .data-\[hidden\=true\]\:gp-hidden[data-hidden=true]{display:none}.gps-555716577707229985 .data-\[outline\=active\]\:gp-outline[data-outline=active]{outline-style:solid}.gps-555716577707229985 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-visible{visibility:visible}.gps-555716577707229985 .gp-group[data-state=loading] .group-data-\[state\=loading\]\:gp-invisible,.gps-555716577707229985 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-invisible{visibility:hidden}@keyframes gp-spin{to{transform:rotate(1turn)}}.gps-555716577707229985 .gp-group\/button[data-state=loading] .group-data-\[state\=loading\]\/button\:gp-animate-spin{animation:gp-spin 1s linear infinite}@media (max-width:1024px){.gps-555716577707229985 .tablet\:gp-static{position:static}.gps-555716577707229985 .tablet\:gp-relative{position:relative}.gps-555716577707229985 .tablet\:gp-left-0{left:0}.gps-555716577707229985 .tablet\:gp-right-0{right:0}.gps-555716577707229985 .tablet\:gp-block{display:block}.gps-555716577707229985 .tablet\:\!gp-hidden{display:none!important}.gps-555716577707229985 .tablet\:gp-hidden{display:none}.gps-555716577707229985 .tablet\:\!gp-min-h-full{min-height:100%!important}.gps-555716577707229985 .tablet\:gp-rotate-0{--tw-rotate:0deg}.gps-555716577707229985 .tablet\:gp-rotate-0,.gps-555716577707229985 .tablet\:gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-555716577707229985 .tablet\:gp-rotate-180{--tw-rotate:180deg}.gps-555716577707229985 .tablet\:\!gp-flex-row{flex-direction:row!important}.gps-555716577707229985 .tablet\:gp-flex-row{flex-direction:row}.gps-555716577707229985 .tablet\:\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-555716577707229985 .tablet\:\!gp-content-stretch{align-content:stretch!important}.gps-555716577707229985 .tablet\:\!gp-justify-center{justify-content:center!important}.gps-555716577707229985 .tablet\:gp-px-0{padding-left:0;padding-right:0}}@media (max-width:767px){.gps-555716577707229985 .mobile\:gp-static{position:static}.gps-555716577707229985 .mobile\:gp-relative{position:relative}.gps-555716577707229985 .mobile\:gp-left-0{left:0}.gps-555716577707229985 .mobile\:gp-right-0{right:0}.gps-555716577707229985 .mobile\:gp-block{display:block}.gps-555716577707229985 .mobile\:\!gp-hidden{display:none!important}.gps-555716577707229985 .mobile\:gp-hidden{display:none}.gps-555716577707229985 .mobile\:\!gp-min-h-full{min-height:100%!important}.gps-555716577707229985 .mobile\:gp-rotate-0{--tw-rotate:0deg}.gps-555716577707229985 .mobile\:gp-rotate-0,.gps-555716577707229985 .mobile\:gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-555716577707229985 .mobile\:gp-rotate-180{--tw-rotate:180deg}.gps-555716577707229985 .mobile\:\!gp-flex-row{flex-direction:row!important}.gps-555716577707229985 .mobile\:gp-flex-row{flex-direction:row}.gps-555716577707229985 .mobile\:\!gp-flex-nowrap{flex-wrap:nowrap!important}.gps-555716577707229985 .mobile\:\!gp-content-stretch{align-content:stretch!important}.gps-555716577707229985 .mobile\:\!gp-justify-start{justify-content:flex-start!important}.gps-555716577707229985 .mobile\:\!gp-justify-end{justify-content:flex-end!important}.gps-555716577707229985 .mobile\:\!gp-justify-center{justify-content:center!important}.gps-555716577707229985 .mobile\:gp-px-0{padding-left:0;padding-right:0}}@media (max-width:1024px){.gps-555716577707229985 .tablet\:\[\&\>\*\]\:\!gp-justify-center>*{justify-content:center!important}}@media (max-width:767px){.gps-555716577707229985 .mobile\:\[\&\>\*\]\:\!gp-justify-start>*{justify-content:flex-start!important}.gps-555716577707229985 .mobile\:\[\&\>\*\]\:\!gp-justify-end>*{justify-content:flex-end!important}.gps-555716577707229985 .mobile\:\[\&\>\*\]\:\!gp-justify-center>*{justify-content:center!important}}.gps-555716577707229985 .\[\&\>svg\]\:\!gp-h-\[var\(--height-desktop\)\]>svg{height:var(--height-desktop)!important}.gps-555716577707229985 .\[\&\>svg\]\:gp-h-full>svg{height:100%}.gps-555716577707229985 .\[\&\>svg\]\:\!gp-w-auto>svg{width:auto!important}.gps-555716577707229985 .\[\&\>svg\]\:gp-w-full>svg{width:100%}@media (max-width:1024px){.gps-555716577707229985 .tablet\:\[\&\>svg\]\:\!gp-h-\[var\(--height-tablet\)\]>svg{height:var(--height-tablet)!important}}@media (max-width:767px){.gps-555716577707229985 .mobile\:\[\&\>svg\]\:\!gp-h-\[var\(--height-mobile\)\]>svg{height:var(--height-mobile)!important}}.gps-555716577707229985 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-555716577707229985 .\[\&_p\]\:gp-inline p{display:inline}.gps-555716577707229985 .\[\&_p\]\:gp-whitespace-pre-line p{white-space:pre-line}.gps-555716577707229985 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-555716577707229985 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      enableLazyloadImage="true" id="g8-4U2otcA" data-id="g8-4U2otcA"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:none;--d-tablet:grid;--op:100%;--pt:66px;--pl:60px;--pb:48px;--pr:60px;--pt-mobile:32px;--pl-mobile:15px;--pb-mobile:32px;--pr-mobile:15px;--pt-tablet:66px;--pl-tablet:15px;--pb-tablet:var(--g-s-2xl);--pr-tablet:15px;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g8-4U2otcA gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gDru0DMkus gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="gKK85ayoz4" data-id="gKK85ayoz4"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:16px;--cg:30px;--pc:start;--gtc:minmax(0, 3fr) minmax(0, 9fr);--gtc-tablet:minmax(0, 12fr);--gtc-mobile:minmax(0, 12fr);--w:100%;--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gKK85ayoz4 gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:space-between"
      class="gJBwgaAzHf gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="gEiIEkTEo-" data-id="gEiIEkTEo-"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:32px;--mb-mobile:16px;--pt-mobile:0px;--pt-tablet:0px;--cg:8px;--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gEiIEkTEo- gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gvXcyiDYG4 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="g1x9QhWJiB">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="g1x9QhWJiB "
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb-mobile:14px;--mb-tablet:39px"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:left;--ta-tablet:center;--fs:normal;--ff:var(--g-font-Roboto, 'Roboto'), var(--g-font-heading, heading);--ls:normal;--size:37px;--size-tablet:40px;--size-mobile:26px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--weight:bold;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.gg1x9QhWJiB_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
    </div><div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:space-between"
      class="gXacq_T49v gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="gxmMguaJ7L" data-id="gxmMguaJ7L"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:16px;--mb-mobile:0px;--pl-mobile:0px;--pr-mobile:0px;--cg:8px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gxmMguaJ7L gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gZnO6QO3Hp gp-relative gp-flex gp-flex-col"
    >
      {% render 'gp-section-555716577707229985-2', product: product, variant: variant, product_form_id: product_form_id, productSelectedVariant: productSelectedVariant, form: form %}{% render 'gp-section-555716577707229985-3', product: product, variant: variant, product_form_id: product_form_id, productSelectedVariant: productSelectedVariant, form: form %}{% render 'gp-section-555716577707229985-4', product: product, variant: variant, product_form_id: product_form_id, productSelectedVariant: productSelectedVariant, form: form %}
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="gemLYhvHFw" data-id="gemLYhvHFw"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:0px;--mb-mobile:14px;--cg:30px;--cg-mobile:4px;--pc:start;--gtc:minmax(0, 2.4fr) minmax(0, 2.4fr) minmax(0, 2.4fr) minmax(0, 2.4fr) minmax(0, 2.4fr);--gtc-mobile:minmax(0, 12fr);--w:100%;--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gemLYhvHFw gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:center;--jc-mobile:center;--o:1"
      class="gaRP2LgWR2 gp-relative gp-flex gp-flex-col"
    >
      
    <div class="gp-flex gp-w-full !gp-justify-center tablet:!gp-justify-center mobile:!gp-justify-center" style="--h:100%;--h-tablet:100%;--h-mobile:100%;--d:flex;--d-mobile:flex;--d-tablet:flex">
      <gp-hero-banner
        gp-data='{"target":"_blank","background":{"desktop":{"attachment":"scroll","color":"#FFFFFF","lazyLoad":false,"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"},"tablet":{"attachment":"scroll","color":"#FFFFFF","lazyLoad":false,"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"},"mobile":{"attachment":"scroll","color":"#FFFFFF","lazyLoad":false,"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"gg5JFOKoKl","enableParallax":false,"speedParallax":0.6,"hoverEffect":false,"hoverEffectScale":"110%","layout":{"desktop":{"cols":[12],"display":"fill"}},"contentPosition1Col":{"desktop":"center"},"contentPosition2Col":{"desktop":"center"}}'
        gp-href=""
        
        class="gg5JFOKoKl gp-group/hero gp-flex gp-flex-col gp-transition-colors gp-duration-200 gp-ease-in-out gp-w-full"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:flex;--d-mobile:flex;--d-tablet:flex;--op:100%;--h:100%;--h-tablet:100%;--h-mobile:100%;--w:100%;--w-tablet:100%;--w-mobile:100%"
        data-id="gg5JFOKoKl"
      >
        
      <div
        class="gp-relative gp-flex gp-flex-col gp-items-center gp-overflow-hidden gp_haslazybg !gp-justify-center tablet:!gp-justify-center mobile:!gp-justify-center"
        style="--h:100%;--h-tablet:100%;--h-mobile:100%;--bs:none;--bw:0px 0px 1px 0px;--bc:#E0E0E0;--bblr:10px;--bbrr:10px;--btlr:0px;--btrr:0px;--bgc:#FFFFFF;--shadow:none"
      >
        
  <picture style="border-radius: inherit" class="gp-w-full">
  
      <source media="(max-width: 767px)" data-srcSet="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkyMCIgaGVpZ2h0PSI5MDAiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTE5MjAtOTAwIj4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIxOTIwIiBoZWlnaHQ9IjkwMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTkyMCIgaGVpZ2h0PSI5MDAiIGZpbGw9InVybCgjZy0xOTIwLTkwMCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTE5MjAiIHRvPSIxOTIwIiBkdXI9IjFzIiByZXBlYXRDb3VudD0iaW5kZWZpbml0ZSIgIC8+CiAgPC9zdmc+" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMCIgaGVpZ2h0PSIwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0wLTAiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjAiIGhlaWdodD0iMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMCIgaGVpZ2h0PSIwIiBmaWxsPSJ1cmwoI2ctMC0wKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMCIgdG89IjAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <source media="(max-width: 1024px)" data-srcSet="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkyMCIgaGVpZ2h0PSI5MDAiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTE5MjAtOTAwIj4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIxOTIwIiBoZWlnaHQ9IjkwMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTkyMCIgaGVpZ2h0PSI5MDAiIGZpbGw9InVybCgjZy0xOTIwLTkwMCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTE5MjAiIHRvPSIxOTIwIiBkdXI9IjFzIiByZXBlYXRDb3VudD0iaW5kZWZpbml0ZSIgIC8+CiAgPC9zdmc+" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMCIgaGVpZ2h0PSIwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0wLTAiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjAiIGhlaWdodD0iMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMCIgaGVpZ2h0PSIwIiBmaWxsPSJ1cmwoI2ctMC0wKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMCIgdG89IjAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <img
        title
        class="adaptive-hero-banner gp_lazyload"
        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMCIgaGVpZ2h0PSIwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0wLTAiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjAiIGhlaWdodD0iMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMCIgaGVpZ2h0PSIwIiBmaWxsPSJ1cmwoI2ctMC0wKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMCIgdG89IjAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4="
        data-src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkyMCIgaGVpZ2h0PSI5MDAiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTE5MjAtOTAwIj4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIxOTIwIiBoZWlnaHQ9IjkwMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTkyMCIgaGVpZ2h0PSI5MDAiIGZpbGw9InVybCgjZy0xOTIwLTkwMCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTE5MjAiIHRvPSIxOTIwIiBkdXI9IjFzIiByZXBlYXRDb3VudD0iaW5kZWZpbml0ZSIgIC8+CiAgPC9zdmc+"
        width="100%"
        alt=""
        style="--d:none;--d-tablet:none;--d-mobile:none;--h:100%;--h-tablet:100%;--h-mobile:100%;--w:100%;--w-tablet:100%;--w-mobile:100%;--op:0;--z:-1"
      />
    
  </picture>
        <div class="gp-absolute gp-w-full gp-h-full">
          <div
            class="gp-overflow-hidden gp-relative gp-w-full gp-h-full"
            >
            <div
              aria-label="Background Image"
              role="banner"
              class='gp-hero-banner-bg gp-absolute gp-overflow-hidden gp-w-full gp-h-full top-0 left-0'
              style="clip-path:inset(0 0 0 round 0px 0px 9px 9px)"
            >
            <div class="hero-banner-bg-parallax gp-hero-banner-image-background gp_lazybg"
              style="--bgc:#FFFFFF;--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bgi:url('');--bgi-tablet:url('');--bgi-mobile:url('');--duration:0.5s;--scale:110%;--pos:absolute;--pos-tablet:absolute;--pos-mobile:absolute;--h:100%;--h-tablet:100%;--h-mobile:100%;--w:100%;--w-tablet:100%;--w-mobile:100%;--top:;--top-tablet:;--top-mobile:"
            >
            
            
            </div></div>
          </div>
          
          </div>
          
       
      
    <div
      id data-id
        style="--pl:5px;--pr:5px;--pt:24px;--pb:24px;--pl-tablet:5px;--pr-tablet:5px;--pt-tablet:24px;--pb-tablet:24px;--pl-mobile:5px;--pr-mobile:5px;--pt-mobile:14px;--pb-mobile:14px;--cg:32px;--pc:center;--pc-tablet:center;--pc-mobile:center;--gtc:minmax(0, 12fr);--w:1200px;--w-tablet:var(--g-ct-w, 1200px);--w-mobile:var(--g-ct-w, 1200px)"
        class="gp-hero-banner-row gp-z-1 gp-w-full tablet:gp-relative mobile:gp-relative tablet:[&>*>*]:!gp-justify-undefined mobile:[&>*>*]:!gp-justify-undefined tablet:!gp-content-stretch tablet:[&>*]:!gp-justify-center mobile:!gp-content-stretch mobile:[&>*]:!gp-justify-center gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gn-jUdoaoH gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gJVJE9YZn7">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gJVJE9YZn7 "
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--ta-mobile:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggJVJE9YZn7_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
      <style id="custom-css-gJVJE9YZn7">
        .gJVJE9YZn7 {

}
.gJVJE9YZn7 p {

}
      </style>
    
    </div>
    </div>
   
    
      </div>
    
        
      </gp-hero-banner>
    </div>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-hero-banner.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>

  
    </div><div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:center;--jc-mobile:center;--o:2"
      class="g-xsr0BNWq gp-relative gp-flex gp-flex-col"
    >
      
    <div class="gp-flex gp-w-full !gp-justify-center tablet:!gp-justify-center mobile:!gp-justify-center" style="--h:100%;--h-tablet:100%;--h-mobile:100%;--d:flex;--d-mobile:flex;--d-tablet:flex">
      <gp-hero-banner
        gp-data='{"target":"_blank","background":{"desktop":{"attachment":"scroll","color":"#FFFFFF","lazyLoad":false,"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"},"tablet":{"attachment":"scroll","color":"#FFFFFF","lazyLoad":false,"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"},"mobile":{"attachment":"scroll","color":"#FFFFFF","lazyLoad":false,"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"gJaTUh02FI","enableParallax":false,"speedParallax":0.6,"hoverEffect":false,"hoverEffectScale":"110%","layout":{"desktop":{"cols":[12],"display":"fill"}},"contentPosition1Col":{"desktop":"center","mobile":"bottom"},"contentPosition2Col":{"desktop":"center"}}'
        gp-href=""
        
        class="gJaTUh02FI gp-group/hero gp-flex gp-flex-col gp-transition-colors gp-duration-200 gp-ease-in-out gp-w-full"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:flex;--d-mobile:flex;--d-tablet:flex;--op:100%;--h:100%;--h-tablet:100%;--h-mobile:100%;--w:100%;--w-tablet:100%;--w-mobile:100%"
        data-id="gJaTUh02FI"
      >
        
      <div
        class="gp-relative gp-flex gp-flex-col gp-items-center gp-overflow-hidden gp_haslazybg !gp-justify-center tablet:!gp-justify-center mobile:!gp-justify-end"
        style="--h:100%;--h-tablet:100%;--h-mobile:100%;--bs:none;--bw:0px 0px 1px 0px;--bc:#E0E0E0;--bblr:10px;--bbrr:10px;--btlr:0px;--btrr:0px;--bgc:#FFFFFF;--shadow:none"
      >
        
  <picture style="border-radius: inherit" class="gp-w-full">
  
      <source media="(max-width: 767px)" data-srcSet="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkyMCIgaGVpZ2h0PSI5MDAiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTE5MjAtOTAwIj4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIxOTIwIiBoZWlnaHQ9IjkwMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTkyMCIgaGVpZ2h0PSI5MDAiIGZpbGw9InVybCgjZy0xOTIwLTkwMCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTE5MjAiIHRvPSIxOTIwIiBkdXI9IjFzIiByZXBlYXRDb3VudD0iaW5kZWZpbml0ZSIgIC8+CiAgPC9zdmc+" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMCIgaGVpZ2h0PSIwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0wLTAiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjAiIGhlaWdodD0iMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMCIgaGVpZ2h0PSIwIiBmaWxsPSJ1cmwoI2ctMC0wKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMCIgdG89IjAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <source media="(max-width: 1024px)" data-srcSet="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkyMCIgaGVpZ2h0PSI5MDAiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTE5MjAtOTAwIj4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIxOTIwIiBoZWlnaHQ9IjkwMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTkyMCIgaGVpZ2h0PSI5MDAiIGZpbGw9InVybCgjZy0xOTIwLTkwMCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTE5MjAiIHRvPSIxOTIwIiBkdXI9IjFzIiByZXBlYXRDb3VudD0iaW5kZWZpbml0ZSIgIC8+CiAgPC9zdmc+" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMCIgaGVpZ2h0PSIwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0wLTAiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjAiIGhlaWdodD0iMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMCIgaGVpZ2h0PSIwIiBmaWxsPSJ1cmwoI2ctMC0wKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMCIgdG89IjAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <img
        title
        class="adaptive-hero-banner gp_lazyload"
        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMCIgaGVpZ2h0PSIwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0wLTAiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjAiIGhlaWdodD0iMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMCIgaGVpZ2h0PSIwIiBmaWxsPSJ1cmwoI2ctMC0wKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMCIgdG89IjAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4="
        data-src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkyMCIgaGVpZ2h0PSI5MDAiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTE5MjAtOTAwIj4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIxOTIwIiBoZWlnaHQ9IjkwMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTkyMCIgaGVpZ2h0PSI5MDAiIGZpbGw9InVybCgjZy0xOTIwLTkwMCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTE5MjAiIHRvPSIxOTIwIiBkdXI9IjFzIiByZXBlYXRDb3VudD0iaW5kZWZpbml0ZSIgIC8+CiAgPC9zdmc+"
        width="100%"
        alt=""
        style="--d:none;--d-tablet:none;--d-mobile:none;--h:100%;--h-tablet:100%;--h-mobile:100%;--w:100%;--w-tablet:100%;--w-mobile:100%;--op:0;--z:-1"
      />
    
  </picture>
        <div class="gp-absolute gp-w-full gp-h-full">
          <div
            class="gp-overflow-hidden gp-relative gp-w-full gp-h-full"
            >
            <div
              aria-label="Background Image"
              role="banner"
              class='gp-hero-banner-bg gp-absolute gp-overflow-hidden gp-w-full gp-h-full top-0 left-0'
              style="clip-path:inset(0 0 0 round 0px 0px 9px 9px)"
            >
            <div class="hero-banner-bg-parallax gp-hero-banner-image-background gp_lazybg"
              style="--bgc:#FFFFFF;--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bgi:url('');--bgi-tablet:url('');--bgi-mobile:url('');--duration:0.5s;--scale:110%;--pos:absolute;--pos-tablet:absolute;--pos-mobile:absolute;--h:100%;--h-tablet:100%;--h-mobile:100%;--w:100%;--w-tablet:100%;--w-mobile:100%;--top:;--top-tablet:;--top-mobile:"
            >
            
            
            </div></div>
          </div>
          
          </div>
          
       
      
    <div
      id data-id
        style="--pl:5px;--pr:5px;--pt:24px;--pb:24px;--pl-tablet:5px;--pr-tablet:5px;--pt-tablet:24px;--pb-tablet:24px;--pl-mobile:5px;--pr-mobile:5px;--pt-mobile:14px;--pb-mobile:14px;--cg:32px;--pc:center;--pc-tablet:center;--pc-mobile:end;--gtc:minmax(0, 12fr);--w:1200px;--w-tablet:var(--g-ct-w, 1200px);--w-mobile:var(--g-ct-w, 1200px)"
        class="gp-hero-banner-row gp-z-1 gp-w-full tablet:gp-relative mobile:gp-relative tablet:[&>*>*]:!gp-justify-undefined mobile:[&>*>*]:!gp-justify-undefined tablet:!gp-content-stretch tablet:[&>*]:!gp-justify-center mobile:!gp-content-stretch mobile:[&>*]:!gp-justify-end gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gWHc9sJ6FG gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gpFpQOjSJb">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gpFpQOjSJb "
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--ta-mobile:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggpFpQOjSJb_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
      <style id="custom-css-gpFpQOjSJb">
        .gpFpQOjSJb {

}
.gpFpQOjSJb p {

}
      </style>
    
    </div>
    </div>
   
    
      </div>
    
        
      </gp-hero-banner>
    </div>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-hero-banner.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>

  
    </div><div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:center;--jc-mobile:center;--o:3"
      class="gEMoMgV7kS gp-relative gp-flex gp-flex-col"
    >
      
    <div class="gp-flex gp-w-full !gp-justify-center tablet:!gp-justify-center mobile:!gp-justify-center" style="--h:100%;--h-tablet:100%;--h-mobile:100%;--d:flex;--d-mobile:flex;--d-tablet:flex">
      <gp-hero-banner
        gp-data='{"target":"_blank","background":{"desktop":{"attachment":"scroll","color":"#FFFFFF","lazyLoad":false,"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"},"tablet":{"attachment":"scroll","color":"#FFFFFF","lazyLoad":false,"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"},"mobile":{"attachment":"scroll","color":"#FFFFFF","lazyLoad":false,"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"gDupijCQfV","enableParallax":false,"speedParallax":0.6,"hoverEffect":false,"hoverEffectScale":"110%","layout":{"desktop":{"cols":[12],"display":"fill"}},"contentPosition1Col":{"desktop":"center","mobile":"bottom"},"contentPosition2Col":{"desktop":"center"}}'
        gp-href=""
        
        class="gDupijCQfV gp-group/hero gp-flex gp-flex-col gp-transition-colors gp-duration-200 gp-ease-in-out gp-w-full"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:flex;--d-mobile:flex;--d-tablet:flex;--op:100%;--h:100%;--h-tablet:100%;--h-mobile:100%;--w:100%;--w-tablet:100%;--w-mobile:100%"
        data-id="gDupijCQfV"
      >
        
      <div
        class="gp-relative gp-flex gp-flex-col gp-items-center gp-overflow-hidden gp_haslazybg !gp-justify-center tablet:!gp-justify-center mobile:!gp-justify-end"
        style="--h:100%;--h-tablet:100%;--h-mobile:100%;--bs:none;--bw:0px 0px 1px 0px;--bc:#E0E0E0;--bblr:10px;--bbrr:10px;--btlr:0px;--btrr:0px;--bgc:#FFFFFF;--shadow:none"
      >
        
  <picture style="border-radius: inherit" class="gp-w-full">
  
      <source media="(max-width: 767px)" data-srcSet="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkyMCIgaGVpZ2h0PSI5MDAiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTE5MjAtOTAwIj4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIxOTIwIiBoZWlnaHQ9IjkwMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTkyMCIgaGVpZ2h0PSI5MDAiIGZpbGw9InVybCgjZy0xOTIwLTkwMCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTE5MjAiIHRvPSIxOTIwIiBkdXI9IjFzIiByZXBlYXRDb3VudD0iaW5kZWZpbml0ZSIgIC8+CiAgPC9zdmc+" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMCIgaGVpZ2h0PSIwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0wLTAiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjAiIGhlaWdodD0iMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMCIgaGVpZ2h0PSIwIiBmaWxsPSJ1cmwoI2ctMC0wKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMCIgdG89IjAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <source media="(max-width: 1024px)" data-srcSet="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkyMCIgaGVpZ2h0PSI5MDAiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTE5MjAtOTAwIj4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIxOTIwIiBoZWlnaHQ9IjkwMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTkyMCIgaGVpZ2h0PSI5MDAiIGZpbGw9InVybCgjZy0xOTIwLTkwMCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTE5MjAiIHRvPSIxOTIwIiBkdXI9IjFzIiByZXBlYXRDb3VudD0iaW5kZWZpbml0ZSIgIC8+CiAgPC9zdmc+" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMCIgaGVpZ2h0PSIwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0wLTAiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjAiIGhlaWdodD0iMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMCIgaGVpZ2h0PSIwIiBmaWxsPSJ1cmwoI2ctMC0wKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMCIgdG89IjAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <img
        title
        class="adaptive-hero-banner gp_lazyload"
        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMCIgaGVpZ2h0PSIwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0wLTAiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjAiIGhlaWdodD0iMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMCIgaGVpZ2h0PSIwIiBmaWxsPSJ1cmwoI2ctMC0wKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMCIgdG89IjAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4="
        data-src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkyMCIgaGVpZ2h0PSI5MDAiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTE5MjAtOTAwIj4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIxOTIwIiBoZWlnaHQ9IjkwMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTkyMCIgaGVpZ2h0PSI5MDAiIGZpbGw9InVybCgjZy0xOTIwLTkwMCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTE5MjAiIHRvPSIxOTIwIiBkdXI9IjFzIiByZXBlYXRDb3VudD0iaW5kZWZpbml0ZSIgIC8+CiAgPC9zdmc+"
        width="100%"
        alt=""
        style="--d:none;--d-tablet:none;--d-mobile:none;--h:100%;--h-tablet:100%;--h-mobile:100%;--w:100%;--w-tablet:100%;--w-mobile:100%;--op:0;--z:-1"
      />
    
  </picture>
        <div class="gp-absolute gp-w-full gp-h-full">
          <div
            class="gp-overflow-hidden gp-relative gp-w-full gp-h-full"
            >
            <div
              aria-label="Background Image"
              role="banner"
              class='gp-hero-banner-bg gp-absolute gp-overflow-hidden gp-w-full gp-h-full top-0 left-0'
              style="clip-path:inset(0 0 0 round 0px 0px 9px 9px)"
            >
            <div class="hero-banner-bg-parallax gp-hero-banner-image-background gp_lazybg"
              style="--bgc:#FFFFFF;--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bgi:url('');--bgi-tablet:url('');--bgi-mobile:url('');--duration:0.5s;--scale:110%;--pos:absolute;--pos-tablet:absolute;--pos-mobile:absolute;--h:100%;--h-tablet:100%;--h-mobile:100%;--w:100%;--w-tablet:100%;--w-mobile:100%;--top:;--top-tablet:;--top-mobile:"
            >
            
            
            </div></div>
          </div>
          
          </div>
          
       
      
    <div
      id data-id
        style="--pl:5px;--pr:5px;--pt:24px;--pb:24px;--pl-tablet:5px;--pr-tablet:5px;--pt-tablet:24px;--pb-tablet:24px;--pl-mobile:5px;--pr-mobile:5px;--pt-mobile:14px;--pb-mobile:14px;--cg:32px;--pc:center;--pc-tablet:center;--pc-mobile:end;--gtc:minmax(0, 12fr);--w:1200px;--w-tablet:var(--g-ct-w, 1200px);--w-mobile:var(--g-ct-w, 1200px)"
        class="gp-hero-banner-row gp-z-1 gp-w-full tablet:gp-relative mobile:gp-relative tablet:[&>*>*]:!gp-justify-undefined mobile:[&>*>*]:!gp-justify-undefined tablet:!gp-content-stretch tablet:[&>*]:!gp-justify-center mobile:!gp-content-stretch mobile:[&>*]:!gp-justify-end gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gvchLijyfj gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gut9-EzZcp">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gut9-EzZcp "
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--ta-mobile:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggut9-EzZcp_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
      <style id="custom-css-gut9-EzZcp">
        .gut9-EzZcp {

}
.gut9-EzZcp p {

}
      </style>
    
    </div>
    </div>
   
    
      </div>
    
        
      </gp-hero-banner>
    </div>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-hero-banner.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>

  
    </div><div
      enableLazyloadImage="true" tag="Col" label="Block" type="component"
      style="--jc:center;--jc-mobile:center;--o:4"
      class="gHsmz3df09 gp-relative gp-flex gp-flex-col"
    >
      
    <div class="gp-flex gp-w-full !gp-justify-center tablet:!gp-justify-center mobile:!gp-justify-center" style="--h:100%;--h-tablet:100%;--h-mobile:100%;--d:flex;--d-mobile:flex;--d-tablet:flex">
      <gp-hero-banner
        gp-data='{"target":"_blank","background":{"desktop":{"attachment":"scroll","color":"#FFFFFF","lazyLoad":false,"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"},"tablet":{"attachment":"scroll","color":"#FFFFFF","lazyLoad":false,"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"},"mobile":{"attachment":"scroll","color":"#FFFFFF","lazyLoad":false,"position":{"x":50,"y":50},"repeat":"no-repeat","size":"cover","type":"color"}},"uid":"gnqoHn1doK","enableParallax":false,"speedParallax":0.6,"hoverEffect":false,"hoverEffectScale":"110%","layout":{"desktop":{"cols":[12],"display":"fill"}},"contentPosition1Col":{"desktop":"center","mobile":"bottom"},"contentPosition2Col":{"desktop":"center"}}'
        gp-href=""
        
        class="gnqoHn1doK gp-group/hero gp-flex gp-flex-col gp-transition-colors gp-duration-200 gp-ease-in-out gp-w-full"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:flex;--d-mobile:flex;--d-tablet:flex;--op:100%;--h:100%;--h-tablet:100%;--h-mobile:100%;--w:100%;--w-tablet:100%;--w-mobile:100%"
        data-id="gnqoHn1doK"
      >
        
      <div
        class="gp-relative gp-flex gp-flex-col gp-items-center gp-overflow-hidden gp_haslazybg !gp-justify-center tablet:!gp-justify-center mobile:!gp-justify-end"
        style="--h:100%;--h-tablet:100%;--h-mobile:100%;--bs:none;--bw:0px 0px 1px 0px;--bc:#E0E0E0;--bblr:10px;--bbrr:10px;--btlr:0px;--btrr:0px;--bgc:#FFFFFF;--shadow:none"
      >
        
  <picture style="border-radius: inherit" class="gp-w-full">
  
      <source media="(max-width: 767px)" data-srcSet="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkyMCIgaGVpZ2h0PSI5MDAiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTE5MjAtOTAwIj4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIxOTIwIiBoZWlnaHQ9IjkwMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTkyMCIgaGVpZ2h0PSI5MDAiIGZpbGw9InVybCgjZy0xOTIwLTkwMCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTE5MjAiIHRvPSIxOTIwIiBkdXI9IjFzIiByZXBlYXRDb3VudD0iaW5kZWZpbml0ZSIgIC8+CiAgPC9zdmc+" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMCIgaGVpZ2h0PSIwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0wLTAiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjAiIGhlaWdodD0iMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMCIgaGVpZ2h0PSIwIiBmaWxsPSJ1cmwoI2ctMC0wKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMCIgdG89IjAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <source media="(max-width: 1024px)" data-srcSet="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkyMCIgaGVpZ2h0PSI5MDAiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTE5MjAtOTAwIj4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIxOTIwIiBoZWlnaHQ9IjkwMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTkyMCIgaGVpZ2h0PSI5MDAiIGZpbGw9InVybCgjZy0xOTIwLTkwMCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTE5MjAiIHRvPSIxOTIwIiBkdXI9IjFzIiByZXBlYXRDb3VudD0iaW5kZWZpbml0ZSIgIC8+CiAgPC9zdmc+" srcset="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMCIgaGVpZ2h0PSIwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0wLTAiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjAiIGhlaWdodD0iMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMCIgaGVpZ2h0PSIwIiBmaWxsPSJ1cmwoI2ctMC0wKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMCIgdG89IjAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4=" />
      <img
        title
        class="adaptive-hero-banner gp_lazyload"
        src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMCIgaGVpZ2h0PSIwIiB2ZXJzaW9uPSIxLjEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiPgogICAgPGRlZnM+CiAgICAgIDxsaW5lYXJHcmFkaWVudCBpZD0iZy0wLTAiPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iMjAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNTAlIiAvPgogICAgICAgIDxzdG9wIHN0b3AtY29sb3I9InJnYmEoNTEsIDUxLCA1MSwgMCkiIG9mZnNldD0iNzAlIiAvPgogICAgICA8L2xpbmVhckdyYWRpZW50PgogICAgPC9kZWZzPgogICAgPHJlY3Qgd2lkdGg9IjAiIGhlaWdodD0iMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMCIgaGVpZ2h0PSIwIiBmaWxsPSJ1cmwoI2ctMC0wKSIgLz4KICAgIDxhbmltYXRlIHhsaW5rOmhyZWY9IiNyIiBhdHRyaWJ1dGVOYW1lPSJ4IiBmcm9tPSItMCIgdG89IjAiIGR1cj0iMXMiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiAgLz4KICA8L3N2Zz4="
        data-src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTkyMCIgaGVpZ2h0PSI5MDAiIHZlcnNpb249IjEuMSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB4bWxuczp4bGluaz0iaHR0cDovL3d3dy53My5vcmcvMTk5OS94bGluayI+CiAgICA8ZGVmcz4KICAgICAgPGxpbmVhckdyYWRpZW50IGlkPSJnLTE5MjAtOTAwIj4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjIwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjUwJSIgLz4KICAgICAgICA8c3RvcCBzdG9wLWNvbG9yPSJyZ2JhKDUxLCA1MSwgNTEsIDApIiBvZmZzZXQ9IjcwJSIgLz4KICAgICAgPC9saW5lYXJHcmFkaWVudD4KICAgIDwvZGVmcz4KICAgIDxyZWN0IHdpZHRoPSIxOTIwIiBoZWlnaHQ9IjkwMCIgZmlsbD0icmdiYSg1MSwgNTEsIDUxLCAwKSIgLz4KICAgIDxyZWN0IGlkPSJyIiB3aWR0aD0iMTkyMCIgaGVpZ2h0PSI5MDAiIGZpbGw9InVybCgjZy0xOTIwLTkwMCkiIC8+CiAgICA8YW5pbWF0ZSB4bGluazpocmVmPSIjciIgYXR0cmlidXRlTmFtZT0ieCIgZnJvbT0iLTE5MjAiIHRvPSIxOTIwIiBkdXI9IjFzIiByZXBlYXRDb3VudD0iaW5kZWZpbml0ZSIgIC8+CiAgPC9zdmc+"
        width="100%"
        alt=""
        style="--d:none;--d-tablet:none;--d-mobile:none;--h:100%;--h-tablet:100%;--h-mobile:100%;--w:100%;--w-tablet:100%;--w-mobile:100%;--op:0;--z:-1"
      />
    
  </picture>
        <div class="gp-absolute gp-w-full gp-h-full">
          <div
            class="gp-overflow-hidden gp-relative gp-w-full gp-h-full"
            >
            <div
              aria-label="Background Image"
              role="banner"
              class='gp-hero-banner-bg gp-absolute gp-overflow-hidden gp-w-full gp-h-full top-0 left-0'
              style="clip-path:inset(0 0 0 round 0px 0px 9px 9px)"
            >
            <div class="hero-banner-bg-parallax gp-hero-banner-image-background gp_lazybg"
              style="--bgc:#FFFFFF;--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bgi:url('');--bgi-tablet:url('');--bgi-mobile:url('');--duration:0.5s;--scale:110%;--pos:absolute;--pos-tablet:absolute;--pos-mobile:absolute;--h:100%;--h-tablet:100%;--h-mobile:100%;--w:100%;--w-tablet:100%;--w-mobile:100%;--top:;--top-tablet:;--top-mobile:"
            >
            
            
            </div></div>
          </div>
          
          </div>
          
       
      
    <div
      id data-id
        style="--pl:5px;--pr:5px;--pt:24px;--pb:24px;--pl-tablet:5px;--pr-tablet:5px;--pt-tablet:24px;--pb-tablet:24px;--pl-mobile:5px;--pr-mobile:5px;--pt-mobile:14px;--pb-mobile:14px;--cg:32px;--pc:center;--pc-tablet:center;--pc-mobile:end;--gtc:minmax(0, 12fr);--w:1200px;--w-tablet:var(--g-ct-w, 1200px);--w-mobile:var(--g-ct-w, 1200px)"
        class="gp-hero-banner-row gp-z-1 gp-w-full tablet:gp-relative mobile:gp-relative tablet:[&>*>*]:!gp-justify-undefined mobile:[&>*>*]:!gp-justify-undefined tablet:!gp-content-stretch tablet:[&>*]:!gp-justify-center mobile:!gp-content-stretch mobile:[&>*]:!gp-justify-end gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:center"
      class="gfamVeCkcZ gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gBEntiMVjG">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gBEntiMVjG "
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:center;--ta-mobile:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-body, body);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggBEntiMVjG_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
      <style id="custom-css-gBEntiMVjG">
        .gBEntiMVjG {

}
.gBEntiMVjG p {

}
      </style>
    
    </div>
    </div>
   
    
      </div>
    
        
      </gp-hero-banner>
    </div>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-hero-banner.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>

  
    </div><div
      enableLazyloadImage="true" tag="Col" label="Block" type="component"
      style="--jc:center;--jc-mobile:center;--o:0"
      class="g3rY5avZ_M gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="gIVppg5Uhp" data-id="gIVppg5Uhp"
        style="--d:grid;--d-tablet:grid;--d-mobile:grid;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--cg:8px;--pc:start;--gtc:minmax(0, auto) minmax(0, auto);--gtc-mobile:minmax(0, auto) minmax(0, auto);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gIVppg5Uhp gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full"
    >
    <div
      enableLazyloadImage="true" tag="Col" label="Block" type="component"
      style="--jc:center"
      class="gf7PtCU_SZ gp-relative gp-flex gp-flex-col"
    >
      
  <div
      
      style="--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%;--ta:left"
      class="gp-leading-[0] g_Tgia490P"
    >
      <div 
      data-id="g_Tgia490P"
      class="icon-wrapper gp-inline-flex gp-overflow-hidden"
      style="--bs:none;--bw:1px 1px 1px 1px;--bc:#000000"
      >
      <div
      
    >
      <span
        style="--c:#1180ff;--t:rotate(0deg);--w:20px;--w-tablet:20px;--w-mobile:20px;--h:20px;--h-tablet:20px;--h-mobile:20px;--minw:20px;--minw-tablet:20px;--minw-mobile:20px;--height-desktop:20px;--height-tablet:20px;--height-mobile:20px"
        class="gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden [&>svg]:!gp-h-[var(--height-desktop)] [&>svg]:!gp-w-auto tablet:[&>svg]:!gp-h-[var(--height-tablet)] mobile:[&>svg]:!gp-h-[var(--height-mobile)]"
      ><svg height="20" width="20" xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 256 256" fill="currentColor" data-id="508817591470063976">
              <path fill="currentColor" strokeLinecap="round" strokeLinejoin="round" fill="currentColor" d="M244,128a12,12,0,0,1-12,12H207.42l-36.69,73.37A12,12,0,0,1,160,220h-.6a12,12,0,0,1-10.61-7.72L95,71.15,66.92,133A12,12,0,0,1,56,140H24a12,12,0,0,1,0-24H48.27L85.08,35a12,12,0,0,1,22.13.7l54.28,142.46,27.78-55.56A12,12,0,0,1,200,116h32A12,12,0,0,1,244,128Z" /></svg></span>
    </div>
      </div>
    </div>
    
    </div><div
      enableLazyloadImage="true" tag="Col" label="Block" type="component"
      style="--jc:center"
      class="gKk_JvGZ_2 gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gPEjx2KDbB">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gPEjx2KDbB "
        style="--ta:left;--d:block;--d-tablet:block;--d-mobile:block;--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--op:100%"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:600;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:180%;--lh-tablet:180%;--lh-mobile:180%;--c:var(--g-c-text-1, text-1);--tt:uppercase;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggPEjx2KDbB_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
    </div>
   
    
    </div>
    </div>
   
    
    </div>
    </div>
   
    
    </div>
    </div>
   
    
    </div>
    </div>
  
            </section>
           
    
<style {% if section.settings.section_preload == "false" %} class="gps-link" type="text/disabled-css" {% endif %}>@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: normal;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWubEbVmUiAw.woff) format('woff');
}
/* cyrillic-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWubEbVmZiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0460-052F, U+1C80-1C8A, U+20B4, U+2DE0-2DFF, U+A640-A69F, U+FE2E-FE2F;
}
/* cyrillic */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWubEbVmQiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0301, U+0400-045F, U+0490-0491, U+04B0-04B1, U+2116;
}
/* greek-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWubEbVmYiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+1F00-1FFF;
}
/* greek */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWubEbVmXiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0370-0377, U+037A-037F, U+0384-038A, U+038C, U+038E-03A1, U+03A3-03FF;
}
/* math */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWubEbVnoiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0302-0303, U+0305, U+0307-0308, U+0310, U+0312, U+0315, U+031A, U+0326-0327, U+032C, U+032F-0330, U+0332-0333, U+0338, U+033A, U+0346, U+034D, U+0391-03A1, U+03A3-03A9, U+03B1-03C9, U+03D1, U+03D5-03D6, U+03F0-03F1, U+03F4-03F5, U+2016-2017, U+2034-2038, U+203C, U+2040, U+2043, U+2047, U+2050, U+2057, U+205F, U+2070-2071, U+2074-208E, U+2090-209C, U+20D0-20DC, U+20E1, U+20E5-20EF, U+2100-2112, U+2114-2115, U+2117-2121, U+2123-214F, U+2190, U+2192, U+2194-21AE, U+21B0-21E5, U+21F1-21F2, U+21F4-2211, U+2213-2214, U+2216-22FF, U+2308-230B, U+2310, U+2319, U+231C-2321, U+2336-237A, U+237C, U+2395, U+239B-23B7, U+23D0, U+23DC-23E1, U+2474-2475, U+25AF, U+25B3, U+25B7, U+25BD, U+25C1, U+25CA, U+25CC, U+25FB, U+266D-266F, U+27C0-27FF, U+2900-2AFF, U+2B0E-2B11, U+2B30-2B4C, U+2BFE, U+3030, U+FF5B, U+FF5D, U+1D400-1D7FF, U+1EE00-1EEFF;
}
/* symbols */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWubEbVn6iAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0001-000C, U+000E-001F, U+007F-009F, U+20DD-20E0, U+20E2-20E4, U+2150-218F, U+2190, U+2192, U+2194-2199, U+21AF, U+21E6-21F0, U+21F3, U+2218-2219, U+2299, U+22C4-22C6, U+2300-243F, U+2440-244A, U+2460-24FF, U+25A0-27BF, U+2800-28FF, U+2921-2922, U+2981, U+29BF, U+29EB, U+2B00-2BFF, U+4DC0-4DFF, U+FFF9-FFFB, U+10140-1018E, U+10190-1019C, U+101A0, U+101D0-101FD, U+102E0-102FB, U+10E60-10E7E, U+1D2C0-1D2D3, U+1D2E0-1D37F, U+1F000-1F0FF, U+1F100-1F1AD, U+1F1E6-1F1FF, U+1F30D-1F30F, U+1F315, U+1F31C, U+1F31E, U+1F320-1F32C, U+1F336, U+1F378, U+1F37D, U+1F382, U+1F393-1F39F, U+1F3A7-1F3A8, U+1F3AC-1F3AF, U+1F3C2, U+1F3C4-1F3C6, U+1F3CA-1F3CE, U+1F3D4-1F3E0, U+1F3ED, U+1F3F1-1F3F3, U+1F3F5-1F3F7, U+1F408, U+1F415, U+1F41F, U+1F426, U+1F43F, U+1F441-1F442, U+1F444, U+1F446-1F449, U+1F44C-1F44E, U+1F453, U+1F46A, U+1F47D, U+1F4A3, U+1F4B0, U+1F4B3, U+1F4B9, U+1F4BB, U+1F4BF, U+1F4C8-1F4CB, U+1F4D6, U+1F4DA, U+1F4DF, U+1F4E3-1F4E6, U+1F4EA-1F4ED, U+1F4F7, U+1F4F9-1F4FB, U+1F4FD-1F4FE, U+1F503, U+1F507-1F50B, U+1F50D, U+1F512-1F513, U+1F53E-1F54A, U+1F54F-1F5FA, U+1F610, U+1F650-1F67F, U+1F687, U+1F68D, U+1F691, U+1F694, U+1F698, U+1F6AD, U+1F6B2, U+1F6B9-1F6BA, U+1F6BC, U+1F6C6-1F6CF, U+1F6D3-1F6D7, U+1F6E0-1F6EA, U+1F6F0-1F6F3, U+1F6F7-1F6FC, U+1F700-1F7FF, U+1F800-1F80B, U+1F810-1F847, U+1F850-1F859, U+1F860-1F887, U+1F890-1F8AD, U+1F8B0-1F8BB, U+1F8C0-1F8C1, U+1F900-1F90B, U+1F93B, U+1F946, U+1F984, U+1F996, U+1F9E9, U+1FA00-1FA6F, U+1FA70-1FA7C, U+1FA80-1FA89, U+1FA8F-1FAC6, U+1FACE-1FADC, U+1FADF-1FAE9, U+1FAF0-1FAF8, U+1FB00-1FBFF;
}
/* vietnamese */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWubEbVmbiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0102-0103, U+0110-0111, U+0128-0129, U+0168-0169, U+01A0-01A1, U+01AF-01B0, U+0300-0301, U+0303-0304, U+0308-0309, U+0323, U+0329, U+1EA0-1EF9, U+20AB;
}
/* latin-ext */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWubEbVmaiAz0klQmz24O0g.woff) format('woff');
  unicode-range: U+0100-02BA, U+02BD-02C5, U+02C7-02CC, U+02CE-02D7, U+02DD-02FF, U+0304, U+0308, U+0329, U+1D00-1DBF, U+1E00-1E9F, U+1EF2-1EFF, U+2020, U+20A0-20AB, U+20AD-20C0, U+2113, U+2C60-2C7F, U+A720-A7FF;
}
/* latin */
@font-face {
  font-family: 'Roboto';
  font-style: normal;
  font-weight: 400;
  font-stretch: 100%;
  font-display: swap;
  src: url(https://fonts.gstatic.com/s/roboto/v47/KFOMCnqEu92Fr1ME7kSn66aGLdTylUAMQXC89YmC2DPNWubEbVmUiAz0klQmz24.woff) format('woff');
  unicode-range: U+0000-00FF, U+0131, U+0152-0153, U+02BB-02BC, U+02C6, U+02DA, U+02DC, U+0304, U+0308, U+0329, U+2000-206F, U+20AC, U+2122, U+2191, U+2193, U+2212, U+2215, U+FEFF, U+FFFD;
}
</style>

{% schema %}
  {
    
    "name": "Section 6",
    "tag": "section",
    "class": "gps-555716577707229985 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/gardpro-us/apps/gempages-cro/app/shopify/edit?pageType=GP_STATIC&editorId=555716577622754081&sectionId=555716577707229985)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"gg1x9QhWJiB_text","label":"gg1x9QhWJiB_text","default":"DISCOVER OUR GARDPRO COLLECTION"},{"type":"html","id":"ggPJfKj9oQy_label","label":"ggPJfKj9oQy_label","default":"Add to cart"},{"type":"html","id":"ggPJfKj9oQy_outOfStockLabel","label":"ggPJfKj9oQy_outOfStockLabel","default":"Out of stock"},{"type":"html","id":"ggPJfKj9oQy_unavailableLabel","label":"ggPJfKj9oQy_unavailableLabel","default":"Unavailable"},{"type":"html","id":"ggPJfKj9oQy_variantSelectionRequiredMessage","label":"ggPJfKj9oQy_variantSelectionRequiredMessage","default":"Please select a variant to add product to cart"},{"type":"html","id":"ggz9XZKrdEc_label","label":"ggz9XZKrdEc_label","default":"Add to cart"},{"type":"html","id":"ggz9XZKrdEc_outOfStockLabel","label":"ggz9XZKrdEc_outOfStockLabel","default":"Out of stock"},{"type":"html","id":"ggz9XZKrdEc_unavailableLabel","label":"ggz9XZKrdEc_unavailableLabel","default":"Unavailable"},{"type":"html","id":"ggz9XZKrdEc_variantSelectionRequiredMessage","label":"ggz9XZKrdEc_variantSelectionRequiredMessage","default":"Please select a variant to add product to cart"},{"type":"html","id":"ggOnNI2ntG6_label","label":"ggOnNI2ntG6_label","default":"Add to cart"},{"type":"html","id":"ggOnNI2ntG6_outOfStockLabel","label":"ggOnNI2ntG6_outOfStockLabel","default":"Out of stock"},{"type":"html","id":"ggOnNI2ntG6_unavailableLabel","label":"ggOnNI2ntG6_unavailableLabel","default":"Unavailable"},{"type":"html","id":"ggOnNI2ntG6_variantSelectionRequiredMessage","label":"ggOnNI2ntG6_variantSelectionRequiredMessage","default":"Please select a variant to add product to cart"},{"type":"html","id":"gghvORnOXZS_label","label":"gghvORnOXZS_label","default":"Add to cart"},{"type":"html","id":"gghvORnOXZS_outOfStockLabel","label":"gghvORnOXZS_outOfStockLabel","default":"Out of stock"},{"type":"html","id":"gghvORnOXZS_unavailableLabel","label":"gghvORnOXZS_unavailableLabel","default":"Unavailable"},{"type":"html","id":"gghvORnOXZS_variantSelectionRequiredMessage","label":"gghvORnOXZS_variantSelectionRequiredMessage","default":"Please select a variant to add product to cart"},{"type":"html","id":"ggwfvSdFimC_label","label":"ggwfvSdFimC_label","default":"Add to cart"},{"type":"html","id":"ggwfvSdFimC_outOfStockLabel","label":"ggwfvSdFimC_outOfStockLabel","default":"Out of stock"},{"type":"html","id":"ggwfvSdFimC_unavailableLabel","label":"ggwfvSdFimC_unavailableLabel","default":"Unavailable"},{"type":"html","id":"ggwfvSdFimC_variantSelectionRequiredMessage","label":"ggwfvSdFimC_variantSelectionRequiredMessage","default":"Please select a variant to add product to cart"},{"type":"html","id":"ggDXxMsoezi_label","label":"ggDXxMsoezi_label","default":"Add to cart"},{"type":"html","id":"ggDXxMsoezi_outOfStockLabel","label":"ggDXxMsoezi_outOfStockLabel","default":"Out of stock"},{"type":"html","id":"ggDXxMsoezi_unavailableLabel","label":"ggDXxMsoezi_unavailableLabel","default":"Unavailable"},{"type":"html","id":"ggDXxMsoezi_variantSelectionRequiredMessage","label":"ggDXxMsoezi_variantSelectionRequiredMessage","default":"Please select a variant to add product to cart"},{"type":"html","id":"ggGiuOmDJgB_label","label":"ggGiuOmDJgB_label","default":"Add to cart"},{"type":"html","id":"ggGiuOmDJgB_outOfStockLabel","label":"ggGiuOmDJgB_outOfStockLabel","default":"Out of stock"},{"type":"html","id":"ggGiuOmDJgB_unavailableLabel","label":"ggGiuOmDJgB_unavailableLabel","default":"Unavailable"},{"type":"html","id":"ggGiuOmDJgB_variantSelectionRequiredMessage","label":"ggGiuOmDJgB_variantSelectionRequiredMessage","default":"Please select a variant to add product to cart"},{"type":"html","id":"ggaTdtEbyAw_label","label":"ggaTdtEbyAw_label","default":"Add to cart"},{"type":"html","id":"ggaTdtEbyAw_outOfStockLabel","label":"ggaTdtEbyAw_outOfStockLabel","default":"Out of stock"},{"type":"html","id":"ggaTdtEbyAw_unavailableLabel","label":"ggaTdtEbyAw_unavailableLabel","default":"Unavailable"},{"type":"html","id":"ggaTdtEbyAw_variantSelectionRequiredMessage","label":"ggaTdtEbyAw_variantSelectionRequiredMessage","default":"Please select a variant to add product to cart"},{"type":"html","id":"ggssfoLScFn_text","label":"ggssfoLScFn_text","default":"<p>Steel</p>"},{"type":"html","id":"ggamLOV_yqX_text","label":"ggamLOV_yqX_text","default":"<p>Polymeer</p>"},{"type":"html","id":"gg6T2SERSEi_text","label":"gg6T2SERSEi_text","default":"<p>Steel / Glass</p>"},{"type":"html","id":"ggeCFpQhr8x_text","label":"ggeCFpQhr8x_text","default":"<p>Polymeer</p>"},{"type":"html","id":"gg3FKM-W3AT_text","label":"gg3FKM-W3AT_text","default":"<p>CASE</p>"},{"type":"html","id":"ggz_KjSW6Qn_text","label":"ggz_KjSW6Qn_text","default":"<p>1.43 Inch<br>Ultra HD AMOLED<br>Up to 1000 Nits</p>"},{"type":"html","id":"ggVVSIQz0fG_text","label":"ggVVSIQz0fG_text","default":"<p>1.43 Inch<br>Ultra HD AMOLED<br>Up to 900 Nits</p>"},{"type":"html","id":"ggMG_cgVSIn_text","label":"ggMG_cgVSIn_text","default":"<p>2.06 Inch<br>Ultra HD AMOLED<br>Up to 1000 Nits</p>"},{"type":"html","id":"ggJpAjrHpOs_text","label":"ggJpAjrHpOs_text","default":"<p>1.72 Inch<br>IPS Display<br>Up to 700 Nits</p>"},{"type":"html","id":"ggvctlVAHJf_text","label":"ggvctlVAHJf_text","default":"<p>DISPLAY</p>"},{"type":"html","id":"ggwvf4iqIJG_text","label":"ggwvf4iqIJG_text","default":"<p>Up to 35 days<br>Fast Charging</p>"},{"type":"html","id":"ggURM9YknsN_text","label":"ggURM9YknsN_text","default":"<p>Up to 30 days<br>Fast Charging</p>"},{"type":"html","id":"gg_S8Fso8TX_text","label":"gg_S8Fso8TX_text","default":"<p>Up to 15 days<br>Fast Charging</p>"},{"type":"html","id":"ggNO-di7aOl_text","label":"ggNO-di7aOl_text","default":"<p>Up to 15 days<br>-</p>"},{"type":"html","id":"gg25ZGSesHI_text","label":"gg25ZGSesHI_text","default":"<p>BATTERY LIFE</p>"},{"type":"html","id":"ggr95xjWxv__text","label":"ggr95xjWxv__text","default":"<p>75g</p>"},{"type":"html","id":"ggrJllLcW1D_text","label":"ggrJllLcW1D_text","default":"<p>61g</p>"},{"type":"html","id":"ggJzbB24EE9_text","label":"ggJzbB24EE9_text","default":"<p>50g</p>"},{"type":"html","id":"ggXTyt_eMkT_text","label":"ggXTyt_eMkT_text","default":"<p>40g</p>"},{"type":"html","id":"ggwahwAkXAd_text","label":"ggwahwAkXAd_text","default":"<p>WEIGHT</p>"},{"type":"html","id":"ggJVJE9YZn7_text","label":"ggJVJE9YZn7_text","default":"<p>Heart Rate<br>Sleep<br>Sp02<br>Stress</p>"},{"type":"html","id":"ggpFpQOjSJb_text","label":"ggpFpQOjSJb_text","default":"<p>Heart Rate<br>Sleep<br>Sp02<br>Stress</p>"},{"type":"html","id":"ggut9-EzZcp_text","label":"ggut9-EzZcp_text","default":"<p>Heart Rate<br>Sleep<br>Sp02<br>Stress<br>Emotional state</p>"},{"type":"html","id":"ggBEntiMVjG_text","label":"ggBEntiMVjG_text","default":"<p>Heart Rate<br>Sleep<br>Sp02<br>-<br>-</p>"},{"type":"html","id":"ggPEjx2KDbB_text","label":"ggPEjx2KDbB_text","default":"<p>HEALTH TRACKING</p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
