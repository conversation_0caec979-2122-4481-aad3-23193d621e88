

<style {% if section.settings.section_preload == "false" %} class="gps-link gps-style" type="text/disabled-css" {% endif %}>.gps-557191162872464267.gps.gpsil [style*="--ai:"]{align-items:var(--ai)}.gps-557191162872464267.gps.gpsil [style*="--aspect:"]{aspect-ratio:var(--aspect)}.gps-557191162872464267.gps.gpsil [style*="--bg:"]{background:var(--bg)}.gps-557191162872464267.gps.gpsil [style*="--hvr-bg:"]:hover{background:var(--hvr-bg)}.gps-557191162872464267.gps.gpsil [style*="--bga:"]{background-attachment:var(--bga)}.gps-557191162872464267.gps.gpsil [style*="--bgc:"]{background-color:var(--bgc)}.gps-557191162872464267.gps.gpsil [style*="--hvr-bgc:"]:hover{background-color:var(--hvr-bgc)}.gps-557191162872464267.gps.gpsil [style*="--bgi:"]{background-image:var(--bgi)}.gps-557191162872464267.gps.gpsil [style*="--bgp:"]{background-position:var(--bgp)}.gps-557191162872464267.gps.gpsil [style*="--bgr:"]{background-repeat:var(--bgr)}.gps-557191162872464267.gps.gpsil [style*="--bgs:"]{background-size:var(--bgs)}.gps-557191162872464267.gps.gpsil [style*="--b:"]{border:var(--b)}.gps-557191162872464267.gps.gpsil [style*="--hvr-b:"]:hover{border:var(--hvr-b)}.gps-557191162872464267.gps.gpsil [style*="--bb:"]{border-bottom:var(--bb)}.gps-557191162872464267.gps.gpsil [style*="--bc:"]{border-color:var(--bc)}.gps-557191162872464267.gps.gpsil [style*="--hvr-bc:"]:hover{border-color:var(--hvr-bc)}.gps-557191162872464267.gps.gpsil [style*="--bblr:"]{border-bottom-left-radius:var(--bblr)}.gps-557191162872464267.gps.gpsil [style*="--bbrr:"]{border-bottom-right-radius:var(--bbrr)}.gps-557191162872464267.gps.gpsil [style*="--radius:"]{border-radius:var(--radius)}.gps-557191162872464267.gps.gpsil [style*="--bs:"]{border-style:var(--bs)}.gps-557191162872464267.gps.gpsil [style*="--hvr-bs:"]:hover{border-style:var(--hvr-bs)}.gps-557191162872464267.gps.gpsil [style*="--bt:"]{border-top:var(--bt)}.gps-557191162872464267.gps.gpsil [style*="--btlr:"]{border-top-left-radius:var(--btlr)}.gps-557191162872464267.gps.gpsil [style*="--btrr:"]{border-top-right-radius:var(--btrr)}.gps-557191162872464267.gps.gpsil [style*="--bw:"]{border-width:var(--bw)}.gps-557191162872464267.gps.gpsil [style*="--hvr-bw:"]:hover{border-width:var(--hvr-bw)}.gps-557191162872464267.gps.gpsil [style*="--shadow:"]{box-shadow:var(--shadow)}.gps-557191162872464267.gps.gpsil [style*="--c:"]{color:var(--c)}.gps-557191162872464267.gps.gpsil [style*="--hvr-c:"]:hover{color:var(--hvr-c)}.gps-557191162872464267.gps.gpsil [style*="--cg:"]{-moz-column-gap:var(--cg);column-gap:var(--cg)}.gps-557191162872464267.gps.gpsil [style*="--fd:"]{flex-direction:var(--fd)}.gps-557191162872464267.gps.gpsil [style*="--ff:"]{font-family:var(--ff)}.gps-557191162872464267.gps.gpsil [style*="--size:"]{font-size:var(--size)}.gps-557191162872464267.gps.gpsil [style*="--weight:"]{font-weight:var(--weight)}.gps-557191162872464267.gps.gpsil [style*="--fs:"]{font-style:var(--fs)}.gps-557191162872464267.gps.gpsil [style*="--gg:"]{grid-gap:var(--gg)}.gps-557191162872464267.gps.gpsil [style*="--gtc:"]{grid-template-columns:var(--gtc)}.gps-557191162872464267.gps.gpsil [style*="--h:"]{height:var(--h)}.gps-557191162872464267.gps.gpsil [style*="--jc:"]{justify-content:var(--jc)}.gps-557191162872464267.gps.gpsil [style*="--ls:"]{letter-spacing:var(--ls)}.gps-557191162872464267.gps.gpsil [style*="--lh:"]{line-height:var(--lh)}.gps-557191162872464267.gps.gpsil [style*="--m:"]{margin:var(--m)}.gps-557191162872464267.gps.gpsil [style*="--mb:"]{margin-bottom:var(--mb)}.gps-557191162872464267.gps.gpsil [style*="--mt:"]{margin-top:var(--mt)}.gps-557191162872464267.gps.gpsil [style*="--objf:"]{-o-object-fit:var(--objf);object-fit:var(--objf)}.gps-557191162872464267.gps.gpsil [style*="--op:"]{opacity:var(--op)}.gps-557191162872464267.gps.gpsil [style*="--o:"]{order:var(--o)}.gps-557191162872464267.gps.gpsil [style*="--pc:"]{place-content:var(--pc)}.gps-557191162872464267.gps.gpsil [style*="--p:"]{padding:var(--p)}.gps-557191162872464267.gps.gpsil [style*="--pb:"]{padding-bottom:var(--pb)}.gps-557191162872464267.gps.gpsil [style*="--pl:"]{padding-left:var(--pl)}.gps-557191162872464267.gps.gpsil [style*="--pr:"]{padding-right:var(--pr)}.gps-557191162872464267.gps.gpsil [style*="--pt:"]{padding-top:var(--pt)}.gps-557191162872464267.gps.gpsil [style*="--ta:"]{text-align:var(--ta)}.gps-557191162872464267.gps.gpsil [style*="--ts:"]{text-shadow:var(--ts)}.gps-557191162872464267.gps.gpsil [style*="--tt:"]{text-transform:var(--tt)}.gps-557191162872464267.gps.gpsil [style*="--t:"]{transform:var(--t)}.gps-557191162872464267.gps.gpsil [style*="--w:"]{width:var(--w)}.gps-557191162872464267.gps.gpsil [style*="--line-clamp:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp);display:-webkit-box;overflow:hidden}@media only screen and (max-width:1024px){.gps-557191162872464267.gps.gpsil [style*="--aspect-tablet:"]{aspect-ratio:var(--aspect-tablet)}.gps-557191162872464267.gps.gpsil [style*="--size-tablet:"]{font-size:var(--size-tablet)}.gps-557191162872464267.gps.gpsil [style*="--h-tablet:"]{height:var(--h-tablet)}.gps-557191162872464267.gps.gpsil [style*="--lh-tablet:"]{line-height:var(--lh-tablet)}.gps-557191162872464267.gps.gpsil [style*="--pl-tablet:"]{padding-left:var(--pl-tablet)}.gps-557191162872464267.gps.gpsil [style*="--pr-tablet:"]{padding-right:var(--pr-tablet)}.gps-557191162872464267.gps.gpsil [style*="--w-tablet:"]{width:var(--w-tablet)}.gps-557191162872464267.gps.gpsil [style*="--line-clamp-tablet:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-tablet);display:-webkit-box;overflow:hidden}}@media only screen and (max-width:767px){.gps-557191162872464267.gps.gpsil [style*="--aspect-mobile:"]{aspect-ratio:var(--aspect-mobile)}.gps-557191162872464267.gps.gpsil [style*="--size-mobile:"]{font-size:var(--size-mobile)}.gps-557191162872464267.gps.gpsil [style*="--gg-mobile:"]{grid-gap:var(--gg-mobile)}.gps-557191162872464267.gps.gpsil [style*="--gtc-mobile:"]{grid-template-columns:var(--gtc-mobile)}.gps-557191162872464267.gps.gpsil [style*="--h-mobile:"]{height:var(--h-mobile)}.gps-557191162872464267.gps.gpsil [style*="--lh-mobile:"]{line-height:var(--lh-mobile)}.gps-557191162872464267.gps.gpsil [style*="--mb-mobile:"]{margin-bottom:var(--mb-mobile)}.gps-557191162872464267.gps.gpsil [style*="--pb-mobile:"]{padding-bottom:var(--pb-mobile)}.gps-557191162872464267.gps.gpsil [style*="--pl-mobile:"]{padding-left:var(--pl-mobile)}.gps-557191162872464267.gps.gpsil [style*="--pr-mobile:"]{padding-right:var(--pr-mobile)}.gps-557191162872464267.gps.gpsil [style*="--pt-mobile:"]{padding-top:var(--pt-mobile)}.gps-557191162872464267.gps.gpsil [style*="--w-mobile:"]{width:var(--w-mobile)}.gps-557191162872464267.gps.gpsil [style*="--line-clamp-mobile:"]{-webkit-box-orient:vertical;-webkit-line-clamp:var(--line-clamp-mobile);display:-webkit-box;overflow:hidden}}.gps-557191162872464267 .-gp-rotate-90,.gps-557191162872464267 .gp-rotate-180,.gps-557191162872464267 .gp-rotate-90{--tw-translate-x:0;--tw-translate-y:0;--tw-rotate:0;--tw-skew-x:0;--tw-skew-y:0;--tw-scale-x:1;--tw-scale-y:1}.gps-557191162872464267 .gp-relative{position:relative}.gps-557191162872464267 .gp-mx-auto{margin-left:auto;margin-right:auto}.gps-557191162872464267 .gp-mb-0{margin-bottom:0}.gps-557191162872464267 .gp-flex{display:flex}.gps-557191162872464267 .gp-inline-flex{display:inline-flex}.gps-557191162872464267 .gp-grid{display:grid}.gps-557191162872464267 .gp-contents{display:contents}.gps-557191162872464267 .\!gp-hidden{display:none!important}.gps-557191162872464267 .gp-hidden{display:none}.gps-557191162872464267 .gp-h-auto{height:auto}.gps-557191162872464267 .gp-h-full{height:100%}.gps-557191162872464267 .gp-min-h-0{min-height:0}.gps-557191162872464267 .gp-w-full{width:100%}.gps-557191162872464267 .gp-max-w-full{max-width:100%}.gps-557191162872464267 .gp-flex-none{flex:none}.gps-557191162872464267 .-gp-rotate-90{--tw-rotate:-90deg}.gps-557191162872464267 .-gp-rotate-90,.gps-557191162872464267 .gp-rotate-180{transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-557191162872464267 .gp-rotate-180{--tw-rotate:180deg}.gps-557191162872464267 .gp-rotate-90{--tw-rotate:90deg;transform:translate(var(--tw-translate-x),var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))}.gps-557191162872464267 .gp-cursor-pointer{cursor:pointer}.gps-557191162872464267 .gp-grid-rows-\[1fr\]{grid-template-rows:1fr}.gps-557191162872464267 .gp-flex-col{flex-direction:column}.gps-557191162872464267 .gp-items-center{align-items:center}.gps-557191162872464267 .gp-justify-center{justify-content:center}.gps-557191162872464267 .gp-gap-y-0{row-gap:0}.gps-557191162872464267 .gp-overflow-hidden{overflow:hidden}.gps-557191162872464267 .gp-overflow-clip{overflow:clip}.gps-557191162872464267 .gp-transition-all{transition-duration:.15s;transition-property:all;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-557191162872464267 .gp-transition-colors{transition-duration:.15s;transition-property:color,background-color,border-color,text-decoration-color,fill,stroke;transition-timing-function:cubic-bezier(.4,0,.2,1)}.gps-557191162872464267 .gp-duration-200{transition-duration:.2s}.gps-557191162872464267 .gp-duration-500{transition-duration:.5s}.gps-557191162872464267 .gp-ease-in-out{transition-timing-function:cubic-bezier(.4,0,.2,1)}@media (max-width:1024px){.gps-557191162872464267 .tablet\:\!gp-hidden{display:none!important}.gps-557191162872464267 .tablet\:gp-hidden{display:none}.gps-557191162872464267 .tablet\:gp-h-auto{height:auto}.gps-557191162872464267 .tablet\:gp-flex-none{flex:none}}@media (max-width:767px){.gps-557191162872464267 .mobile\:\!gp-hidden{display:none!important}.gps-557191162872464267 .mobile\:gp-hidden{display:none}.gps-557191162872464267 .mobile\:gp-h-auto{height:auto}.gps-557191162872464267 .mobile\:gp-flex-none{flex:none}}.gps-557191162872464267 .\[\&\>svg\]\:\!gp-h-\[var\(--height-iconCollapseSize\)\]>svg{height:var(--height-iconCollapseSize)!important}.gps-557191162872464267 .\[\&\>svg\]\:\!gp-w-auto>svg{width:auto!important}.gps-557191162872464267 .\[\&_\*\]\:gp-max-w-full *{max-width:100%}.gps-557191162872464267 .\[\&_p\]\:gp-inline p{display:inline}.gps-557191162872464267 .\[\&_p\]\:after\:gp-whitespace-pre-wrap p:after{content:var(--tw-content);white-space:pre-wrap}.gps-557191162872464267 .\[\&_p\]\:after\:gp-content-\[\'\\A\'\] p:after{--tw-content:"\A";content:var(--tw-content)}</style>

       
      
            <section
              class="gp-mx-auto gp-max-w-full {% if section.settings.section_preload == "false" %}gps-lazy {% endif %}"
              style="--w:100%;--w-tablet:100%;--w-mobile:100%;--pl:0px;--pl-tablet:0px;--pl-mobile:0px;--pr:0px;--pr-tablet:0px;--pr-mobile:0px"
            >
              
    <div
      enableLazyloadImage="true" id="g8rlGLbbFK" data-id="g8rlGLbbFK"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--pt:80px;--pl:20px;--pb:48px;--pr:20px;--pt-mobile:70px;--pl-mobile:24px;--pb-mobile:12px;--pr-mobile:24px;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:100%;--w-tablet:100%;--w-mobile:100%;--bgc:#FFFFFF;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g8rlGLbbFK gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gX-Q9VXMhH gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="gmpBlmB1Eb" data-id="gmpBlmB1Eb"
        style="--bs:none;--bw:1px 1px 1px 1px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:0px;--mb-mobile:14px;--cg:30px;--pc:start;--gtc:minmax(0, 6fr) minmax(0, 6fr);--gtc-mobile:minmax(0, 12fr);--w:1170px;--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="gmpBlmB1Eb gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:space-between"
      class="gZfTtSfRNR gp-relative gp-flex gp-flex-col"
    >
      
       
      
    <div
      enableLazyloadImage="true" parentTag="Col" id="g1Bz1RXxGN" data-id="g1Bz1RXxGN"
        style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:grid;--d-mobile:grid;--d-tablet:grid;--op:100%;--mb:16px;--pr:48px;--mb-mobile:14px;--pr-mobile:auto;--cg:32px;--pc:start;--gtc:minmax(0, 12fr);--w:var(--g-ct-w, 1200px);--w-tablet:var(--g-ct-w, 100%);--w-mobile:var(--g-ct-w, 100%);--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll"
        class="g1Bz1RXxGN gp-mx-auto gp-mb-0 gp-grid gp-max-w-full gp-gap-y-0 gp-transition-colors gp-duration-200 gp-ease-in-out [&_*]:gp-max-w-full gp-grid-rows-[1fr]"
    >
    <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:start"
      class="gpAsBAQAMM gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="glo69T09hw">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="glo69T09hw "
        style="--ta:left;--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:32px;--mb-mobile:28px"
      >
      <div class="gp-flex" style="--jc:left">
        <h2
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ta:left;--fs:normal;--ff:var(--g-font-heading, heading);--weight:600;--ls:normal;--size:30px;--size-tablet:28px;--size-mobile:24px;--lh:130%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;--tt:uppercase;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.gglo69T09hw_text | replace: '$locationOrigin', locationOrigin }}</h2>
      </div>
    </div>
    </gp-text>
    <div gp-el-wrapper style="--bs:solid;--bw:1px 0px 0px 0px;--bc:#EEEEEE;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:16px;--pr:0px;--mb-mobile:14px;--pr-mobile:0px" class="g2R42OoOVX ">
      
    <gp-accordion
      data-id="g2R42OoOVX"
     uid="g2R42OoOVX"
      class="gp-flex gp-w-full gp-flex-col "
      style="--gg:0px;--gg-mobile:0px;--bgr:function (n) {
                return new Array(1 + n).join(this);
            };border-radius:inherit"
      gp-data='{"setting":{"iconSvg":"<svg width=\"17\" height=\"16\" viewBox=\"0 0 17 16\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path d=\"M5.64645 2.64645C5.84171 2.45118 6.15829 2.45118 6.35355 2.64645L11.3536 7.64645C11.5488 7.84171 11.5488 8.15829 11.3536 8.35355L6.35355 13.3536C6.15829 13.5488 5.84171 13.5488 5.64645 13.3536C5.45118 13.1583 5.45118 12.8417 5.64645 12.6464L10.2929 8L5.64645 3.35355C5.45118 3.15829 5.45118 2.84171 5.64645 2.64645Z\" fill=\"currentColor\"/>\n                  </svg>","isIconPlus":false,"activeKey":"1","expanded":false,"expandItem":true,"iconPosition":"right","iconGlobalSize":{"desktop":{"gap":"16px"}},"iconCollapse":"\n            <svg height=\"100%\" width=\"100%\" xmlns=\"http://www.w3.org/2000/svg\" classname=\"w-6 h-6\" viewBox=\"0 0 512 512\" fill=\"currentColor\">\n              <path fill=\"currentColor\" strokelinecap=\"round\" strokelinejoin=\"round\" d=\"M256 64a16 16 0 0 1 16 16v160h160a16 16 0 0 1 0 32h-160v160a16 16 0 0 1 -32 0v-160h-160a16 16 0 0 1 0 -32h160v-160A16 16 0 0 1 256 64Z\"></path></svg>\n            ","iconExpand":"\n            <svg height=\"100%\" width=\"100%\" xmlns=\"http://www.w3.org/2000/svg\" classname=\"w-6 h-6\" viewBox=\"0 0 512 512\" fill=\"currentColor\">\n              <path fill=\"currentColor\" strokelinecap=\"round\" strokelinejoin=\"round\" d=\"M64 256a16 16 0 0 1 16 -16h352a16 16 0 0 1 0 32h-352A16 16 0 0 1 64 256Z\"></path></svg>\n            ","iconCollapseSize":16,"layoutHeader":"text-only","expandedMode":"single","configIconSize":16,"parentUid":"g2R42OoOVX","childListNumber":["0"],"chidlrenUid":["gNQ2djR6B6","gIk476iLwe","g3dyYCfB6t","gZHBZFM6vp","gYrIEy_fIY"]},"styles":{"bgColor":{"active":"transparent","hover":"transparent","normal":"transparent"},"color":{"active":"#242424","hover":"#242424","normal":"#242424"},"headerBorder":{"active":{"border":"none","borderWidth":"Mixed","color":"#121212","isCustom":true,"position":"bottom","width":"0px 0px 1px 0px"},"hover":{"border":"none","borderWidth":"Mixed","color":"#EEEEEE","isCustom":true,"position":"bottom","width":"0px 0px 1px 0px"},"normal":{"border":"none","borderWidth":"Mixed","color":"#EEEEEE","isCustom":true,"position":"bottom","width":"0px 0px 1px 0px"}},"textColor":{"active":"#242424","hover":"#242424","normal":"#242424"},"iconColor":{"active":"#121212","hover":"#121212","normal":"#121212"},"contentSizePadding":{"desktop":{"gap":"0px","padding":{"bottom":"24px","left":"0px","right":"0px","top":"0px","type":"custom"}},"mobile":{"customShapeValue":"","gap":"0px","height":"","padding":{"bottom":"21px","left":"0px","right":"0px","top":"0px","type":"custom"},"shape":"","shapeValue":"","width":""}},"headerContentPadding":{"desktop":{"gap":"","padding":{"bottom":"16px","left":"16px","right":"16px","top":"24px","type":"custom"}},"mobile":{"customShapeValue":"","gap":"","height":"","padding":{"bottom":"21px","left":"14px","right":"14px","top":"21px","type":"custom"},"shape":"","shapeValue":"","width":""}},"widthHeightSize":{"desktop":{"height":"auto","width":"100%"}}},"uid":"g2R42OoOVX"}'
    >
    <div class="gp-hidden gp-rotate-90 -gp-rotate-90 gp-rotate-180"></div>
    <div
                class="gp-flex"
                style="--jc:center"
              >
                
  <div class="gp-overflow-clip" style="--w:100%;--w-tablet:100%;--w-mobile:100%;--height-iconCollapseSize:16px;--height-configIconSize:16px;--bs:solid;--bw:0px 0px 1px 0px;--bc:#EEEEEE;--bg:transparent;--shadow:none">
    <div
      data-index="0"
      class="g2R42OoOVX gp-accordion-item gp-overflow-hidden gp-child-item-g2R42OoOVX"
      style="scrollBehavior:smooth"
    >
      <div
        aria-hidden
        uid="gNQ2djR6B6"
        id="gNQ2djR6B6"
        data-index="0"
        class="g2R42OoOVX gp-accordion-item-g2R42OoOVX-0 gp-flex gp-items-center gp-accordion-item_header gp-cursor-pointer"
        style="--pl:16px;--pr:16px;--pt:24px;--pb:16px;--pl-mobile:14px;--pr-mobile:14px;--pt-mobile:21px;--pb-mobile:21px;--h:auto;--h-tablet:auto;--h-mobile:auto;--fd:row-reverse;--jc:space-between;gap:16px;--bgc:transparent;--bs:none;--bw:0px 0px 1px 0px;--bc:#121212;--c:#242424"
      >
       <style class="accordion-style">.gp-accordion-item-g2R42OoOVX-0:hover 
      {
        .gp-collapsible-icon { 
          color: #242424 !important;
        }

        .gp-icon { 
            color: #121212 !important;
        }
      }
    </style>
        <div class="gp-inline-flex">
        <span
            style="--c:#242424;width:16px;height:16px"
            data-index="0"
            class="g2R42OoOVX gp-accordion-item_icon gp-collapsible-icon gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden gp-transition-all gp-duration-200 [&>svg]:!gp-h-[var(--height-iconCollapseSize)] [&>svg]:!gp-w-auto"
          >
            <svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" classname="w-6 h-6" viewBox="0 0 512 512" fill="currentColor">
              <path fill="currentColor" strokelinecap="round" strokelinejoin="round" d="M64 256a16 16 0 0 1 16 -16h352a16 16 0 0 1 0 32h-352A16 16 0 0 1 64 256Z"></path></svg>
            </span>

        </div>
      <div class="gp-flex gp-items-center gp-w-full" style="--gg:16px;--jc:left">
        
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-overflow-hidden"
          style="--w:100%;--ts:none;--fs:normal;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:600;--ls:normal;--size:18px;--size-tablet:16px;--size-mobile:14px;--lh:160%;--lh-tablet:130%;--lh-mobile:130%;--tt:uppercase;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg2R42OoOVX_childItem_0 }}</div>
      </div>
    </div>
    </gp-text>
    
        
      </div>
      </div>
      <div
        uid="gNQ2djR6B6"
        data-index="0"
        data-show="true"
        class="g2R42OoOVX gp-accordion-item_body gp-transition-all gp-grid gp-duration-500 gp-overflow-hidden"
        style="grid-template-rows:1fr;grid-template-columns:minmax(auto, 100%)"
      >
        <div
        uid="gNQ2djR6B6"
        data-index="0"
        class="g2R42OoOVX gp-accordion-item_body-inner gp-min-h-0 gp-transition-all gp-duration-500"  style="--pl:0px;--pr:0px;--pt:0px;--pb:24px;--pl-mobile:0px;--pr-mobile:0px;--pt-mobile:0px;--pb-mobile:21px" >
            <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      
      class="gIqXVpREUN gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gjTiS0geft">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gjTiS0geft "
        style="--ta:left;--bs:none;--bw:0px 0px 1px 0px;--bc:#EEEEEE;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mt:0px;--pl:16px;--pb:0px;--pr:16px;--pl-mobile:14px;--pb-mobile:0px;--pr-mobile:14px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--ls:normal;--size:15px;--size-tablet:16px;--size-mobile:14px;--lh:160%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggjTiS0geft_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
        </div>
      </div>
    </div>
  </div>
  
              </div>
            <div
                class="gp-flex"
                style="--jc:center"
              >
                
  <div class="gp-overflow-clip" style="--w:100%;--w-tablet:100%;--w-mobile:100%;--height-iconCollapseSize:16px;--height-configIconSize:16px;--bs:solid;--bw:0px 0px 1px 0px;--bc:#EEEEEE;--bg:transparent;--shadow:none">
    <div
      data-index="1"
      class="g2R42OoOVX gp-accordion-item gp-overflow-hidden gp-child-item-g2R42OoOVX"
      style="scrollBehavior:smooth"
    >
      <div
        aria-hidden
        uid="gIk476iLwe"
        id="gIk476iLwe"
        data-index="1"
        class="g2R42OoOVX gp-accordion-item-g2R42OoOVX-1 gp-flex gp-items-center gp-accordion-item_header gp-cursor-pointer"
        style="--pl:16px;--pr:16px;--pt:24px;--pb:16px;--pl-mobile:14px;--pr-mobile:14px;--pt-mobile:21px;--pb-mobile:21px;--h:auto;--h-tablet:auto;--h-mobile:auto;--fd:row-reverse;--jc:space-between;gap:16px;--hvr-bgc:transparent;--bgc:transparent;--hvr-c:#242424;--c:#242424;--bs:none;--hvr-bs:none;--bw:0px 0px 1px 0px;--hvr-bw:0px 0px 1px 0px;--bc:#EEEEEE;--hvr-bc:#EEEEEE"
      >
       <style class="accordion-style">.gp-accordion-item-g2R42OoOVX-1:hover 
      {
        .gp-collapsible-icon { 
          color: #242424 !important;
        }

        .gp-icon { 
            color: #121212 !important;
        }
      }
    </style>
        <div class="gp-inline-flex">
        <span
            style="--hvr-c:#242424;--c:#242424;width:16px;height:16px"
            data-index="1"
            class="g2R42OoOVX gp-accordion-item_icon gp-collapsible-icon gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden gp-transition-all gp-duration-200 [&>svg]:!gp-h-[var(--height-iconCollapseSize)] [&>svg]:!gp-w-auto"
          >
            <svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" classname="w-6 h-6" viewBox="0 0 512 512" fill="currentColor">
              <path fill="currentColor" strokelinecap="round" strokelinejoin="round" d="M256 64a16 16 0 0 1 16 16v160h160a16 16 0 0 1 0 32h-160v160a16 16 0 0 1 -32 0v-160h-160a16 16 0 0 1 0 -32h160v-160A16 16 0 0 1 256 64Z"></path></svg>
            </span>

        </div>
      <div class="gp-flex gp-items-center gp-w-full" style="--gg:16px;--jc:left">
        
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-overflow-hidden"
          style="--w:100%;--ts:none;--fs:normal;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:600;--ls:normal;--size:18px;--size-tablet:16px;--size-mobile:14px;--lh:160%;--lh-tablet:130%;--lh-mobile:130%;--tt:uppercase;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg2R42OoOVX_childItem_1 }}</div>
      </div>
    </div>
    </gp-text>
    
        
      </div>
      </div>
      <div
        uid="gIk476iLwe"
        data-index="1"
        data-show="false"
        class="g2R42OoOVX gp-accordion-item_body gp-transition-all gp-grid gp-duration-500 gp-overflow-hidden"
        style="grid-template-rows:0fr;grid-template-columns:minmax(auto, 100%)"
      >
        <div
        uid="gIk476iLwe"
        data-index="1"
        class="g2R42OoOVX gp-accordion-item_body-inner gp-min-h-0 gp-transition-all gp-duration-500"  style="--pl:0px;--pr:0px;--pl-mobile:0px;--pr-mobile:0px" >
            <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      
      class="gECfZpY6Db gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gszr_nJ-ka">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gszr_nJ-ka "
        style="--ta:left;--bs:none;--bw:0px 0px 1px 0px;--bc:#EEEEEE;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mt:0px;--pl:16px;--pb:0px;--pr:16px;--pl-mobile:14px;--pb-mobile:0px;--pr-mobile:14px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--ls:normal;--size:15px;--size-tablet:16px;--size-mobile:14px;--lh:160%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggszr_nJ-ka_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
        </div>
      </div>
    </div>
  </div>
  
              </div>
            <div
                class="gp-flex"
                style="--jc:center"
              >
                
  <div class="gp-overflow-clip" style="--w:100%;--w-tablet:100%;--w-mobile:100%;--height-iconCollapseSize:16px;--height-configIconSize:16px;--bs:solid;--bw:0px 0px 1px 0px;--bc:#EEEEEE;--bg:transparent;--shadow:none">
    <div
      data-index="2"
      class="g2R42OoOVX gp-accordion-item gp-overflow-hidden gp-child-item-g2R42OoOVX"
      style="scrollBehavior:smooth"
    >
      <div
        aria-hidden
        uid="g3dyYCfB6t"
        id="g3dyYCfB6t"
        data-index="2"
        class="g2R42OoOVX gp-accordion-item-g2R42OoOVX-2 gp-flex gp-items-center gp-accordion-item_header gp-cursor-pointer"
        style="--pl:16px;--pr:16px;--pt:24px;--pb:16px;--pl-mobile:14px;--pr-mobile:14px;--pt-mobile:21px;--pb-mobile:21px;--h:auto;--h-tablet:auto;--h-mobile:auto;--fd:row-reverse;--jc:space-between;gap:16px;--hvr-bgc:transparent;--bgc:transparent;--hvr-c:#242424;--c:#242424;--bs:none;--hvr-bs:none;--bw:0px 0px 1px 0px;--hvr-bw:0px 0px 1px 0px;--bc:#EEEEEE;--hvr-bc:#EEEEEE"
      >
       <style class="accordion-style">.gp-accordion-item-g2R42OoOVX-2:hover 
      {
        .gp-collapsible-icon { 
          color: #242424 !important;
        }

        .gp-icon { 
            color: #121212 !important;
        }
      }
    </style>
        <div class="gp-inline-flex">
        <span
            style="--hvr-c:#242424;--c:#242424;width:16px;height:16px"
            data-index="2"
            class="g2R42OoOVX gp-accordion-item_icon gp-collapsible-icon gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden gp-transition-all gp-duration-200 [&>svg]:!gp-h-[var(--height-iconCollapseSize)] [&>svg]:!gp-w-auto"
          >
            <svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" classname="w-6 h-6" viewBox="0 0 512 512" fill="currentColor">
              <path fill="currentColor" strokelinecap="round" strokelinejoin="round" d="M256 64a16 16 0 0 1 16 16v160h160a16 16 0 0 1 0 32h-160v160a16 16 0 0 1 -32 0v-160h-160a16 16 0 0 1 0 -32h160v-160A16 16 0 0 1 256 64Z"></path></svg>
            </span>

        </div>
      <div class="gp-flex gp-items-center gp-w-full" style="--gg:16px;--jc:left">
        
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-overflow-hidden"
          style="--w:100%;--ts:none;--fs:normal;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:600;--ls:normal;--size:18px;--size-tablet:16px;--size-mobile:14px;--lh:160%;--lh-tablet:130%;--lh-mobile:130%;--tt:uppercase;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg2R42OoOVX_childItem_2 }}</div>
      </div>
    </div>
    </gp-text>
    
        
      </div>
      </div>
      <div
        uid="g3dyYCfB6t"
        data-index="2"
        data-show="false"
        class="g2R42OoOVX gp-accordion-item_body gp-transition-all gp-grid gp-duration-500 gp-overflow-hidden"
        style="grid-template-rows:0fr;grid-template-columns:minmax(auto, 100%)"
      >
        <div
        uid="g3dyYCfB6t"
        data-index="2"
        class="g2R42OoOVX gp-accordion-item_body-inner gp-min-h-0 gp-transition-all gp-duration-500"  style="--pl:0px;--pr:0px;--pl-mobile:0px;--pr-mobile:0px" >
            <div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      
      class="gijoYRv_cw gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gyqJ9E0SAd">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gyqJ9E0SAd "
        style="--ta:left;--bs:none;--bw:0px 0px 1px 0px;--bc:#EEEEEE;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mt:0px;--pl:16px;--pb:0px;--pr:16px;--pl-mobile:14px;--pb-mobile:0px;--pr-mobile:14px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--ls:normal;--size:15px;--size-tablet:16px;--size-mobile:14px;--lh:160%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggyqJ9E0SAd_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
        </div>
      </div>
    </div>
  </div>
  
              </div>
            <div
                class="gp-flex"
                style="--jc:center"
              >
                
  <div class="gp-overflow-clip" style="--w:100%;--w-tablet:100%;--w-mobile:100%;--height-iconCollapseSize:16px;--height-configIconSize:16px;--bs:solid;--bw:0px 0px 1px 0px;--bc:#EEEEEE;--bg:transparent;--shadow:none">
    <div
      data-index="3"
      class="g2R42OoOVX gp-accordion-item gp-overflow-hidden gp-child-item-g2R42OoOVX"
      style="scrollBehavior:smooth"
    >
      <div
        aria-hidden
        uid="gZHBZFM6vp"
        id="gZHBZFM6vp"
        data-index="3"
        class="g2R42OoOVX gp-accordion-item-g2R42OoOVX-3 gp-flex gp-items-center gp-accordion-item_header gp-cursor-pointer"
        style="--pl:16px;--pr:16px;--pt:24px;--pb:16px;--pl-mobile:14px;--pr-mobile:14px;--pt-mobile:21px;--pb-mobile:21px;--h:auto;--h-tablet:auto;--h-mobile:auto;--fd:row-reverse;--jc:space-between;gap:16px;--hvr-bgc:transparent;--bgc:transparent;--hvr-c:#242424;--c:#242424;--bs:none;--hvr-bs:none;--bw:0px 0px 1px 0px;--hvr-bw:0px 0px 1px 0px;--bc:#EEEEEE;--hvr-bc:#EEEEEE"
      >
       <style class="accordion-style">.gp-accordion-item-g2R42OoOVX-3:hover 
      {
        .gp-collapsible-icon { 
          color: #242424 !important;
        }

        .gp-icon { 
            color: #121212 !important;
        }
      }
    </style>
        <div class="gp-inline-flex">
        <span
            style="--hvr-c:#242424;--c:#242424;width:16px;height:16px"
            data-index="3"
            class="g2R42OoOVX gp-accordion-item_icon gp-collapsible-icon gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden gp-transition-all gp-duration-200 [&>svg]:!gp-h-[var(--height-iconCollapseSize)] [&>svg]:!gp-w-auto"
          >
            <svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" classname="w-6 h-6" viewBox="0 0 512 512" fill="currentColor">
              <path fill="currentColor" strokelinecap="round" strokelinejoin="round" d="M256 64a16 16 0 0 1 16 16v160h160a16 16 0 0 1 0 32h-160v160a16 16 0 0 1 -32 0v-160h-160a16 16 0 0 1 0 -32h160v-160A16 16 0 0 1 256 64Z"></path></svg>
            </span>

        </div>
      <div class="gp-flex gp-items-center gp-w-full" style="--gg:16px;--jc:left">
        
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-overflow-hidden"
          style="--w:100%;--ts:none;--fs:normal;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:600;--ls:normal;--size:18px;--size-tablet:16px;--size-mobile:14px;--lh:160%;--lh-tablet:130%;--lh-mobile:130%;--tt:uppercase;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg2R42OoOVX_childItem_3 }}</div>
      </div>
    </div>
    </gp-text>
    
        
      </div>
      </div>
      <div
        uid="gZHBZFM6vp"
        data-index="3"
        data-show="false"
        class="g2R42OoOVX gp-accordion-item_body gp-transition-all gp-grid gp-duration-500 gp-overflow-hidden"
        style="grid-template-rows:0fr;grid-template-columns:minmax(auto, 100%)"
      >
        <div
        uid="gZHBZFM6vp"
        data-index="3"
        class="g2R42OoOVX gp-accordion-item_body-inner gp-min-h-0 gp-transition-all gp-duration-500"  style="--pl:0px;--pr:0px;--pl-mobile:0px;--pr-mobile:0px" >
            <div
      enableLazyloadImage="true" tag="Col" type="component"
      style="--ai:normal;--jc:normal;--o:0"
      class="g9Gwjih9_J gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gh2pzTg0q_">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gh2pzTg0q_ "
        style="--ta:left;--bs:none;--bw:0px 0px 1px 0px;--bc:#EEEEEE;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mt:0px;--pl:16px;--pb:0px;--pr:16px;--pl-mobile:14px;--pb-mobile:0px;--pr-mobile:14px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--ls:normal;--size:15px;--size-tablet:16px;--size-mobile:14px;--lh:160%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggh2pzTg0q__text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
        </div>
      </div>
    </div>
  </div>
  
              </div>
            <div
                class="gp-flex"
                style="--jc:center"
              >
                
  <div class="gp-overflow-clip" style="--w:100%;--w-tablet:100%;--w-mobile:100%;--height-iconCollapseSize:16px;--height-configIconSize:16px;--bs:solid;--bw:0px 0px 1px 0px;--bc:#EEEEEE;--bg:transparent;--shadow:none">
    <div
      data-index="4"
      class="g2R42OoOVX gp-accordion-item gp-overflow-hidden gp-child-item-g2R42OoOVX"
      style="scrollBehavior:smooth"
    >
      <div
        aria-hidden
        uid="gYrIEy_fIY"
        id="gYrIEy_fIY"
        data-index="4"
        class="g2R42OoOVX gp-accordion-item-g2R42OoOVX-4 gp-flex gp-items-center gp-accordion-item_header gp-cursor-pointer"
        style="--pl:16px;--pr:16px;--pt:24px;--pb:16px;--pl-mobile:14px;--pr-mobile:14px;--pt-mobile:21px;--pb-mobile:21px;--h:auto;--h-tablet:auto;--h-mobile:auto;--fd:row-reverse;--jc:space-between;gap:16px;--hvr-bgc:transparent;--bgc:transparent;--hvr-c:#242424;--c:#242424;--bs:none;--hvr-bs:none;--bw:0px 0px 1px 0px;--hvr-bw:0px 0px 1px 0px;--bc:#EEEEEE;--hvr-bc:#EEEEEE"
      >
       <style class="accordion-style">.gp-accordion-item-g2R42OoOVX-4:hover 
      {
        .gp-collapsible-icon { 
          color: #242424 !important;
        }

        .gp-icon { 
            color: #121212 !important;
        }
      }
    </style>
        <div class="gp-inline-flex">
        <span
            style="--hvr-c:#242424;--c:#242424;width:16px;height:16px"
            data-index="4"
            class="g2R42OoOVX gp-accordion-item_icon gp-collapsible-icon gp-inline-flex gp-items-center gp-justify-center gp-overflow-hidden gp-transition-all gp-duration-200 [&>svg]:!gp-h-[var(--height-iconCollapseSize)] [&>svg]:!gp-w-auto"
          >
            <svg height="100%" width="100%" xmlns="http://www.w3.org/2000/svg" classname="w-6 h-6" viewBox="0 0 512 512" fill="currentColor">
              <path fill="currentColor" strokelinecap="round" strokelinejoin="round" d="M256 64a16 16 0 0 1 16 16v160h160a16 16 0 0 1 0 32h-160v160a16 16 0 0 1 -32 0v-160h-160a16 16 0 0 1 0 -32h160v-160A16 16 0 0 1 256 64Z"></path></svg>
            </span>

        </div>
      <div class="gp-flex gp-items-center gp-w-full" style="--gg:16px;--jc:left">
        
        
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="">
    <div
      
        class=" "
        
      >
      <div  >
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-overflow-hidden"
          style="--w:100%;--ts:none;--fs:normal;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:600;--ls:normal;--size:18px;--size-tablet:16px;--size-mobile:14px;--lh:160%;--lh-tablet:130%;--lh-mobile:130%;--tt:uppercase;word-break:break-word;overflow:hidden"
        >{{ section.settings.gg2R42OoOVX_childItem_4 }}</div>
      </div>
    </div>
    </gp-text>
    
        
      </div>
      </div>
      <div
        uid="gYrIEy_fIY"
        data-index="4"
        data-show="false"
        class="g2R42OoOVX gp-accordion-item_body gp-transition-all gp-grid gp-duration-500 gp-overflow-hidden"
        style="grid-template-rows:0fr;grid-template-columns:minmax(auto, 100%)"
      >
        <div
        uid="gYrIEy_fIY"
        data-index="4"
        class="g2R42OoOVX gp-accordion-item_body-inner gp-min-h-0 gp-transition-all gp-duration-500"  style="--pl:0px;--pr:0px;--pl-mobile:0px;--pr-mobile:0px" >
            <div
      enableLazyloadImage="true" tag="Col" type="component"
      style="--ai:normal;--jc:normal;--o:0"
      class="gUtxPGzQNd gp-relative gp-flex gp-flex-col"
    >
      
  {% assign locationOrigin = request.origin | append: routes.root_url | split: '/' | join: '/' %}
  <gp-text data-id="gbV7CVzJuw">
    <div
      enableLazyloadImage="true" parentTag="Col"
        class="gbV7CVzJuw "
        style="--ta:left;--bs:none;--bw:0px 0px 1px 0px;--bc:#EEEEEE;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mt:0px;--pl:16px;--pb:0px;--pr:16px;--pl-mobile:14px;--pb-mobile:0px;--pr-mobile:14px"
      >
      <div class="gp-flex" style="--jc:left">
        <div
          
          data-gp-text
          class="[&_p]:gp-inline [&_p]:after:gp-whitespace-pre-wrap [&_p]:after:gp-content-['\A'] gp-text"
          style="--w:100%;--w-tablet:100%;--w-mobile:100%;--ts:none;--ta:left;--line-clamp:unset;--line-clamp-tablet:unset;--line-clamp-mobile:unset;--fs:normal;--ff:var(--g-font-sans-serif, 'sans-serif'), var(--g-font-body, body);--weight:400;--ls:normal;--size:16px;--size-tablet:16px;--size-mobile:14px;--lh:160%;--lh-tablet:130%;--lh-mobile:130%;--c:#242424;word-break:break-word;--bgc:transparent;--bgi:url();--bgp:50% 50%;--bgs:cover;--bgr:no-repeat;--bga:scroll;overflow:hidden"
        >{{ section.settings.ggbV7CVzJuw_text | replace: '$locationOrigin', locationOrigin }}</div>
      </div>
    </div>
    </gp-text>
    
    </div>
        </div>
      </div>
    </div>
  </div>
  
              </div>
            
    </gp-accordion>
    <script {% if section.settings.section_preload == "false" %}class="gps-link" delay {% else %}src{% endif %}="https://d2ls1pfffhvy22.cloudfront.net/assets-v2/gp-accordion.js?v={{ shop.metafields.GEMPAGES.ASSETS_VERSION }}" defer="defer"></script>
  
      </div>
    </div>
    </div>
   
    
    </div><div
      enableLazyloadImage="true" label="Block" tag="Col" type="component"
      style="--jc:space-between"
      class="g7LkYH5owb gp-relative gp-flex gp-flex-col"
    >
      <div
    
     data-id="g4b3bXucyV"
    role="presentation"
    class="gp-group/image g4b3bXucyV gp-flex-none gp-h-auto mobile:gp-flex-none mobile:gp-h-auto tablet:gp-flex-none tablet:gp-h-auto"
    style="--bs:solid;--bw:0px;--bc:#121212;--shadow:none;--d:block;--d-mobile:block;--d-tablet:block;--op:100%;--mb:0px;--mb-mobile:0px;--ta:center"
    >
      <div
        class="pointer-events-auto gp-h-full gp-flex"
        
        class="gp-h-full gp-w-full"
        style="--shadow:none;border-radius:inherit;--jc:center"
      >
        
  <picture style="border-radius: inherit" class="gp-contents">
  
      <source media="(max-width: 767px)" srcset="{{ "gempages_553400155311702965-c52ea43b-a8ec-4745-93b5-2d685b411401.png" | file_url }}" />
      <source media="(max-width: 1024px)" srcset="{{ "gempages_553400155311702965-c52ea43b-a8ec-4745-93b5-2d685b411401.png" | file_url }}" />
      <img
        
        class="gp-h-full gp-max-w-full gp-flex"
        src="{{ "gempages_553400155311702965-c52ea43b-a8ec-4745-93b5-2d685b411401.png" | file_url }}"
        alt=""
        fetchpriority="high"
        style="--aspect:auto;--aspect-tablet:auto;--aspect-mobile:auto;--objf:cover;--w-mobile:100%;--h:750px;--h-tablet:750px;--h-mobile:auto;--btrr:12px;--bblr:12px;--bbrr:12px;--btlr:12px;--radiusType:custom"
      />
    
  </picture>
      </div>
  </div>
    </div>
    </div>
   
    
    </div>
    </div>
  
            </section>
           
    


{% schema %}
  {
    
    "name": "Section 7",
    "tag": "section",
    "class": "gps-557191162872464267 gps gpsil",
    "settings": [{"type":"paragraph","content":"Section design by GemPages. [Click here to start design](https://admin.shopify.com/store/gardpro-us/apps/gempages-cro/app/shopify/edit?pageType=GP_STATIC&editorId=555482666321838914&sectionId=557191162872464267)"},{"type":"select","label":"Section Preload","id":"section_preload","options":[{"label":"On","value":"true"},{"label":"Off","value":"false"},{"label":"Off Lazy Script","value":"off_lazy_script"}],"default":"false"},{"type":"text","label":"Checksum","id":"checksum"},{"type":"html","id":"gglo69T09hw_text","label":"gglo69T09hw_text","default":"FREQUENTLY ASKED QUESTIONS"},{"type":"html","id":"gg2R42OoOVX_childItem_0","label":"gg2R42OoOVX_childItem_0","default":"<p><span style=\"color:rgb(29,29,31);\">How do I know which Gard Pro model is best for me?</span></p>"},{"type":"html","id":"gg2R42OoOVX_childItem_1","label":"gg2R42OoOVX_childItem_1","default":"<p>Are the Gard Pro smartwatches pairable with any phone?</p>"},{"type":"html","id":"gg2R42OoOVX_childItem_2","label":"gg2R42OoOVX_childItem_2","default":"<p>How long will it take to receive my order?</p>"},{"type":"html","id":"gg2R42OoOVX_childItem_3","label":"gg2R42OoOVX_childItem_3","default":"<p>With which carrier do you ship your products?</p>"},{"type":"html","id":"gg2R42OoOVX_childItem_4","label":"gg2R42OoOVX_childItem_4","default":"<p>What are the dimensions of the Gard Pro smartwatches and will they fit on my wrist?</p>"},{"type":"html","id":"ggjTiS0geft_text","label":"ggjTiS0geft_text","default":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(29,29,31);font-size:17px;\">This is indeed a difficult choice! We are happy to help you choose. Therefore, we have created a list of questions for you on our homepage. In the middle of the page you should click on the blue button ''Find the perfect smartwatch''.</span></p>"},{"type":"html","id":"ggszr_nJ-ka_text","label":"ggszr_nJ-ka_text","default":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(29,29,31);font-size:17px;\">Our smartwatches pair with any Android device (Android 6 and above) and iPhone (iOS 9 and above).</span></p>"},{"type":"html","id":"ggyqJ9E0SAd_text","label":"ggyqJ9E0SAd_text","default":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(29,29,31);font-size:17px;\">Delivery time for customers worldwide 3-6 business days. Shortly after placing your order, you will receive a shipping confirmation email with a tracking number. If you have any questions, feel free to contact us.</span></p>"},{"type":"html","id":"ggh2pzTg0q__text","label":"ggh2pzTg0q__text","default":"<p><span style=\"background-color:rgb(255,255,255);color:rgb(29,29,31);font-size:17px;\">We deliver worldwide with, DHL, FEDEX, USPS for the fastest and most reliable service. Free shipping for orders in US and Canada above $50</span></p>"},{"type":"html","id":"ggbV7CVzJuw_text","label":"ggbV7CVzJuw_text","default":"<p><strong>Gard Pro Health Smartwatch 2 </strong>The case is 1.50 inches wide, 1.77 inches high and 0.43 inches thick.</p><p><strong>Gard Pro Health Smartwatch 2 </strong>The case is 1.50 inches wide, 1.77 inches high and 0,41 inches thick.</p><p><strong>Gard Pro Ultra </strong>The case is 1.71 wide, 2.24 inches high and 0.54 inches thick.</p><p><strong>Gard Pro Ultra 2+ </strong>The case is 2.02 inches wide and high and 0,51 inches thick.</p>"}],
    "blocks": [{"type": "@app"}],
    "locales": {}
  }
{% endschema %}
