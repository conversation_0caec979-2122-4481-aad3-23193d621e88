{%- style -%}
.logo-bar--{{ section.id }} {
  opacity: {{ section.settings.logo_opacity | divided_by: 100.0 }};
}
{%- endstyle -%}

{%- if section.settings.divider -%}<div class="asyncLoad section--divider">{%- endif -%}
<div class="asyncLoad page-width" data-aos="logo__animation">
  {%- if section.settings.title != blank -%}
    <div class="section-header">
      <h2 class="section-header__title">{{ section.settings.title | escape }}</h2>
    </div>
  {%- endif -%}

  {%- if section.blocks.size > 0 -%}
    <div class="logo-bar logo-bar--{{ section.id }} list_{{ section.id | remove:'-' | remove:'_' }}">
      {%- for block in section.blocks -%}
        <div class="logo-bar__item" {{ block.shopify_attributes }}>
          {%- if block.settings.link != blank -%}
            <a href="{{ block.settings.link }}" class="logo-bar__link">
          {%- endif -%}
          {%- if block.settings.image != blank -%}
            {%- assign img_url = block.settings.image | img_url: 'master' | replace: '_1x1.', '_{width}x.' -%}
            <img class="logo-bar__image lazyload"
                data-src="{{ img_url }}"
                data-widths="[60]"
                data-aspectratio="{{ block.settings.image.aspect_ratio }}"
                data-sizes="auto"
                alt="{{ block.settings.image.alt }}">
          {%- else -%}
            {{ 'logo' | placeholder_svg_tag: 'placeholder-svg' }}
          {%- endif -%}
          {%- if block.settings.link != blank -%}
            </a>
          {%- endif -%}
        </div>
      {%- endfor -%}
    </div>
  {%- endif -%}
</div>

{%- if section.settings.divider -%}</div>{%- endif -%}
<style>

  #shopify-section-{{ section.id }} .section-header__title{
    color: {{ section.settings.h_color }};
  }
  {% if section.settings.enable_bg_color == true %}
  #shopify-section-{{ section.id }} {
    background: {{ section.settings.bg_color }};
  }
    {% endif %}
  #shopify-section-{{ section.id }} .slick-prev {
    left: -10px;
  }
  #shopify-section-{{ section.id }} .slick-next {
    right: -10px;
  }
   #shopify-section-{{ section.id }} .slick-prev:before {
    content: unset;
  }
  #shopify-section-{{ section.id }} .slick-next:before {
    content: unset;
  }
  #shopify-section-{{ section.id }} .logo-bar__item{
    margin-block: 15px;
  }
  @media screen and (min-width: 750px){
    #shopify-section-{{ section.id }} {
      margin-block: {{ section.settings.margin_top_b_dsk }}px;
      {% if section.settings.enable_bg_color == true %}
      padding-block: 60px;
      {% endif %}
    }
   .list_{{ section.id | remove:'-' | remove:'_' }} .slick-arrow{
    width: 35px;
    height: 35px;
    z-index: 2;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #E0DFDF;
  }
  .list_{{ section.id | remove:'-' | remove:'_' }} .slick-arrow .fa:before{
    color: #fff;
    font-size: 30px;
  }
  }
  @media screen and (max-width: 750px){
    #shopify-section-{{ section.id }} {
      margin-block: {{ section.settings.margin_top_b_mob }}px;
      {% if section.settings.enable_bg_color == true %}
      padding-block: 40px;
      {% endif %}
    }
   .list_{{ section.id | remove:'-' | remove:'_' }} .slick-arrow{
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 2;
    background: #E0DFDF;
  }
  .list_{{ section.id | remove:'-' | remove:'_' }} .slick-arrow .fa:before{
    color: #fff;
    font-size: 20px;
  }
  }
</style>
<script>
  
$( document ).ready(function() {
  $('.list_{{ section.id | remove:'-' | remove:'_' }}').slick({
    slidesToShow: {{ section.settings.per_row_dsk }},
    slidesToScroll: 1,
    autoplay: true,
     prevArrow:"<button type='button' class='slick-prev pull-left'><i class='fa fa-angle-left' aria-hidden='true'></i></button>",
     nextArrow:"<button type='button' class='slick-next pull-right'><i class='fa fa-angle-right' aria-hidden='true'></i></button>",
    autoplaySpeed: 2000,
    arrows: true,
    responsive: [
    {
      breakpoint: 768,
      settings: {
        slidesToShow: 4
      }
    },
    {
      breakpoint: 480,
      settings: {
        slidesToShow: {{ section.settings.per_row_mob }}
      }
    }
  ]
  });
});

</script>
{% schema %}
{
  "name": "Logo List Slider",
  "class": "index-section",
  "max_blocks": 10,
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "t:sections.logo-list.settings.title.label"
    },
    {
      "type": "range",
      "id": "logo_opacity",
      "label": "t:sections.logo-list.settings.logo_opacity.label",
      "default": 76,
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "%"
    },
    {
      "type": "number",
      "id": "per_row_dsk",
      "label": "Slide Per Row Desktop",
      "default": 5
    },
    {
      "type": "number",
      "id": "per_row_mob",
      "label": "Slide Per Row Desktop",
      "default": 3
    },
    {
      "type": "color",
      "id": "h_color",
      "label": "Headig Color"
    },
    {
      "type": "checkbox",
      "id": "enable_bg_color",
      "label": "Use Background Color",
      "default": false
    },
    {
      "type": "color",
      "id": "bg_color",
      "label": "Slider Background Color"
    },
    {
      "type": "checkbox",
      "id": "divider",
      "label": "t:sections.logo-list.settings.divider.label",
      "default": false
    },
    {
      "type": "range",
      "id": "margin_top_b_dsk",
      "label": "Space top and bottom desktop",
      "default": 70,
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "margin_top_b_mob",
      "label": "White Space top and bottom mobile",
      "default": 40,
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "px"
    }
  ],
  "blocks": [
    {
      "type": "logo_image",
      "name": "t:sections.logo-list.blocks.logo.name",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.logo-list.blocks.logo.settings.image.label"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.logo-list.blocks.logo.settings.link.label",
          "info": "t:sections.logo-list.blocks.logo.settings.link.info"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Logo List Slider",
      "blocks": [
        {
          "type": "logo_image"
        },
        {
          "type": "logo_image"
        },
        {
          "type": "logo_image"
        },
        {
          "type": "logo_image"
        }
      ]
    }
  ]
}
{% endschema %}
