{%- style -%}
  .newsletter-{{ section.id }} {
    background-color: {{ section.settings.color_background }};
    color: {{ section.settings.color_text }};
  }

  .newsletter-{{ section.id }} .newsletter__input::-webkit-input-placeholder { color: {{ section.settings.color_text }}; }
  .newsletter-{{ section.id }} .newsletter__input::-moz-placeholder { color: {{ section.settings.color_text }}; }
  .newsletter-{{ section.id }} .newsletter__input::-ms-input-placeholder { color: {{ section.settings.color_text }}; }
{%- endstyle -%}

<div class="newsletter-section newsletter-{{ section.id }}{% if section.settings.color_background == settings.color_body_bg %} newsletter-section--with-divider{% endif %}">
  <div class="page-width text-center">
    {%- for block in section.blocks -%}
      <div class="theme-block" {{ block.shopify_attributes }}>
        {%- case block.type -%}
          {%- when '@app' -%}
            {% render block %}
          {%- when 'title' -%}
            {%- if block.settings.title != blank -%}
              <p class="h3">{{ block.settings.title | escape }}</p>
            {%- endif -%}
          {%- when 'text' -%}
            {%- if block.settings.text != blank -%}
              <div class="rte">{{ block.settings.text }}</div>
            {%- endif -%}
          {%- when 'form' -%}
            {%- render 'newsletter-form', section_id: section.id, snippet_context: 'section' -%}
          {%- when 'share_buttons' -%}
            {%- render 'social-sharing' -%}
        {%- endcase -%}
      </div>
    {%- endfor -%}
  </div>
</div>

{% schema %}
{
  "name": "t:sections.newsletter.name",
  "max_blocks": 6,
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "title",
      "name": "t:sections.newsletter.blocks.title.name",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "t:sections.newsletter.blocks.title.settings.title.label",
          "default": "Sign up and save"
        }
      ]
    },
    {
      "type": "text",
      "name": "t:sections.newsletter.blocks.text.name",
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "label": "t:sections.newsletter.blocks.text.settings.text.label",
          "default": "<p>Subscribe to get special offers, free giveaways, and once-in-a-lifetime deals.</p>"
        }
      ]
    },
    {
      "type": "form",
      "name": "t:sections.newsletter.blocks.form.name",
      "limit": 1
    },
    {
      "type": "share_buttons",
      "name": "t:sections.newsletter.blocks.share_buttons.name",
      "limit": 1
    }
  ],
  "settings": [
    {
      "type": "paragraph",
      "content": "t:sections.newsletter.settings.content"
    },
    {
      "type": "color",
      "id": "color_background",
      "label": "t:sections.newsletter.settings.color_background.label",
      "default": "#f9f9f9"
    },
    {
      "type": "color",
      "id": "color_text",
      "label": "t:sections.newsletter.settings.color_text.label",
      "default": "#1c1d1d"
    }
  ],
  "presets": [
    {
      "name": "t:sections.newsletter.presets.email_signup.name",
      "blocks": [
        {
          "type": "title"
        },
        {
          "type": "text"
        },
        {
          "type": "form"
        }
      ]
    }
  ]
}
{% endschema %}
