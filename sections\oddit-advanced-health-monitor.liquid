{% if section.settings.title != blank
  or section.settings.desc != blank
  or section.settings.image != blank
  or section.blocks.size > 0
%}
  <style>
    @media (max-width: 767px) {
      .advanced-health-monitor .swiper-button-prev,
      .advanced-health-monitor .swiper-button-next {
        position: unset;
      }
      .advanced-health-monitor .swiper-button-prev[aria-disabled='true'],
      .advanced-health-monitor .swiper-button-next[aria-disabled='true'] {
        background-color: unset;
        opacity: 0.3;
      }
    }
  </style>
  <div class="advanced-health-monitor section-{{ section.id }} tw-py-[40px] max-md:tw-py-[20px]">
    <div class="{% if section.settings.full_width %}full-width{% else %}page-width{% endif %} !tw-max-w-[1440px] !tw-px-[48px] max-md:!tw-px-[16px]">
      {% if section.settings.title != blank or section.settings.desc != blank or section.settings.image != blank %}
        <div class="oddit-health-monitor tw-flex tw-gap-[40px] max-lg:tw-gap-[20px] max-lg:tw-flex-col-reverse">
          {% if section.settings.title != blank or section.settings.desc != blank %}
            <div class="title-desc tw-pt-[120px] max-[1400px]:tw-pt-[60px] tw-max-w-[436px] max-lg:tw-max-w-full tw-w-full tw-pl-[50px] max-[1400px]:tw-pl-0 max-[1200px]:tw-pt-[30px] max-lg:tw-pt-0">
              {% if section.settings.title != blank %}
                <h2 class="title tw-text-[40px] max-md:tw-text-[32px] tw-font-semibold tw-leading-[1.3] tw-font-dm-sans tw-text-darkblack !tw-capitalize tw-mb-[20px]">
                  {{ section.settings.title }}
                </h2>
              {% endif %}
              {% if section.settings.desc != blank %}
                <div class="desc tw-text-[20px] max-md:tw-text-[15px] tw-text-darkblack tw-font-dm-sans tw-font-medium tw-leading-[1.3] *:tw-mb-0">
                  {{ section.settings.desc }}
                </div>
              {% endif %}
            </div>
          {% endif %}
          {% if section.settings.image != blank %}
            <div class="image-container tw-flex tw-max-w-[calc(100%-476px)] max-lg:tw-max-w-full max-lg:tw-h-[90vw] tw-w-full tw-rounded-[30px] tw-overflow-hidden">
              <img
                src="{{ section.settings.image | image_url : width: 5760 }}"
                class="right-img tw-w-full tw-h-full tw-object-cover"
                alt="{{ section.settings.image.alt }}"
              >
            </div>
          {% endif %}
        </div>
      {% endif %}
      {% if section.blocks.size > 0 %}
        <div class="thumbnail-with-text tw-max-w-[1012px] tw-w-full tw-ml-[50px] max-[1200px]:tw-ml-0 -tw-mt-[90px] max-[1200px]:tw-mt-[30px] tw-relative !tw-px-[72px] max-md:!tw-px-0 max-lg:tw-w-[calc(100%+96px)] max-lg:-tw-ml-[48px] max-md:tw-w-[calc(100%+32px)] max-md:-tw-ml-[16px]">
          <div class="swiper product-list-slider tw-overflow-hidden max-md:!tw-px-[16px]">
            <div class="swiper-wrapper">
              {% for block in section.blocks %}
                {% if block.settings.block_image != blank or block.settings.block_title != blank %}
                  <div class="thumbnail-image-info swiper-slide">
                    {% if block.settings.block_image != blank %}
                      <div class="thumbnail tw-mb-[23px] tw-px-[30px] tw-pt-[30px] tw-bg-[#E6E3D9]/90 tw-rounded-[30px] tw-flex">
                        <img
                          src="{{ block.settings.block_image | image_url : width: 200 }}"
                          class="block-image tw-rounded-[20px_20px_0_0] tw-overflow-hidden tw-w-full tw-h-auto"
                          alt="{{ block.settings.block_image.alt }}"
                        >
                      </div>
                    {% endif %}
                    {% if block.settings.block_title != blank %}
                      <div class="block-title tw-text-[20px] max-md:tw-text-[15px] tw-text-darkblack tw-font-dm-sans tw-font-semibold tw-leading-[1.3] *:tw-mb-0 tw-text-center tw-px-[10px]">
                        {{ block.settings.block_title }}
                      </div>
                    {% endif %}
                  </div>
                {% endif %}
              {% endfor %}
            </div>
          </div>
          <div class="max-md:tw-flex max-md:tw-justify-center max-md:tw-mt-[20px] max-md:tw-items-center max-md:tw-gap-[10px]">
            <div class="swiper-button-prev tw-top-1/2 -tw-translate-y-1/2 max-md:tw-translate-y-0 after:tw-hidden !tw-w-[52px] !tw-h-[52px] !tw-mt-0 tw-left-0 max-md:tw-bg-[#E6E3D9] max-md:tw-rounded-full">
              {{ 'icon-previous-btn.svg' | inline_asset_content }}
            </div>
            <div class="swiper-button-next tw-top-1/2 -tw-translate-y-1/2 max-md:tw-translate-y-0 after:tw-hidden !tw-w-[52px] !tw-h-[52px] !tw-mt-0 tw-right-0 max-md:tw-bg-[#E6E3D9] max-md:tw-rounded-full">
              {{ 'icon-next-btn.svg' | inline_asset_content }}
            </div>
          </div>
        </div>
      {% endif %}
    </div>
  </div>
{% endif %}

{% schema %}
{
  "name": "Oddit Advanced Health",
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "Section Full Width",
      "default": false
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title"
    },
    {
      "type": "richtext",
      "id": "desc",
      "label": "Description"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image"
    }
  ],
  "blocks": [
    {
      "type": "thumbnail-with-text",
      "name": "Thumbnail with text",
      "settings": [
        {
          "type": "image_picker",
          "id": "block_image",
          "label": "Block Image"
        },
        {
          "type": "richtext",
          "id": "block_title",
          "label": "Title"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Oddit Advanced Health"
    }
  ]
}
{% endschema %}
