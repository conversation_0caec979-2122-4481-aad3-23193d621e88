<style>
  .collapsible-trigger.is-open .dropdown-icon svg {
    transform: rotate(180deg);
  }
</style>
{% if section.settings.title != blank or section.blocks.size > 0 %}
  <div class="oddit-faq tw-py-[40px] max-md:tw-py-[20px]">
    <div class="{% if section.settings.full_width %}full-width{% else %}page-width{% endif %} !tw-px-[48px] max-md:!tw-px-[16px]">
      <div class="oddit-faq-back tw-bg-[#FFFDFB] tw-rounded-[40px] tw-py-[100px] tw-px-[95px] tw-max-w-[1240px] tw-mx-[auto] max-xl:tw-py-[60px] max-xl:tw-px-[55px] max-md:tw-py-[30px] max-md:tw-px-[20px]">
        <div class="oddit-faq-main tw-flex tw-flex-wrap tw-gap-[20px]">
          <div class="left-title tw-w-[calc(43%-10px)] max-lg:tw-w-full max-lg:tw-text-center">
            {%- if section.settings.title != blank -%}
              <h2 class="title tw-font-dm-sans tw-font-semibold tw-text-[40px] tw-text-darkblack tw-my-0 tw-leading-[1.3] max-md:tw-text-[32px] !tw-capitalize">
                {{ section.settings.title | escape }}
              </h2>
            {%- endif -%}
          </div>
          {%- if section.blocks.size > 0 -%}
            <div class="faq-content tw-w-[calc(57%-10px)] max-lg:tw-w-full">
              {%- for block in section.blocks -%}
                <div class="faq-info tw-border-0 tw-border-b tw-border-solid tw-border-[#E6E3D9] last:tw-border-b-0">
                  <button
                    type="button"
                    class="collapsible-trigger tw-py-[16px] tw-pl-[16px] tw-pr-[56px] tw-relative tw-leading-[normal] tw-text-left tw-w-full"
                    aria-controls="FAQ-content-{{ block.id }}"
                  >
                    <span class="question-title tw-font-dm-sans tw-font-medium tw-text-[16px] tw-text-darkblack">
                      {{- block.settings.title -}}
                    </span>
                    <span class="dropdown-icon tw-absolute tw-right-[16px] tw-top-[50%] tw-translate-y-[-50%]">
                      {{- 'chevron-down.svg' | inline_asset_content -}}
                    </span>
                  </button>
                  <div
                    id="FAQ-content-{{ block.id }}"
                    class="collapsible-content collapsible-content--all tw-pb-[0]"
                  >
                    <div class="collapsible-content__inner collapsible-content__inner--faq rte  tw-px-[16px] !tw-pb-[16px] tw-font-dm-sans tw-font-normal tw-text-[15px] tw-text-black">
                      {{ block.settings.text }}
                    </div>
                  </div>
                </div>
              {%- endfor -%}
            </div>
          {%- endif -%}
        </div>
      </div>
    </div>
  </div>
{% endif %}
{% schema %}
{
  "name": "Oddit FAQ",
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "Section Full Width",
      "default": false
    },
    {
      "type": "text",
      "id": "title",
      "label": "Title",
      "default": "FAQs"
    }
  ],
  "blocks": [
    {
      "type": "question",
      "name": "Question",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Question",
          "default": "Frequently asked question"
        },
        {
          "type": "richtext",
          "id": "text",
          "label": "Answer",
          "default": "<p>Answer</p>"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Oddit FAQ"
    }
  ]
}
{% endschema %}
