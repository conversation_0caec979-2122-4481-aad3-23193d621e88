<div class="oddit-fitness-tracking-main tw-max-w-[1340px] tw-w-full tw-px-[48px] max-md:tw-px-[16px] tw-mx-auto tw-pt-[60px] max-md:tw-py-[20px] tw-pb-[110px]">
  <div class="tw-flex tw-items-center tw-flex-wrap tw-gap-[20px]">
    {% if section.settings.image != blank %}
      <div class="image tw-w-[calc(50%-10px)] max-lg:tw-w-full tw-flex tw-rounded-[40px]">
        <img
          src="{{ section.settings.image | img_url: 'master' }}"
          alt="{{ section.settings.image.alt }}"
          class="tw-w-full tw-object-cover"
          loading="lazy"
        >
      </div>
    {% endif %}
    <div class="content-inner tw-w-[calc(50%-10px)] max-lg:tw-w-full">
      {% if section.settings.title != blank or section.settings.description != blank %}
        <div class="title-content tw-pl-[29px] max-md:tw-pl-0 tw-pb-[28px] max-md:tw-pb-[35px] tw-max-w-[534px] tw-w-full">
          {% if section.settings.title != blank %}
            <h2 class="tw-m-0 tw-mb-[20px] max-md:tw-mb-[15px] tw-text-[38px] max-md:tw-text-[32px] tw-text-darkblack tw-font-semibold tw-font-dm-sans tw-leading-[1] max-md:tw-leading-[normal] tw-tracking-normal !tw-capitalize">
              {{ section.settings.title }}
            </h2>
          {% endif %}
          {% if section.settings.description != blank %}
            <p class="tw-m-0 tw-text-[20px] max-md:tw-text-[15px] tw-text-darkblack tw-font-medium tw-font-dm-sans tw-leading-[normal] tw-tracking-normal">
              {{ section.settings.description }}
            </p>
          {% endif %}
        </div>
      {% endif %}

      <div class="tabs-wrapper">
        {%- assign groups = '' -%}
        {%- assign group_titles = '' -%}

        {% for block in section.blocks %}
          {%- assign groups = groups | append: block.settings.group | append: ',' -%}
          {%- assign group_titles = group_titles | append: block.settings.tab_title | append: ',' -%}
        {% endfor %}

        {%- assign unique_groups = groups | split: ',' | uniq -%}
        {%- assign unique_titles = group_titles | split: ',' | uniq -%}

        <div class="tabs-container">
          <div class="swiper tabs-switch-main -tw-mb-[20px] max-md:tw-mb-[20px] !tw-pl-[30px] max-md:!tw-pl-0">
            <div class="tabs-switch swiper-wrapper !tw-gap-0">
              {% for group in unique_groups %}
                {% assign index = forloop.index0 %}
                {% if group != '' and unique_titles[index] != blank %}
                  <div
                    class="swiper-slide !tw-w-fit tab-button{% if forloop.first %} active{% endif %} tw-min-w-max !tw-border-none !tw-p-[15px] tw-text-[15px] tw-font-bold tw-text-[#4A4741] tw-font-dm-sans tw-leading-[1] tw-uppercase tw-bg-[#FFFDFB] tw-rounded-[38px]"
                    data-group="{{ group | handleize }}"
                  >
                    {{ unique_titles[index] }}
                  </div>
                {% endif %}
              {% endfor %}
            </div>
            <div class="swiper-scrollbar tw-hidden"></div>
          </div>

          <div class="tab-content-wrapper tw-bg-[#E6E3D9]/80 tw-rounded-[40px] tw-pt-[50px] tw-overflow-hidden max-md:tw-w-[calc(100%+16px)] max-md:tw-rounded-tr-none max-md:tw-rounded-br-none">
            {% for group in unique_groups %}
              {% if group != '' %}
                <div
                  class="tab-card tab-card-{{ forloop.index }}{% if forloop.first %} active{% endif %} tw-overflow-hidden"
                  data-group="{{ group | handleize }}"
                >
                  <div class="swiper tab-card-items tab-card-items-{{ forloop.index }} swiper-container !tw-pl-[30px]">
                    <div class="swiper-wrapper">
                      {% for block in section.blocks %}
                        {% if block.settings.group == group %}
                          {% if block.settings.image != blank %}
                            <div class="tab-item swiper-slide !tw-flex">
                              <img
                                class="!tw-w-full !tw-max-w-full"
                                src="{{ block.settings.image | img_url: 'master' }}"
                                alt="{{ block.settings.image.alt }}"
                                loading="lazy"
                              >
                            </div>
                          {% endif %}
                        {% endif %}
                      {% endfor %}
                    </div>
                    <div class="swiper-button-main hide">
                      <div class="fitness-tracking-swiper-button-prev swiper-button-prev"></div>
                      <div class="fitness-tracking-swiper-button-next swiper-button-next"></div>
                    </div>
                  </div>
                </div>
              {% endif %}
            {% endfor %}
          </div>
          <div class="custom-button-main tw-flex tw-items-center tw-justify-end max-md:tw-justify-start tw-gap-[10px] tw-mt-[20px]">
            <div
              class="button-prev swiper-button-disabled after:tw-hidden !tw-w-[52px] !tw-h-[52px] !tw-mt-0 tw-cursor-pointer"
              style="position: unset;"
            >
              {{ 'icon-previous-btn.svg' | inline_asset_content }}
            </div>
            <div
              class="button-next after:tw-hidden !tw-w-[52px] !tw-h-[52px] !tw-mt-0 tw-cursor-pointer"
              style="position: unset;"
            >
              {{ 'icon-next-btn.svg' | inline_asset_content }}
            </div>
          </div>
        </div>

        <style>
          .oddit-fitness-tracking-main .tabs-switch {
            display: flex;
            gap: 10px;
          }
          .oddit-fitness-tracking-main .tab-button {
            cursor: pointer;
            padding: 10px;
            border: 1px solid #ccc;
            background: #f7f7f7;
          }
          .oddit-fitness-tracking-main .tab-card {
            display: none;
          }
          .oddit-fitness-tracking-main .tab-card.active {
            display: block;
          }
          .oddit-fitness-tracking-main .tab-card-items {
            display: flex;
            gap: 10px;
          }
          .oddit-fitness-tracking-main .tab-item img {
            width: 100%;
            height: auto;
            max-width: 150px;
          }
          .oddit-fitness-tracking-main .tabs-switch::-webkit-scrollbar {
            display: none;
          }
          .oddit-fitness-tracking-main .tabs-switch .tab-button {
            transition: all 0.3s linear;
          }
          .oddit-fitness-tracking-main .tabs-switch .tab-button:hover {
            background-color: #4a4741;
            color: #ffffff;
          }
          .oddit-fitness-tracking-main .tabs-switch .tab-button.active {
            background-color: #4a4741;
            color: #ffffff;
          }
          .oddit-fitness-tracking-main .custom-button-main .button-prev.swiper-button-disabled {
            opacity: 0.35;
            pointer-events: none;
            cursor: auto;
          }
          .oddit-fitness-tracking-main .custom-button-main .button-next.swiper-button-disabled {
            opacity: 0.35;
            pointer-events: none;
            cursor: auto;
          }
          .oddit-fitness-tracking-main .tabs-switch-main::after {
            background: linear-gradient(90deg, rgba(255, 255, 255, 0) 0%, #f5f3ef 81.01%);
            position: absolute;
            top: 0;
            right: 0;
            display: block;
            width: 100px;
            height: 45px;
            content: '';
            z-index: 1;
          }
        </style>

        <script>
          document.addEventListener('DOMContentLoaded', function () {
            const prevBtn = document.querySelectorAll('.custom-button-main .button-prev');
            prevBtn.forEach((button) => {
              button.onclick = function () {
                this.closest('.tabs-container')
                  .querySelector('.tab-card.active .swiper-button-main .swiper-button-prev')
                  .click();
                if (
                  this.closest('.tabs-container')
                    .querySelector('.tab-card.active .swiper-button-main .swiper-button-prev')
                    .classList.contains('swiper-button-disabled')
                ) {
                  this.classList.add('swiper-button-disabled');
                  this.closest('.tabs-container')
                    .querySelector('.tab-card.active .swiper-button-main .swiper-button-next')
                    .classList.remove('swiper-button-disabled');
                } else {
                  this.classList.remove('swiper-button-disabled');
                  this.closest('.custom-button-main')
                    .querySelector('.button-next')
                    .classList.remove('swiper-button-disabled');
                }
              };
            });
            const nextBtn = document.querySelectorAll('.custom-button-main .button-next');
            nextBtn.forEach((button) => {
              button.onclick = function () {
                this.closest('.tabs-container')
                  .querySelector('.tab-card.active .swiper-button-main .swiper-button-next')
                  .click();
                if (
                  this.closest('.tabs-container')
                    .querySelector('.tab-card.active .swiper-button-main .swiper-button-next')
                    .classList.contains('swiper-button-disabled')
                ) {
                  this.classList.add('swiper-button-disabled');
                  this.closest('.custom-button-main')
                    .querySelector('.button-prev')
                    .classList.remove('swiper-button-disabled');
                } else {
                  this.classList.remove('swiper-button-disabled');
                  this.closest('.custom-button-main')
                    .querySelector('.button-prev')
                    .classList.remove('swiper-button-disabled');
                }
              };
            });

            function arrowsChange(button) {
              let btn = button.closest('.tabs-container').querySelector('.tab-card.active');
              if (btn) {
                let prevButton = btn.querySelector('.swiper-button-prev');
                let nextButton = btn.querySelector('.swiper-button-next');
                let tabsContainer = btn.closest('.tabs-container').querySelector('.custom-button-main');
                let buttonPrev = tabsContainer.querySelector('.button-prev');
                let buttonNext = tabsContainer.querySelector('.button-next');

                if (prevButton && prevButton.classList.contains('swiper-button-disabled')) {
                  buttonNext.classList.remove('swiper-button-disabled');
                  buttonPrev.classList.add('swiper-button-disabled');
                }

                if (nextButton && nextButton.classList.contains('swiper-button-disabled')) {
                  buttonPrev.classList.remove('swiper-button-disabled');
                  buttonNext.classList.add('swiper-button-disabled');
                }
              }
            }

            new Swiper('.oddit-fitness-tracking-main .swiper.tabs-switch-main', {
              slidesPerView: 2.3,
              spaceBetween: 10,
              freeMode: true,
              mousewheel: {
                forceToAxis: true,
              },
              scrollbar: {
                el: '.oddit-fitness-tracking-main .swiper-scrollbar',
                draggable: true,
              },
              breakpoints: {
                0: {
                  slidesPerView: 1.3,
                  spaceBetween: 10,
                },
                768: {
                  slidesPerView: 2.3,
                  spaceBetween: 10,
                },
              },
            });

            document.querySelectorAll('.oddit-fitness-tracking-main').forEach((section) => {
              let buttons = section.querySelectorAll('.tab-button');
              let tabCards = section.querySelectorAll('.tab-card');

              buttons.forEach((button) => {
                button.addEventListener('click', function () {
                  buttons.forEach((btn) => {
                    btn.classList.remove('active');
                  });
                  this.classList.add('active');
                  let group = this.getAttribute('data-group');
                  tabCards.forEach((card) => {
                    card.classList.remove('active');
                    if (card.getAttribute('data-group') === group) {
                      card.classList.add('active');
                      card
                        .closest('.tabs-container')
                        .querySelector('.button-prev')
                        .classList.remove('swiper-button-disabled');
                      card
                        .closest('.tabs-container')
                        .querySelector('.button-next')
                        .classList.remove('swiper-button-disabled');
                      card.querySelector('.tab-card-items').swiper.slideTo(0);
                      card.querySelector('.tab-card-items').swiper.update();
                      const arrowChangeTimeout = setTimeout(() => {
                        arrowsChange(button);
                        clearTimeout(arrowChangeTimeout);
                      }, 100);
                    }
                  });
                });
              });
            });

            let count = 0;
            document
              .querySelectorAll('.oddit-fitness-tracking-main .swiper-container')
              .forEach((swiperContainer, index) => {
                count++;
                const swiper = new Swiper(swiperContainer, {
                  slidesPerView: 2.1,
                  spaceBetween: 10,
                  loop: false,
                  allowTouchMove: true,
                  navigation: {
                    nextEl: '.fitness-tracking-swiper-button-next',
                    prevEl: '.fitness-tracking-swiper-button-prev',
                  },
                  breakpoints: {
                    0: {
                      slidesPerView: 1.3,
                      spaceBetween: 10,
                    },
                    768: {
                      slidesPerView: 2.3,
                      spaceBetween: 10,
                    },
                  },
                });
              });
          });
        </script>
      </div>
    </div>
  </div>
</div>

{% schema %}
{
  "name": "Oddit Fitness Tracking",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Comprehensive Fitness Tracking"
    },
    {
      "type": "textarea",
      "id": "description",
      "label": "Description"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "Image"
    }
  ],
  "blocks": [
    {
      "type": "image-tab",
      "name": "Image Tab",
      "settings": [
        {
          "type": "text",
          "id": "group",
          "label": "Group"
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "Image"
        },
        {
          "type": "text",
          "id": "tab_title",
          "label": "Tab Title"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Oddit Fitness Tracking"
    }
  ]
}
{% endschema %}
