{% if section.blocks.size > 0 %}
  <div class="oddit-icon-with-text tw-bg-[#4A4741] tw-py-[51px]">
    <div class="{% if section.settings.full_width %}full-width{% else %}page-width{% endif %} !tw-px-[48px]  max-md:!tw-px-[30px]">
      <div class="icon-with-text tw-grid tw-gap-[15px] tw-grid-cols-3 tw-max-w-[1278px] tw-mx-[auto] max-md:tw-grid-cols-1 max-md:tw-gap-y-[60px]">
        {% for block in section.blocks %}
          {% if block.settings.icon != blank or block.settings.heading != blank or block.settings.text != blank %}
            <div class="icon-with-text-info tw-text-center">
              {% if block.settings.icon != blank %}
                <img
                  src="{{ block.settings.icon | image_url : width: 5760 }}"
                  class="icon !tw-w-[36px] !tw-h-[36px] tw-object-contain !tw-mb-[10px]"
                  alt="{{ block.settings.icon.alt }}"
                >
              {% endif %}
              {% if block.settings.heading != blank %}
                <h3 class="heading tw-font-dm-sans tw-font-medium tw-text-[20px] tw-text-white tw-mt-0 tw-mb-[10px] !tw-capitalize tw-tracking-normal">
                  {{ block.settings.heading }}
                </h3>
              {% endif %}
              {% if block.settings.text != blank %}
                <div class="text tw-font-dm-sans tw-font-normal tw-text-[15px] tw-text-white tw-mt-0 tw-mb-0 tw-leading-[1] tw-tracking-normal">
                  {{ block.settings.text }}
                </div>
              {% endif %}
            </div>
          {% endif %}
        {% endfor %}
      </div>
    </div>
  </div>
{% endif %}
{% schema %}
{
  "name": "Oddit icon with text",
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "Section Full Width",
      "default": false
    }
  ],
  "blocks": [
    {
      "name": "Icon with text",
      "type": "icon-with-text",
      "settings": [
        {
          "type": "image_picker",
          "id": "icon",
          "label": "Icon"
        },
        {
          "type": "text",
          "id": "heading",
          "label": "Title"
        },
        {
          "type": "text",
          "id": "text",
          "label": "Text"
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Oddit icon with text"
    }
  ]
}
{% endschema %}
