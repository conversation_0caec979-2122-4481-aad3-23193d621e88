<style>
  .product-comparisons-main .table-card p {
    margin-bottom: 0;
  }

  .product-comparisons-main .table-card.active,
  .product-comparisons-main .table-card.active-tab {
    display: block;
  }

  .product-comparisons-main .parent-drop-down.active .dropdown {
    display: block;
    top: 100%;
    z-index: 1;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.2);
  }
  .product-comparisons-main .parent-drop-down.active {
    border-color: #4a4741;
  }
  .product-comparisons-main .parent-drop-down.active > span svg {
    transform: rotate(180deg);
  }
  .product-comparisons-main .parent-drop-down .item-title.disabled {
    opacity: 0.5;
  }

  .product-comparisons-main .table-card:nth-child(2),
  .product-comparisons-main .table-card.active {
    background-color: #fff;
  }
  @media only screen and (max-width: 767px) {
    .product-comparisons-main .table-card div:last-child {
      border: none;
    }
    .product-comparisons-main .table-card.active {
      order: -1;
      background-color: #fff !important;
    }
    .product-comparisons-main .table-card:nth-child(2) {
      background-color: transparent;
    }
  }
</style>
{% if section.settings.title != blank or section.blocks.size > 0 %}
  <div class="product-comparisons-main tw-mt-[55px]">
    <div
      class="page-width !tw-max-w-[1340px] tw-w-full !tw-px-[48px] max-md:!tw-px-[16px] tw-mx-auto"
    >
      {% if section.settings.title != blank %}
        <h2 class="heading !tw-mb-[38px] tw-text-[40px] max-md:tw-text-[32px] max-md:tw-font-bold tw-text-darkblack tw-font-semibold tw-leading-[1] tw-tracking-normal tw-font-dm-sans !tw-capitalize tw-text-center max-md:!tw-mb-[30px]">
          {{ section.settings.title }}
        </h2>
      {% endif %}
      {% if section.blocks.size > 0 %}
        <div class="comparision-table tw-max-w-[1244px] tw-mx-[auto]">
          <div class="mobile-tab tw-mb-[24px] md:tw-hidden">
            <div class="parent-drop-dow-main tw-flex tw-w-full">
              <div class="parent-drop-down tw-relative tw-border-0 tw-border-b tw-border-solid tw-border-[#4A4741] tw-w-[50%]">
                <p class="dropdown-btn tw-mb-0 tw-font-dm-sans tw-font-medium tw-text-[14px] tw-text-darkblack tw-leading-[1] tw-tracking-[-0.03rem] tw-py-[20px] tw-pl-[12px] tw-pr-[41px] tw-h-full tw-flex tw-items-center">
                  {{ section.blocks[0].settings.product.title }}
                </p>
                <span class="tw-absolute tw-flex tw-top-[50%] tw-translate-y-[-50%] tw-right-[19px] tw-pointer-events-none">
                  {{- 'compare-arrow.svg' | inline_asset_content -}}
                </span>
                <ul class="dropdown dropdown-col-1 tw-absolute tw-hidden tw-top-0 tw-left-0 tw-bg-white tw-p-[10px] tw-m-0">
                  {% for block in section.blocks %}
                    {%- assign item = block.settings.product -%}
                    <li
                      class="item-title{% if forloop.first %} active{% endif %}{% if forloop.index == 2 %} disabled{% endif %} tw-text-[13px] tw-font-dm-sans tw-font-normal tw-px-[13px] tw-py-[10px] tw-list-none tw-leading-[1] !tw-mb-0"
                      data-block-id="block-{{ block.id }}"
                      data-title="{{ item.title }}"
                    >
                      {{ item.title }}
                    </li>
                  {% endfor %}
                </ul>
              </div>
              <div class="parent-drop-down tw-relative tw-border-0 tw-border-b tw-border-solid tw-border-[#D9D9D9] tw-w-[50%]">
                <p class="dropdown-btn tw-mb-0 tw-font-dm-sans tw-font-medium tw-text-[14px] tw-text-darkblack tw-leading-[1] tw-tracking-[-0.03rem] tw-py-[20px] tw-pl-[12px] tw-pr-[41px] tw-h-full tw-flex tw-items-center">
                  {{ section.blocks[1].settings.product.title }}
                </p>
                <span class="tw-absolute tw-flex tw-top-[50%] tw-translate-y-[-50%] tw-right-[19px] tw-pointer-events-none">
                  {{- 'compare-arrow.svg' | inline_asset_content -}}
                </span>
                <ul class="dropdown dropdown-col-2 tw-absolute tw-hidden tw-top-0 tw-left-0 tw-bg-white tw-p-[10px] tw-m-0">
                  {% for block in section.blocks %}
                    {%- assign item = block.settings.product -%}
                    <li
                      class="item-title{% if forloop.index == 2 %} active{% endif %}{% if forloop.first %} disabled{% endif %} tw-text-[14px] tw-font-dm-sans tw-font-normal tw-px-[10px] tw-py-[10px] tw-list-none tw-leading-[1] !tw-mb-0"
                      data-block-id="block-{{ block.id }}"
                      data-title="{{ item.title }}"
                    >
                      {{ item.title }}
                    </li>
                  {% endfor %}
                </ul>
              </div>
            </div>
          </div>

          <div class="comparision-inner tw-flex tw-pt-[115px] tw-pb-[70px] max-md:tw-pt-[80px] max-md:tw-pb-[30px]">
            <div class="table-card first-default-card tw-w-[16%] max-md:tw-hidden">
              <div
                class="title-content tw-px-[10px] tw-py-[20px] tw-font-dm-sans tw-font-medium tw-text-[20px] tw-text-darkblack tw-leading-[1] tw-min-h-[144px] tw-border-0 tw-border-b tw-border-solid tw-border-[#E6E3D9] max-xl:tw-min-h-[130px]"
              ></div>
              {% if section.settings.title_1 != blank %}
                <div class="title-content tw-px-[10px] tw-py-[20px] tw-font-dm-sans tw-font-medium tw-text-[20px] tw-text-darkblack tw-leading-[1] tw-min-h-[112px] tw-border-0 tw-border-b tw-border-solid tw-border-[#E6E3D9] max-xl:tw-text-[18px] max-lg:tw-text-[16px]">
                  <span class="tw-line-clamp-3">{{ section.settings.title_1 }}</span>
                </div>
              {% endif %}
              {% if section.settings.title_2 != blank %}
                <div class="title-content tw-px-[10px] tw-py-[20px] tw-font-dm-sans tw-font-medium tw-text-[20px] tw-text-darkblack tw-leading-[1] tw-min-h-[112px] tw-border-0 tw-border-b tw-border-solid tw-border-[#E6E3D9] max-xl:tw-text-[18px] max-lg:tw-text-[16px] tw-line-clamp-3">
                  <span class="tw-line-clamp-3">{{ section.settings.title_2 }}</span>
                </div>
              {% endif %}
              {% if section.settings.title_3 != blank %}
                <div class="title-content tw-px-[10px] tw-py-[20px] tw-font-dm-sans tw-font-medium tw-text-[20px] tw-text-darkblack tw-leading-[1] tw-min-h-[112px] tw-border-0 tw-border-b tw-border-solid tw-border-[#E6E3D9] max-xl:tw-text-[18px] max-lg:tw-text-[16px] tw-line-clamp-3">
                  <span class="tw-line-clamp-3">{{ section.settings.title_3 }}</span>
                </div>
              {% endif %}
              {% if section.settings.title_4 != blank %}
                <div class="title-content tw-px-[10px] tw-py-[20px] tw-font-dm-sans tw-font-medium tw-text-[20px] tw-text-darkblack tw-leading-[1] tw-min-h-[112px] tw-border-0 tw-border-b tw-border-solid tw-border-[#E6E3D9] max-xl:tw-text-[18px] max-lg:tw-text-[16px] tw-line-clamp-3">
                  <span class="tw-line-clamp-3">{{ section.settings.title_4 }}</span>
                </div>
              {% endif %}
              {% if section.settings.title_5 != blank %}
                <div class="title-content tw-px-[10px] tw-py-[20px] tw-font-dm-sans tw-font-medium tw-text-[20px] tw-text-darkblack tw-leading-[1] tw-min-h-[112px] max-md:tw-min-h-[61px] max-xl:tw-text-[18px] max-lg:tw-text-[16px] tw-line-clamp-3">
                  <span class="tw-line-clamp-3">{{ section.settings.title_5 }}</span>
                </div>
              {% endif %}
              <div class="title-content"></div>
            </div>
            {% for block in section.blocks %}
              {%- assign item = block.settings.product -%}
              <div
                class="table-card{% if forloop.first %} first-card active{% endif %}{% if forloop.index == 2 %} active-tab{% endif %} tw-w-[21%] tw-rounded-[10px] max-md:tw-hidden max-md:tw-w-[50%]"
                data-block-id="block-{{ block.id }}"
              >
                <div class="product-content tw-flex tw-flex-wrap tw-flex-col tw-relative tw-px-[10px] tw-py-[20px] tw-text-center tw-min-h-[144px] tw-border-0 tw-border-b tw-border-solid tw-border-[#E6E3D9] max-xl:tw-min-h-[130px]">
                  {% if product.handle == item.handle %}
                    <span class="tw-absolute tw-top-[-150px] tw-left-[-20px] max-lg:tw-top-[-120px] max-md:tw-hidden">
                      <span class="tw-font-dm-sans tw-font-semibold tw-text-[15px] tw-text-darkblack tw-text-center tw-max-w-[100px] tw-block max-xl:tw-text-[13px] tw-mb-[8px] tw-rotate-[-5deg]"
                        >Currently viewing</span
                      >
                      {{ 'round-arrow.svg' | inline_asset_content }}
                    </span>
                  {% endif %}
                  {%- if item.featured_image != blank -%}
                    <div class="image tw-absolute tw-left-[50%] tw-translate-x-[-50%] tw-top-[-115px] max-lg:tw-top-[-80px]">
                      <img
                        class="tw-w-[134px] tw-h-[134px] tw-object-contain max-lg:tw-w-[100px] max-lg:tw-h-[100px]"
                        src="{{ item.featured_image | img_url: 'master' }}"
                        alt="{{ item.featured_image.alt }}"
                      >
                    </div>
                  {%- endif -%}
                  <h3 class="tw-font-dm-sans tw-font-bold tw-text-[20px] tw-text-darkblack tw-text-center !tw-mb-[2px] tw-mt-[auto] tw-line-clamp-2 max-xl:!tw-text-[18px] max-lg:!tw-text-[16px] !tw-capitalize !tw-leading-[normal]">
                    {{ item.title }}
                  </h3>
                  <div class="item-price tw-flex tw-items-center tw-justify-center tw-flex-wrap">
                    <span
                      class="product__price{% if item.compare_at_price > item.price %} on-sale-custom {% endif %} !tw-text-[20px] !tw-text-[#008001] tw-font-dm-sans tw-font-semibold tw-tracking-normal !tw-m-0 !tw-p-0 !tw-mr-[0] tw-leading-[normal] max-xl:!tw-text-[18px] max-lg:!tw-text-[16px]"
                    >
                      {{- item.price | money_without_trailing_zeros -}}
                    </span>

                    {%- assign hide_sale_price = true -%}
                    {%- if item.compare_at_price_max > item.price -%}
                      {%- if item.compare_at_price > item.price -%}
                        {%- assign hide_sale_price = false -%}
                      {%- endif -%}
                      <span class="{% if hide_sale_price %} hide{% endif %}">
                        <span
                          data-compare-price
                          class="product__price product__price--compare-custom tw-line-through !tw-text-[16px] !tw-text-[#80868B] !tw-font-normal !tw-font-dm-sans !tw-p-0 !tw-m-0 !tw-ml-[10px] max-xl:!tw-text-[14px] max-xl:!tw-ml-[7px]"
                        >
                          {%- if item.compare_at_price > item.price -%}
                            {{ item.compare_at_price | money_without_trailing_zeros }}
                          {%- endif -%}
                        </span>
                      </span>
                    {%- endif -%}

                    {%- liquid
                      assign compare_at_price = item.compare_at_price
                      assign price = item.price | default: 1999

                      if compare_at_price > price
                        assign sale_percentage = compare_at_price | minus: price | times: 100.0 | divided_by: compare_at_price | money_without_currency | times: 100 | remove: '.0'
                      endif
                    -%}
                    {%- if sale_percentage -%}
                      <span class="tw-ml-[10px] tw-text-[15px] tw-text-white tw-font-bold tw-font-dm-sans tw-uppercase tw-leading-[1] tw-bg-[#008001] tw-rounded-[20px] tw-px-[7px] tw-py-[4px] max-xl:!tw-text-[13px] max-xl:!tw-ml-[7px]">
                        SAVE {{ sale_percentage }}%
                      </span>
                    {%- endif -%}
                  </div>
                </div>
                {% if block.settings.case != blank %}
                  <div class="case tw-px-[15px] tw-py-[20px] tw-min-h-[112px] tw-border-0 tw-border-b tw-border-solid tw-border-[#E6E3D9]">
                    <p class="tw-font-dm-sans tw-font-normal tw-text-[16px] tw-text-darkblack max-xl:tw-text-[14px] max-md:tw-text-[16px] tw-line-clamp-3">
                      {{ block.settings.case }}
                    </p>
                  </div>
                {% endif %}
                {% if block.settings.display != blank %}
                  <div class="display case tw-px-[15px] tw-py-[20px] tw-min-h-[112px] tw-border-0 tw-border-b tw-border-solid tw-border-[#E6E3D9]">
                    <p class="tw-font-dm-sans tw-font-normal tw-text-[16px] tw-text-darkblack max-xl:tw-text-[14px] max-md:tw-text-[16px] tw-line-clamp-3">
                      {{ block.settings.display }}
                    </p>
                  </div>
                {% endif %}
                {% if block.settings.battery != blank %}
                  <div class="battery case tw-px-[15px] tw-py-[20px] tw-min-h-[112px] tw-border-0 tw-border-b tw-border-solid tw-border-[#E6E3D9]">
                    <p class="tw-font-dm-sans tw-font-normal tw-text-[16px] tw-text-darkblack max-xl:tw-text-[14px] max-md:tw-text-[16px] tw-line-clamp-3">
                      {{ block.settings.battery }}
                    </p>
                  </div>
                {% endif %}
                {% if block.settings.weight != blank %}
                  <div class="weight case tw-px-[15px] tw-py-[20px] tw-min-h-[112px] tw-border-0 tw-border-b tw-border-solid tw-border-[#E6E3D9]">
                    <p class="tw-font-dm-sans tw-font-normal tw-text-[16px] tw-text-darkblack max-xl:tw-text-[14px] max-md:tw-text-[16px] tw-line-clamp-3">
                      {{ block.settings.weight }}
                    </p>
                  </div>
                {% endif %}
                {% if block.settings.health_tracking != blank %}
                  <div class="health-tracking case tw-px-[15px] tw-py-[20px] tw-min-h-[112px] tw-border-0 tw-border-b tw-border-solid tw-border-[#E6E3D9]">
                    <p class="tw-font-dm-sans tw-font-normal tw-text-[16px] tw-text-darkblack max-xl:tw-text-[14px] max-md:tw-text-[16px] tw-line-clamp-3">
                      {{ block.settings.health_tracking }}
                    </p>
                  </div>
                {% endif %}

                <div class="add_to_cart tw-px-[15px] tw-py-[20px] tw-min-h-[112px] tw-border-0 tw-border-b tw-border-solid tw-border-[#E6E3D9] max-md:tw-min-h-[62px]">
                  {% if block.settings.add_to_cart %}
                    <add-item
                      class="custom-upsell-add tw-text-[#0071E3] tw-text-[16px] tw-font-medium tw-font-dm-sans tw-flex tw-items-center tw-justify-between tw-leading-[normal] tw-cursor-pointer max-xl:tw-text-[14px] max-md:tw-text-[16px] max-md:tw-gap-[12px] max-md:tw-justify-center"
                      data-id="{{ item.selected_or_first_available_variant.id }}"
                    >
                      <span class="tw-underline md:tw-flex-1 max-md:tw-leading-[1]">Add to cart</span>
                      {{ 'arrow-link-icon.svg' | inline_asset_content }}
                    </add-item>
                  {% else %}
                    <a
                      href="{{ item.url }}"
                      class="tw-font-dm-sans tw-font-medium tw-text-[16px] !tw-text-[#0071E3] tw-flex tw-justify-between tw-items-center tw-gap-[10px] tw-no-underline max-xl:tw-text-[14px] max-md:tw-text-[16px] max-md:tw-gap-[12px] max-md:tw-justify-center"
                    >
                      <span class="mobile-hide tw-underline tw-flex-1 max-md:tw-hidden tw-line-clamp-3"
                        >Shop the {{ item.title -}}
                      </span>
                      <span class="desktop-hide tw-underline md:tw-hidden tw-leading-[1]">Shop now</span>
                      {{ 'arrow-link-icon.svg' | inline_asset_content }}
                    </a>
                  {% endif %}
                </div>
              </div>
            {% endfor %}
          </div>
        </div>
      {% endif %}
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      let dropdownBtn = document.querySelectorAll('.product-comparisons-main .dropdown-btn');
      if (dropdownBtn) {
        dropdownBtn.forEach(function (item) {
          item.addEventListener('click', function () {
            this.closest('.parent-drop-down').classList.toggle('active');
          });
        });
      }
      let dropdownLiFirst = document.querySelectorAll('.product-comparisons-main .dropdown-col-1 li');
      if (dropdownLiFirst) {
        dropdownLiFirst.forEach(function (item) {
          item.addEventListener('click', function () {
            if (
              this.closest('.product-comparisons-main').querySelector(
                `.comparision-inner .table-card:not(.active-tab)[data-block-id="${this.dataset.blockId}"]`
              )
            ) {
              this.closest('.product-comparisons-main')
                .querySelectorAll('.comparision-inner .table-card')
                .forEach((i) => {
                  i.classList.remove('active');
                });
              this.closest('.parent-drop-down').querySelector('.dropdown-btn').textContent = this.dataset.title;
              this.closest('.parent-drop-down').classList.remove('active');

              this.closest('.product-comparisons-main')
                .querySelector(
                  `.comparision-inner .table-card:not(.active-tab)[data-block-id="${this.dataset.blockId}"]`
                )
                .classList.add('active');

              this.closest('.comparision-table')
                .querySelectorAll(`.dropdown-col-2 li`)
                .forEach((i) => {
                  i.classList.remove('disabled');
                });

              this.closest('.comparision-table')
                .querySelector(`.dropdown-col-2 li[data-block-id="${this.dataset.blockId}"]`)
                .classList.add('disabled');
            }
          });
        });
      }
      let dropdownLiSecond = document.querySelectorAll('.product-comparisons-main .dropdown-col-2 li');
      if (dropdownLiSecond) {
        dropdownLiSecond.forEach(function (item) {
          item.addEventListener('click', function () {
            if (
              this.closest('.product-comparisons-main').querySelector(
                `.comparision-inner .table-card:not(.active)[data-block-id="${this.dataset.blockId}"]`
              )
            ) {
              this.closest('.product-comparisons-main')
                .querySelectorAll('.comparision-inner .table-card')
                .forEach((i) => {
                  i.classList.remove('active-tab');
                });
              this.closest('.parent-drop-down').querySelector('.dropdown-btn').textContent = this.dataset.title;
              this.closest('.parent-drop-down').classList.remove('active');

              this.closest('.product-comparisons-main')
                .querySelector(`.comparision-inner .table-card:not(.active)[data-block-id="${this.dataset.blockId}"]`)
                .classList.add('active-tab');

              this.closest('.comparision-table')
                .querySelectorAll(`.dropdown-col-1 li`)
                .forEach((i) => {
                  i.classList.remove('disabled');
                });

              this.closest('.comparision-table')
                .querySelector(`.dropdown-col-1 li[data-block-id="${this.dataset.blockId}"]`)
                .classList.add('disabled');
            }
          });
        });
      }
      const main = document.querySelectorAll('.product-comparisons-main');
      main.forEach((button) => {
        button.onclick = function (event) {
          if (!event.target.closest('.mobile-tab')) {
            let dropdowns = document.querySelectorAll('.parent-drop-down');
            dropdowns.forEach((i) => {
              i.classList.remove('active');
            });
          }
        };
      });
    });
  </script>
{% endif %}

{% schema %}
{
  "name": "Oddit Product Comparison",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "Heading",
      "default": "Compare Gard Pro Models"
    },
    {
      "type": "text",
      "id": "title_1",
      "label": "Title 1"
    },
    {
      "type": "text",
      "id": "title_2",
      "label": "Title 2"
    },
    {
      "type": "text",
      "id": "title_3",
      "label": "Title 3"
    },
    {
      "type": "text",
      "id": "title_4",
      "label": "Title 4"
    },
    {
      "type": "text",
      "id": "title_5",
      "label": "Title 5"
    }
  ],
  "blocks": [
    {
      "type": "product",
      "name": "Product",
      "settings": [
        {
          "type": "product",
          "id": "product",
          "label": "Product"
        },
        {
          "type": "textarea",
          "id": "case",
          "label": "Case Material"
        },
        {
          "type": "textarea",
          "id": "display",
          "label": "Display Specifications"
        },
        {
          "type": "textarea",
          "id": "battery",
          "label": "Battery Life"
        },
        {
          "type": "textarea",
          "id": "weight",
          "label": "Weight"
        },
        {
          "type": "textarea",
          "id": "health_tracking",
          "label": "Health Tracking Features"
        },
        {
          "type": "checkbox",
          "id": "add_to_cart",
          "label": "Enable Add to Cart",
          "default": true
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "Oddit Product Comparison"
    }
  ]
}
{% endschema %}
