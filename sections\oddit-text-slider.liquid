{% if section.blocks.size > 0 %}
  <div class="oddit-text-slider tw-my-[40px] max-md:tw-my-[20px]" id="oddit-text-slider--{{ section.id }}">
    <div class="{% if section.settings.full_width %}full-width{% else %}page-width{% endif %} !tw-max-w-[1440px] !tw-px-[48px] max-md:!tw-px-[0px]">
      <div class="text-slider tw-flex tw-py-[30px] tw-rounded-[20px] tw-overflow-hidden max-md:tw-rounded-[0px]">
        {% capture text_slider %} 
        <div class="marquee-container tw-max-w-fit tw-min-w-fit tw-flex">
          {% for block in section.blocks %}
            {% if block.settings.title != blank %}
              <div class="title tw-text-[#F5F3EF] tw-font-dm-sans tw-font-bold tw-text-[20px] !tw-px-[27px] tw-relative after:tw-content-[''] after:tw-absolute after:tw-top-[50%] after:tw-left-[0px] after:tw-translate-y-[-50%] after:tw-w-[5px] after:tw-h-[5px] after:tw-rounded-[100%] after:tw-bg-[#F5F3EF] tw-whitespace-nowrap">
                {{ block.settings.title }}
              </div>
            {% endif %}
          {% endfor %}
        </div>
    {% endcapture %}
        {{ text_slider }}
        {{ text_slider }}
      </div>
    </div>
  </div>
{% endif %}
{% style %}
  #oddit-text-slider--{{ section.id }} .text-slider{
      Background-color: {{ section.settings.bg_color }}
  }
    .marquee-container {
      animation: marquee 15s linear infinite;
    }

    @keyframes marquee {
      0% {
        transform: translateX(0%);
      }
      100% {
        transform: translateX(-100%);
      }
    }
{% endstyle %}
{% schema %}
{
  "name": "Oddit text slider",
  "settings": [
    {
      "type": "checkbox",
      "id": "full_width",
      "label": "Section Full Width",
      "default": false
    },
    {
      "type": "color",
      "id": "bg_color",
      "label": "Background Color",
      "default": "#ffffff"
    }
  ],
  "blocks": [
    {
      "name": "Title",
      "type": "title",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "Title"
        }
      ]
    }
  ],

  "presets": [
    {
      "name": "Oddit text slider"
    }
  ]
}
{% endschema %}
