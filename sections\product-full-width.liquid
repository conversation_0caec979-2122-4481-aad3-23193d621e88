{%- if section.blocks.size > 0 -%}
  <div class="page-width{% if section.settings.max_width %} page-width--narrow{% endif %}">
    {%- for block in section.blocks -%}
      {%- case block.type -%}
        {%- when '@app' -%}
          {% render block %}
        {%- when 'separator' -%}
          <div class="product-block" {{ block.shopify_attributes }}><hr></div>
        {%- when 'text' -%}
          <div {{ block.shopify_attributes }} class="product-block">
            {{ block.settings.text }}
          </div>
        {%- when 'tab' -%}
          <div class="product-block product-block--tab" {{ block.shopify_attributes }}>
            {% assign tab_id = block.id | append: product.id %}
            {% capture tab_content %}
              {{ block.settings.content }}
              {{ block.settings.page.content }}
            {% endcapture %}
            {%- render 'tab', id: tab_id, title: block.settings.title, content: tab_content -%}
          </div>
        {%- when 'contact' -%}
          <div class="product-block product-block--tab" {{ block.shopify_attributes }}>
            {% assign tab_id = block.id | append: product.id %}
            {%- render 'tab-contact', id: tab_id, block: block -%}
          </div>
        {%- when 'description' -%}
          <div class="product-block{% if block.settings.is_tab %} product-block--tab{% endif %}" {{ block.shopify_attributes }}>
            {%- assign id = block.id | append: product.id -%}
            {%- render 'product-description', id: id, product: product, is_tab: block.settings.is_tab -%}
          </div>
        {%- when 'share' -%}
          <div class="product-block" {{ block.shopify_attributes }}>
            {%- render 'social-sharing', share_title: product.title, share_permalink: product.url, share_image: product -%}
          </div>
        {%- when 'custom' -%}
          <div class="product-block" {{ block.shopify_attributes }}>
            {{ block.settings.code }}
          </div>
      {%- endcase -%}
    {%- endfor -%}
  </div>
{%- endif -%}

{% schema %}
{
  "name": "t:sections.product-full-width.name",
  "class": "product-full-width",
  "settings": [
    {
      "type": "paragraph",
      "content": "t:sections.product-full-width.settings.content"
    },
    {
      "type": "checkbox",
      "id": "max_width",
      "label": "t:sections.product-full-width.settings.max_width.label",
      "info": "t:sections.product-full-width.settings.max_width.info",
      "default": true
    }
  ],
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "description",
      "name": "t:sections.product-full-width.blocks.description.name",
      "limit": 1,
      "settings": [
        {
          "type": "checkbox",
          "id": "is_tab",
          "label": "t:sections.product-full-width.blocks.description.settings.is_tab.label"
        }
      ]
    },
    {
      "type": "text",
      "name": "t:sections.product-full-width.blocks.text.name",
      "settings": [
        {
          "type": "text",
          "id": "text",
          "default": "Text block",
          "label": "t:sections.product-full-width.blocks.text.settings.text.label"
        }
      ]
    },
    {
      "type": "tab",
      "name": "t:sections.product-full-width.blocks.tab.name",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "t:sections.product-full-width.blocks.tab.settings.title.label",
          "default": "Shipping information"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "t:sections.product-full-width.blocks.tab.settings.content.label",
          "default": "<p>Use collapsible tabs for more detailed information that will help customers make a purchasing decision.</p><p>Ex: Shipping and return policies, size guides, and other common questions.</p>"
        },
        {
          "type": "page",
          "id": "page",
          "label": "t:sections.product-full-width.blocks.tab.settings.page.label"
        }
      ]
    },
    {
      "type": "share",
      "name": "t:sections.product-full-width.blocks.share_on_social.name",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "t:sections.product-full-width.blocks.share_on_social.settings.content"
        }
      ]
    },
    {
      "type": "separator",
      "name": "t:sections.product-full-width.blocks.separator.name"
    },
    {
      "type": "contact",
      "name": "t:sections.product-full-width.blocks.contact_form.name",
      "limit": 1,
      "settings": [
        {
          "type": "paragraph",
          "content": "t:sections.product-full-width.blocks.contact_form.settings.content"
        },
        {
          "type": "text",
          "id": "title",
          "label": "t:sections.product-full-width.blocks.contact_form.settings.title.label",
          "default": "Ask a question"
        },
        {
          "type": "checkbox",
          "id": "phone",
          "label": "t:sections.product-full-width.blocks.contact_form.settings.phone.label"
        }
      ]
    },
    {
      "type": "custom",
      "name": "t:sections.product-full-width.blocks.html.name",
      "settings": [
        {
          "type": "liquid",
          "id": "code",
          "label": "t:sections.product-full-width.blocks.html.settings.code.label",
          "default": "<h4>Custom code block</h4><p>Use this advanced section to add custom HTML, app scripts, or liquid.</p>",
          "info": "t:sections.product-full-width.blocks.html.settings.code.info"
        }
      ]
    }
  ]
}
{% endschema %}
