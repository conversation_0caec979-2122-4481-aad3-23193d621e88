{%- liquid
  assign recommend_products = true

  if recommendations.products and recommendations.products_count > 0
    assign related_collection = recommendations
  endif

  for tag in product.tags
    if tag contains '_related'
      assign include_collection_handle = tag | split: '_' | last
      assign include_collection = collections[include_collection_handle]
      if include_collection != empty and include_collection.products_count > 0
        assign related_collection = include_collection
        assign recommend_products = false
        break
      endif
    endif
  endfor

  assign number_of_products = section.settings.related_count
-%}

{%- if section.settings.show_product_recommendations -%}
  <div
    id="Recommendations-{{ section.id }}"
    data-section-id="{{ section.id }}"
    data-section-type="product-recommendations"
    data-enable="{{ recommend_products }}"
    data-product-id="{{ product.id }}"
    data-url="{{ routes.product_recommendations_url }}?section_id={{ section.id }}&product_id={{ product.id }}&limit={{ number_of_products }}"
    data-limit="{{ number_of_products }}">

    <div
      data-section-id="{{ product.id }}"
      data-subsection
      data-section-type="collection-grid"
      class="index-section !tw-my-[40px]">
      <div class="page-width">
        <header class="section-header !tw-mb-[40px] max-md:!tw-mb-[20px]">
          <h3 class="section-header__title tw-text-[40px] max-md:tw-text-[27px] max-md:tw-text-left max-md:tw-font-bold tw-text-darkblack tw-font-semibold tw-leading-[normal] tw-tracking-normal tw-font-dm-sans !tw-capitalize">
            {{ section.settings.product_recommendations_heading }}
          </h3>
        </header>
      </div>

      <div class="page-width page-width--flush-small">
        <div class="grid-overflow-wrapper">
          {%- if recommend_products -%}
            <div class="product-recommendations-placeholder">
              {% comment %}
                This content is visually hidden and replaced when recommended products show up
              {% endcomment %}
              <div class="grid grid--uniform visually-invisible" aria-hidden="true">
                {%- render 'product-grid-item',
                  product: product,
                  quick_shop_enable: settings.quick_shop_enable
                -%}
              </div>
            </div>
          {%- endif -%}
          {%- if related_collection.products_count > 1 -%}
            <div class="product-recommendations">
              <div class="grid grid--uniform !-tw-ml-[30px] max-md:!-tw-ml-[15px]" data-aos="overflow__animation">
                {%- for product in related_collection.products limit: number_of_products -%}
                  {%- render 'product-grid-item',
                    product: product,
                    per_row: number_of_products,
                    quick_shop_enable: settings.quick_shop_enable
                  -%}
                {%- endfor -%}
              </div>
            </div>
          {%- endif -%}
        </div>
      </div>
    </div>
  </div>
{%- endif -%}

{% schema %}
{
  "name": "t:sections.product-recommendations.name",
  "settings": [
    {
      "type": "checkbox",
      "id": "show_product_recommendations",
      "label": "t:sections.product-recommendations.settings.show_product_recommendations.label",
      "info": "t:sections.product-recommendations.settings.show_product_recommendations.info",
      "default": true
    },
    {
      "type": "text",
      "id": "product_recommendations_heading",
      "label": "t:sections.product-recommendations.settings.product_recommendations_heading.label",
      "default": "You may also like"
    },
    {
      "type": "range",
      "id": "related_count",
      "label": "t:sections.product-recommendations.settings.related_count.label",
      "default": 5,
      "min": 2,
      "max": 6,
      "step": 1
    }
  ]
}
{% endschema %}
