<div
  data-section-id="{{ section.id }}"
  data-section-type="slideshow-section"
  {% if section.settings.parallax %}
    data-parallax="true"
  {% endif %}
>
  {%- if section.blocks.size > 0 -%}
    <div class="slideshow-wrapper">
      {%- if section.settings.autoplay and section.settings.style == 'bars' and section.blocks.size > 1 -%}
        {%- style -%}
          [data-bars][data-autoplay="true"] .flickity-page-dots .dot:after {
            animation-duration: {{ section.settings.autoplay_speed | times: 1000 }}ms;
          }
        {%- endstyle -%}

        <button type="button" class="visually-hidden slideshow__pause" data-id="{{ section.id }}" aria-live="polite">
          <span class="slideshow__pause-stop">
            <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-pause" viewBox="0 0 10 13">
              <g fill="#000" fill-rule="evenodd"><path d="M0 0h3v13H0zM7 0h3v13H7z"/></g>
            </svg>
            <span class="icon__fallback-text">{{ 'sections.slideshow.pause_slideshow' | t }}</span>
          </span>
          <span class="slideshow__pause-play">
            <svg
              aria-hidden="true"
              focusable="false"
              role="presentation"
              class="icon icon-play"
              viewBox="18.24 17.35 24.52 28.3"
            >
              <path fill="#323232" d="M22.1 19.151v25.5l20.4-13.489-20.4-12.011z"/>
            </svg>
            <span class="icon__fallback-text">{{ 'sections.slideshow.play_slideshow' | t }}</span>
          </span>
        </button>
      {%- endif -%}

      {%- assign natural_height = false -%}
      {%- assign natural_mobile_height = false -%}
      {%- if section.settings.section_height == 'natural' or section.settings.mobile_height == 'auto' -%}
        {% comment %}
          Get first image's aspect ratio
        {% endcomment %}
        {%- for block in section.blocks limit: 1 -%}
          {%- if block.settings.image != blank -%}
            {%- if section.settings.section_height == 'natural' %}
              {%- assign natural_height = true -%}
              {%- capture natural_height_ratio -%}{{ 100 | divided_by: block.settings.image.aspect_ratio }}%{% endcapture %}
            {%- endif -%}
          {%- endif -%}
          {%- if block.settings.image_mobile != blank -%}
            {%- if section.settings.mobile_height == 'auto' -%}
              {%- assign natural_mobile_height = true -%}
              {%- capture natural_mobile_height_ratio -%}{{ 100 | divided_by: block.settings.image_mobile.aspect_ratio }}%{% endcapture %}
            {%- endif -%}
          {%- endif -%}
        {%- endfor -%}
      {%- endif -%}

      {%- if natural_height -%}
        {%- style -%}
          @media only screen and (min-width: 769px) {
            .hero-natural--{{ section.id }} {
              height: 0;
              padding-bottom: {{ natural_height_ratio }};
            }
          }
        {%- endstyle -%}
      {%- endif -%}
      {%- if natural_mobile_height -%}
        {%- style -%}
          @media screen and (max-width: 768px) {
            .hero-natural-mobile--{{ section.id }} {
              height: 0;
              padding-bottom: {{ natural_mobile_height_ratio }};
            }
          }
        {%- endstyle -%}
      {%- endif -%}

      <div class="{% if natural_height %}hero-natural--{{ section.id }}{% endif %}{% if natural_mobile_height %} hero-natural-mobile--{{ section.id }}{% endif %}">
        <div
          id="Slideshow-{{ section.id }}"
          class="hero hero--{{ section.settings.section_height }} hero--{{ section.id }} hero--mobile--{{ section.settings.mobile_height }} loading loading--delayed"
          {% if natural_height %}
            data-natural="true"
          {% endif %}
          data-mobile-natural="{{ natural_mobile_height }}"
          data-autoplay="{{ section.settings.autoplay }}"
          data-speed="{{ section.settings.autoplay_speed | times: 1000 }}"
          {% if section.settings.style == 'arrows' %}
            data-arrows="true"
          {% endif %}
          {% if section.settings.style == 'dots' %}
            data-dots="true"
          {% endif %}
          {% if section.settings.style == 'bars' %}
            data-dots="true"
            data-bars="true"
          {% endif %}
          data-slide-count="{{ section.blocks.size }}"
        >
          {%- for block in section.blocks -%}
            <div
              {{ block.shopify_attributes }}
              class="slideshow__slide slideshow__slide--{{ block.id }}"
              data-index="{{ forloop.index0 }}"
              data-id="{{ block.id }}"
            >
              {%- style -%}
                .slideshow__slide--{{ block.id }} .hero__title {
                  font-size: {{ block.settings.title_size | times: 0.5 }}px;
                }
                @media only screen and (min-width: 769px) {
                  .slideshow__slide--{{ block.id }} .hero__title {
                    font-size: {{ block.settings.title_size }}px;
                  }
                }

                {%- assign button_alpha = block.settings.color_accent | color_extract: 'alpha' -%}
                {% unless button_alpha == 0.0 %}
                  .slideshow__slide--{{ block.id }} .btn {
                    background: {{ block.settings.color_accent }} !important;
                    border: none;

                    {%- assign accent_brightness = block.settings.color_accent | color_extract: 'lightness' -%}

                    {% if accent_brightness > 40 %}
                      color: #fff !important;
                    {% endif %}
                  }

                  {% if settings.button_style == 'angled' %}
                    .slideshow__slide--{{ block.id }} .btn:before,
                    .slideshow__slide--{{ block.id }} .btn:after {
                      background: {{ block.settings.color_accent }} !important;
                      border: none;
                    }
                  {% endif %}
                {% endunless %}

                {% if block.settings.overlay_opacity > 0 %}
                  .slideshow__slide--{{ block.id }} .hero__image-wrapper:after {
                    content: '';
                    position: absolute;
                    left: 0;
                    right: 0;
                    top: 0;
                    bottom: 0;
                    z-index: 3;
                    background-color: #000;
                    opacity: {{ block.settings.overlay_opacity | divided_by: 100.0 }};
                  }
                {% endif %}
              {%- endstyle -%}

              {%- liquid
                assign hero_text = false
                assign link_slide = false
                if block.settings.top_subheading != blank or block.settings.title != blank or block.settings.subheading != blank or block.settings.link_text != blank
                  assign hero_text = true
                endif
                if block.settings.link != blank and block.settings.link_2 == blank
                  assign link_slide = true
                endif

                assign has_mobile_image = false
                if block.settings.image_mobile != blank
                  assign has_mobile_image = true
                endif
              -%}

              <div class="hero__image-wrapper{% unless hero_text %} hero__image-wrapper--no-overlay{% endunless %}">
                {%- if section.settings.parallax -%}
                  <parallax-image class="parallax-container">
                  <div
                    class="parallax-image"
                    data-movement="15%"
                    data-parallax-image
                    data-angle="{{ block.settings.parallax_direction }}"
                  >
                {%- endif -%}

                {%- if block.settings.image != blank -%}
                  {% style %}
                    .hero__image--{{ block.id }}{
                      object-position: {{ block.settings.focal_point }};
                    }
                  {% endstyle %}

                  {% if has_mobile_image %}
                    {% assign desktop_classes = 'small--hide lazyloaded hero__image hero__image--' | append: block.id %}
                  {% else %}
                    {% assign desktop_classes = 'hero__image lazyloaded hero__image--' | append: block.id %}
                  {% endif %}

                  {% render 'image-element',
                    img: block.settings.image,
                    img_width: 2400,
                    classes: desktop_classes,
                    alt: block.settings.image.alt,
                    sizes: '100%'
                  %}

                  {%- if has_mobile_image -%}
                    {% assign mobile_classes = 'medium-up--hide lazyloaded hero__image hero__image--'
                      | append: block.id
                    %}

                    {% render 'image-element',
                      img: block.settings.image_mobile,
                      img_height: 1200,
                      classes: mobile_classes,
                      alt: block.settings.image_mobile.alt,
                      sizes: '100%'
                    %}
                  {%- endif -%}

                {%- else -%}
                  {%- if template == 'password' -%}
                    {%- assign password_image = 'password-page-background.jpg' | asset_url -%}

                    {% render 'image-element',
                      img: password_image,
                      img_height: 2400,
                      classes: 'hero__image',
                      sizes: '100%'
                    %}
                  {%- else -%}
                    {{ 'lifestyle-1' | placeholder_svg_tag: 'placeholder-svg' }}
                  {%- endif -%}
                {%- endif -%}

                {%- if section.settings.parallax -%}
                  </div>
                  </parallax-image>
                {%- endif -%}
              </div>

              {%- if link_slide -%}
                <a href="{{ block.settings.link }}" class="hero__slide-link" aria-hidden="true"></a>
              {%- endif -%}

              {%- if hero_text -%}
                <div class="hero__text-wrap">
                  <div class="page-width">
                    <div class="hero__text-content {{ block.settings.text_align }}">
                      <div class="hero__text-shadow">
                        {%- unless block.settings.top_subheading == blank -%}
                          <div class="hero__top-subtitle">
                            <div class="animation-cropper">
                              <div class="animation-contents">
                                {{ block.settings.top_subheading | escape }}
                              </div>
                            </div>
                          </div>
                        {%- endunless -%}
                        {%- unless block.settings.title == blank -%}
                          <h2 class="h1 hero__title">
                            <div class="animation-cropper">
                              <div class="animation-contents">
                                {{ block.settings.title | newline_to_br }}
                              </div>
                            </div>
                          </h2>
                        {%- endunless -%}
                        {%- if block.settings.subheading or block.settings.link or block.settings.link_2 -%}
                          {%- unless block.settings.subheading == blank -%}
                            <div class="hero__subtitle">
                              <div class="animation-cropper">
                                <div class="animation-contents">
                                  {{ block.settings.subheading | escape }}
                                </div>
                              </div>
                            </div>
                          {%- endunless -%}
                          {%- if block.settings.link_text != blank or block.settings.link_text_2 != blank -%}
                            <div class="hero__link">
                              {%- if block.settings.link_text != blank -%}
                                <a
                                  href="{{ block.settings.link }}"
                                  class="btn{% if block.settings.color_accent contains 'rgba(0,0,0,0)' %} btn--inverse{% endif %}"
                                >
                                  {{ block.settings.link_text }}
                                </a>
                              {%- endif -%}
                              {%- if block.settings.link_text_2 != blank -%}
                                <a
                                  href="{{ block.settings.link_2 }}"
                                  class="btn{% if block.settings.color_accent contains 'rgba(0,0,0,0)' %} btn--inverse{% endif %}"
                                >
                                  {{ block.settings.link_text_2 }}
                                </a>
                              {%- endif -%}
                            </div>
                          {%- endif -%}
                        {%- endif -%}
                      </div>
                    </div>
                  </div>
                </div>
              {%- endif -%}
            </div>
          {%- endfor -%}
        </div>
      </div>
    </div>
  {%- endif -%}

  {%- if section.blocks.size == 0 -%}
    <div class="placeholder-noblocks">
      {{ 'home_page.onboarding.no_content' | t }}
    </div>
  {%- endif -%}
</div>

{% schema %}
{
  "name": "t:sections.slideshow.name",
  "class": "index-section--hero",
  "max_blocks": 5,
  "settings": [
    {
      "type": "select",
      "id": "section_height",
      "label": "t:sections.slideshow.settings.section_height.label",
      "default": "650px",
      "options": [
        {
          "label": "t:sections.slideshow.settings.section_height.options.natural.label",
          "value": "natural"
        },
        {
          "label": "t:sections.slideshow.settings.section_height.options.450px.label",
          "value": "450px"
        },
        {
          "label": "t:sections.slideshow.settings.section_height.options.550px.label",
          "value": "550px"
        },
        {
          "label": "t:sections.slideshow.settings.section_height.options.650px.label",
          "value": "650px"
        },
        {
          "label": "t:sections.slideshow.settings.section_height.options.750px.label",
          "value": "750px"
        },
        {
          "label": "t:sections.slideshow.settings.section_height.options.100vh.label",
          "value": "100vh"
        }
      ]
    },
    {
      "type": "select",
      "id": "mobile_height",
      "label": "t:sections.slideshow.settings.mobile_height.label",
      "default": "auto",
      "options": [
        {
          "label": "t:sections.slideshow.settings.mobile_height.options.auto.label",
          "value": "auto"
        },
        {
          "label": "t:sections.slideshow.settings.mobile_height.options.250px.label",
          "value": "250px"
        },
        {
          "label": "t:sections.slideshow.settings.mobile_height.options.300px.label",
          "value": "300px"
        },
        {
          "label": "t:sections.slideshow.settings.mobile_height.options.400px.label",
          "value": "400px"
        },
        {
          "label": "t:sections.slideshow.settings.mobile_height.options.500px.label",
          "value": "500px"
        },
        {
          "label": "t:sections.slideshow.settings.mobile_height.options.100vh.label",
          "value": "100vh"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "parallax",
      "label": "t:sections.slideshow.settings.parallax.label",
      "default": true
    },
    {
      "type": "select",
      "id": "style",
      "label": "t:sections.slideshow.settings.style.label",
      "default": "bars",
      "options": [
        {
          "value": "minimal",
          "label": "t:sections.slideshow.settings.style.options.minimal.label"
        },
        {
          "value": "arrows",
          "label": "t:sections.slideshow.settings.style.options.arrows.label"
        },
        {
          "value": "bars",
          "label": "t:sections.slideshow.settings.style.options.bars.label"
        },
        {
          "value": "dots",
          "label": "t:sections.slideshow.settings.style.options.dots.label"
        }
      ]
    },
    {
      "type": "checkbox",
      "id": "autoplay",
      "label": "t:sections.slideshow.settings.autoplay.label",
      "default": true
    },
    {
      "type": "range",
      "id": "autoplay_speed",
      "label": "t:sections.slideshow.settings.autoplay_speed.label",
      "default": 7,
      "min": 5,
      "max": 10,
      "step": 1,
      "unit": "s"
    }
  ],
  "blocks": [
    {
      "type": "image",
      "name": "t:sections.slideshow.blocks.slide.name",
      "settings": [
        {
          "type": "text",
          "id": "top_subheading",
          "label": "t:sections.slideshow.blocks.slide.settings.top_subheading.label"
        },
        {
          "type": "textarea",
          "id": "title",
          "label": "t:sections.slideshow.blocks.slide.settings.title.label",
          "default": "Two line\ntitle slide."
        },
        {
          "type": "range",
          "id": "title_size",
          "label": "t:sections.slideshow.blocks.slide.settings.title_size.label",
          "default": 80,
          "min": 40,
          "max": 100,
          "unit": "px"
        },
        {
          "type": "text",
          "id": "subheading",
          "label": "t:sections.slideshow.blocks.slide.settings.subheading.label",
          "default": "And optional subtext"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.slideshow.blocks.slide.settings.link.label"
        },
        {
          "type": "text",
          "id": "link_text",
          "label": "t:sections.slideshow.blocks.slide.settings.link_text.label",
          "default": "Optional button"
        },
        {
          "type": "url",
          "id": "link_2",
          "label": "t:sections.slideshow.blocks.slide.settings.link_2.label"
        },
        {
          "type": "text",
          "id": "link_text_2",
          "label": "t:sections.slideshow.blocks.slide.settings.link_text_2.label"
        },
        {
          "type": "color",
          "id": "color_accent",
          "label": "t:sections.slideshow.blocks.slide.settings.color_accent.label",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "select",
          "id": "text_align",
          "label": "t:sections.slideshow.blocks.slide.settings.text_align.label",
          "default": "vertical-center horizontal-center",
          "options": [
            {
              "value": "vertical-center horizontal-left",
              "label": "t:sections.slideshow.blocks.slide.settings.text_align.options.vertical-center_horizontal-left.label"
            },
            {
              "value": "vertical-center horizontal-center",
              "label": "t:sections.slideshow.blocks.slide.settings.text_align.options.vertical-center_horizontal-center.label"
            },
            {
              "value": "vertical-center horizontal-right",
              "label": "t:sections.slideshow.blocks.slide.settings.text_align.options.vertical-center_horizontal-right.label"
            },
            {
              "value": "vertical-bottom horizontal-left",
              "label": "t:sections.slideshow.blocks.slide.settings.text_align.options.vertical-bottom_horizontal-left.label"
            },
            {
              "value": "vertical-bottom horizontal-center",
              "label": "t:sections.slideshow.blocks.slide.settings.text_align.options.vertical-bottom_horizontal-center.label"
            },
            {
              "value": "vertical-bottom horizontal-right",
              "label": "t:sections.slideshow.blocks.slide.settings.text_align.options.vertical-bottom_horizontal-right.label"
            }
          ]
        },
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.slideshow.blocks.slide.settings.image.label"
        },
        {
          "type": "image_picker",
          "id": "image_mobile",
          "label": "t:sections.slideshow.blocks.slide.settings.image_mobile.label"
        },
        {
          "type": "select",
          "id": "parallax_direction",
          "label": "t:sections.background-image-text.settings.parallax_direction.label",
          "info": "Only applied when parallax is enabled in this section.",
          "default" : "top",
          "options": [
            {
              "value": "top",
              "label": "t:sections.background-image-text.settings.parallax_direction.options.top.label"
            },
            {
              "value": "left",
              "label": "t:sections.background-image-text.settings.parallax_direction.options.left.label"
            }
          ]
        },
        {
          "type": "range",
          "id": "overlay_opacity",
          "label": "t:sections.slideshow.blocks.slide.settings.overlay_opacity.label",
          "info": "t:sections.slideshow.blocks.slide.settings.overlay_opacity.info",
          "default": 0,
          "min": 0,
          "max": 100,
          "step": 2,
          "unit": "%"
        },
        {
          "type": "select",
          "id": "focal_point",
          "label": "t:sections.slideshow.blocks.slide.settings.focal_point.label",
          "info": "t:sections.slideshow.blocks.slide.settings.focal_point.info",
          "default": "center center",
          "options": [
            {
              "value": "20% 0",
              "label": "t:sections.slideshow.blocks.slide.settings.focal_point.options.20_0.label"
            },
            {
              "value": "top center",
              "label": "t:sections.slideshow.blocks.slide.settings.focal_point.options.top_center.label"
            },
            {
              "value": "80% 0",
              "label": "t:sections.slideshow.blocks.slide.settings.focal_point.options.80_0.label"
            },
            {
              "value": "20% 50%",
              "label": "t:sections.slideshow.blocks.slide.settings.focal_point.options.20_50.label"
            },
            {
              "value": "center center",
              "label": "t:sections.slideshow.blocks.slide.settings.focal_point.options.center_center.label"
            },
            {
              "value": "80% 50%",
              "label": "t:sections.slideshow.blocks.slide.settings.focal_point.options.80_50.label"
            },
            {
              "value": "20% 100%",
              "label": "t:sections.slideshow.blocks.slide.settings.focal_point.options.20_100.label"
            },
            {
              "value": "bottom center",
              "label": "t:sections.slideshow.blocks.slide.settings.focal_point.options.bottom_center.label"
            },
            {
              "value": "80% 100%",
              "label": "t:sections.slideshow.blocks.slide.settings.focal_point.options.80_100.label"
            }
          ]
        }
      ]
    }
  ],
  "presets": [
    {
      "name": "t:sections.slideshow.presets.hero_optional_slideshow.name",
      "settings": {
        "autoplay": true,
        "autoplay_speed": 5
      },
      "blocks": [
        {
          "type": "image",
          "settings": {
            "title": "Endless\npossibilities.",
            "subheading": "Bring your brand to life"
          }
        },
        {
          "type": "image",
          "settings": {
            "title": "Two line\ntitle slide.",
            "subheading": "And big, beautiful imagery"
          }
        }
      ]
    }
  ]
}
{% endschema %}
