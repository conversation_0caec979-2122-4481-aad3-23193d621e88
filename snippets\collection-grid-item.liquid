{%- liquid
  unless grid_item_width
    assign grid_item_width = 'small--one-half medium-up--one-third'
  endunless
 
  unless background_position
    assign background_position = 'center center'
  endunless

  if block.settings.title != blank
    assign title_output = block.settings.title
  else
    if collection.empty?
      assign title_output = 'home_page.onboarding.collection_title' | t
    elsif collection_title
      assign title_output = collection_title
    else
      assign title_output = collection.title | escape
    endif
  endif
-%}

<div class="grid__item {{ grid_item_width }}" {{ block.shopify_attributes }}>
  <a href="{{ collection.url }}" class="collection-item collection-item--{{ settings.collection_grid_style }}" data-aos="row-of-{{ per_row }}">

    {%- liquid
      if settings.collection_grid_image == 'collection' and collection.image
        assign collection_image = collection.image
      else
        assign collection_image = collection.products.first.featured_media.preview_image
      endif
    -%}

    {%- if collection.empty? -%}
      {%- capture placeholder -%}collection-{% cycle 1, 2, 3, 4, 5, 6 %}{%- endcapture -%}
      <div class="collection-image collection-image--{{ settings.collection_grid_shape }} collection-image--placeholder">{{ placeholder | placeholder_svg_tag: 'placeholder-svg' }}</div>
    {%- else -%}
      <div class="collection-image collection-image--{{ settings.collection_grid_shape }} image-wrap">
        {%- assign img_url = collection_image | img_url: '1x1' | replace: '_1x1.', '_{width}x.' -%}
        <img
          class="lazyload"
          src=""
          data-src="{{ img_url }}"
          data-aspectratio="{{ collection_image.aspect_ratio }}"
          data-widths="[360, 540, 720, 900, 1080]"
          data-sizes="auto"
          alt="{{ collection_image.alt | escape }}"
          style="object-position: {{ background_position }};">

        <noscript>
          <img class="lazyloaded"
            src="{{ collection_image | img_url: '400x' }}"
            alt="{{ collection_image.alt | escape }}">
        </noscript>
      </div>
    {%- endif -%}

    <span
      class="collection-item__title collection-item__title--{{ settings.collection_grid_style }} collection-item__title--{{ settings.type_collection_font }} collection-item__title--{{ settings.collection_grid_text_align }}">
      <span>
        {{ title_output }}
      </span>
    </span>

  </a>
</div>
