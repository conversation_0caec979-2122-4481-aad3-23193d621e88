<div class="footer__item-padding">
  {%- if block.settings.show_footer_title -%}
    <p class="h4 footer__title small--hide">{{ block.settings.title }}</p>
    <button type="button" class="h4 footer__title collapsible-trigger collapsible-trigger-btn medium-up--hide" aria-controls="Footer-{{ block.id }}">
      {{ block.settings.title }}
      {%- render 'collapsible-icons' -%}
    </button> 
  {%- endif -%}
  <div
    {% if block.settings.show_footer_title %}
      id="Footer-{{ block.id }}" class="collapsible-content collapsible-content--small"
    {% endif %}>
    <div class="collapsible-content__inner">
      <div class="footer__collapsible{% unless block.settings.show_footer_title %} footer_collapsible--disabled{% endunless %}">
        {%- if block.settings.image != blank -%}
          <div class="image-wrap text-spacing" style="height: 0; padding-bottom: {{ 100 | divided_by: block.settings.image.aspect_ratio }}%;">
            {%- assign img_url = block.settings.image | img_url: '1x1' | replace: '_1x1.', '_{width}x.' -%}
            <img class="lazyload"
                data-src="{{ img_url }}"
                data-widths="[360, 540, 720, 900, 1080, 1600]"
                data-aspectratio="{{ block.settings.image.aspect_ratio }}"
                data-sizes="auto"
                role="presentation"
                alt="{{ block.settings.image.alt }}">
            <noscript>
              <img class="lazyloaded" src="{{ block.settings.image | img_url: '400x' }}" alt="{{ block.settings.image.alt }}">
            </noscript>
          </div>
        {%- endif -%}

        {{ block.settings.text }}
      </div>
    </div>
  </div>
</div>
