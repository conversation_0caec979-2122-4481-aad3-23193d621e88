                        {% comment %} fix cls {% endcomment %} 
                                <style>.async-hide { opacity: 0 !important} </style>
                                <script>(function(a,s,y,n,c,h,i,d,e){s.className+=' '+y;h.start=1*new Date;
                                h.end=i=function(){s.className=s.className.replace(RegExp(' ?'+y),'')};
                                (a[n]=a[n]||[]).hide=h;setTimeout(function(){i();h.end=null},c);h.timeout=c;
                                })(window,document.documentElement,'async-hide','dataLayer',300,
                                {'GTM-XXXXXX':true});</script>

<link rel="preload" href="https://quickstart-41d588e3.myshopify.com/cdn/shop/t/3/assets/lazysizes-1.0.72.js" as="script">
<script src="https://quickstart-41d588e3.myshopify.com/cdn/shop/t/3/assets/lazysizes-1.0.72.js"></script>