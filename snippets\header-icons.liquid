<div class="site-nav">
    <div class="site-nav__icons">
      <span class="ly-custom-769"></span>
    {%- if shop.customer_accounts_enabled -%}
      <a class="site-nav__link site-nav__link--icon small--hide" href="{{ routes.account_url }}">
        <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-user" viewBox="0 0 64 64"><path d="M35 39.84v-2.53c3.3-1.91 6-6.66 6-11.41 0-7.63 0-13.82-9-13.82s-9 6.19-9 13.82c0 4.75 2.7 9.51 6 11.41v2.53c-10.18.85-18 6-18 12.16h42c0-6.19-7.82-11.31-18-12.16z"/></svg>
        <span class="icon__fallback-text">
          {%- if customer -%}
            {{ 'layout.customer.account' | t }}
          {%- else -%}
            {{ 'layout.customer.log_in' | t }}
          {%- endif -%}
        </span>
      </a>
    {%- endif -%}
    {%- if settings.search_enable -%}
      <a href="{{ routes.search_url }}" class="site-nav__link site-nav__link--icon js-search-header{% if section.settings.main_menu_alignment == 'center' or section.settings.main_menu_alignment == 'center-split' %} medium-up--hide{% endif %}">
        <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-search" viewBox="0 0 64 64"><path d="M47.16 28.58A18.58 18.58 0 1 1 28.58 10a18.58 18.58 0 0 1 18.58 18.58zM54 54L41.94 42"/></svg>
        <span class="icon__fallback-text">{{ 'general.search.title' | t }}</span>
      </a>
    {%- endif -%}

    {%- if section.settings.main_menu_alignment == 'left' or section.settings.main_menu_alignment == 'left-center' or section.settings.main_menu_alignment == 'left-drawer' -%}
      <button
        type="button"
        class="desktop_nav site-nav__link site-nav__link--icon js-drawer-open-nav{% if section.settings.main_menu_alignment == 'left' or section.settings.main_menu_alignment == 'left-center' %} medium-up--hide{% endif %}"
        aria-controls="NavDrawer">
        <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-hamburger" viewBox="0 0 64 64"><path d="M7 15h51M7 32h43M7 49h51"/></svg>
        <span class="icon__fallback-text">{{ 'general.drawers.navigation' | t }}</span>
      </button>
    {%- endif -%}
    {%- if section.settings.main_menu_alignment_mob == 'left' or section.settings.main_menu_alignment_mob == 'left-center' or section.settings.main_menu_alignment_mob == 'left-drawer' -%}
      <button
        type="button"
        class="mobile_nav site-nav__link site-nav__link--icon js-drawer-open-nav{% if section.settings.main_menu_alignment_mob == 'left' or section.settings.main_menu_alignment_mob == 'left-center' %} medium-up--hide{% endif %}"
        aria-controls="NavDrawer">
        <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-hamburger" viewBox="0 0 64 64"><path d="M7 15h51M7 32h43M7 49h51"/></svg>
        <span class="icon__fallback-text">{{ 'general.drawers.navigation' | t }}</span>
      </button>
    {%- endif -%}
    <a href="{{ routes.cart_url }}" class="site-nav__link site-nav__link--icon js-drawer-open-cart" aria-controls="CartDrawer" data-icon="{{ settings.cart_icon }}">
      <span class="cart-link">
        {%- if settings.cart_icon == 'cart' -%}
          <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-cart" viewBox="0 0 64 64"><path fill="none" d="M14 17.44h46.79l-7.94 25.61H20.96l-9.65-35.1H3"/><circle cx="27" cy="53" r="2"/><circle cx="47" cy="53" r="2"/></svg>
        {%- elsif settings.cart_icon == 'bag-minimal' -%}
          <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-bag-minimal" viewBox="0 0 64 64"><path stroke="null" id="svg_4" fill-opacity="null" stroke-opacity="null" fill="null" d="M11.375 17.863h41.25v36.75h-41.25z"/><path stroke="null" id="svg_2" d="M22.25 18c0-7.105 4.35-9 9.75-9s9.75 1.895 9.75 9"/></svg>
        {%- else -%}
          <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-bag" viewBox="0 0 64 64"><g fill="none" stroke="#000" stroke-width="2"><path d="M25 26c0-15.79 3.57-20 8-20s8 4.21 8 20"/><path d="M14.74 18h36.51l3.59 36.73h-43.7z"/></g></svg>
        {%- endif -%}
        <span class="icon__fallback-text">{{ 'layout.cart.title' | t }}</span>
        <span class="cart-link__bubble{% if cart.item_count > 0 %} cart-link__bubble--visible{% endif %}"></span>
      </span>
    </a>
  </div>
</div>
