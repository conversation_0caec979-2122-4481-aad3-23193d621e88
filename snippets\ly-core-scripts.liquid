{%- comment -%}Version 4.0.6{%- endcomment -%}
{%- comment -%}
  !!! IMPORTANT !!!
  This file has been automatically created by Lang<PERSON>.
  Please don't edit this file, as all changes will be overwritten.
{%- endcomment -%}

{%- if shop.url contains 'https://' %}{% assign used_protocol = 'https://' %}{% else %}{% assign used_protocol = 'http://' %}{% endif -%}
{%- assign supported_4_letter_codes = 'zh-TW,zh-CN,pt-PT,pt-BR' | split: ',' -%}
{%- if current_language == nil -%}
  {%- assign current_language_num = 0 -%}
  {%- for language in shop.metafields.language_codes -%}
    {%- assign used_request_locale_iso_code = request.locale.iso_code -%}
    {%- if language.last == used_request_locale_iso_code  -%}
      {%- assign language_id_num = language.first | replace: "ly", "" | plus: 0 %}
      {%- if current_language_num < language_id_num  -%}
        {%- assign current_language = language.first -%}
      {%- assign current_language_num = language_id_num -%}
      {%- endif -%}
    {%- endif -%}
  {%- endfor -%}
{%- endif -%}
{%- assign key = 'ly_theme_settings_' | append: theme.id -%}
{%- if shop.metafields.global[key].type -%}{%- assign themeSettings = shop.metafields.global[key].value -%}{%- else -%}{%- assign themeSettings = shop.metafields.global[key] -%}{%- endif -%}
{%- assign key = 'ly_switcher_data_' | append: theme.id -%}
{%- if shop.metafields.global[key].type -%}{%- assign switcherSettings = shop.metafields.global[key].value -%}{%- else -%}{%- assign switcherSettings = shop.metafields.global[key] -%}{%- endif -%}
{%- assign languages = localization.available_languages -%}
{%- if shop.metafields.langify_v2.cross_domain_links and switcherSettings.languages.size > 1 -%}
  {%- assign languages = switcherSettings.languages -%} 
{%- endif -%}
{%- assign countries = localization.available_countries -%}
{%- assign languageDetectionDefault = switcherSettings.languageDetectionDefault %}


<script>
  var langify = langify || {};


  /**
  * 
  * 
  * @class LyHelper
  */
  class LyHelper {
    constructor() {
      return {
        ajax: function(params) { return LyHelper.ajax(params); },
        isIE: function() { return LyHelper.isIE(); },
        extractImageObject: function(val) { return LyHelper.extractImageObject(val); },
        getCurrentLanguage: function() { return LyHelper.getCurrentLanguage(); },
        getCookie: function(name) { return LyHelper.getCookie(name); },
        setCookie: function(name, value, days) { return LyHelper.setCookie(name, value, days); },
        getVersion: function() { return LyHelper.getVersion(); },
        isOutOfViewport: function(elem) { return LyHelper.isOutOfViewport(elem); },
        isDomainFeatureEnabled: function() { return LyHelper.isDomainFeatureEnabled(); },
        getVal: function(str) { return LyHelper.getVal(str); },
        inIframe: function() { return LyHelper.inIframe(); },
        shopifyAPI: function() { return LyHelper.shopifyAPI(); },
        loadScript: function(url, callback) { return LyHelper.loadScript(url, callback); },
        localizationRedirect: function(type, code, additionalFields, additionalParams, domain) { return LyHelper.localizationRedirect(type, code, additionalFields, additionalParams, domain); },
        changeCurrency: function(code) { return LyHelper.changeCurrency(code); },
        log: function(title, data, type) { return LyHelper.log(title, data, type); },
      }
    }
    static ajax(params) {
      var data = params.data;
      fetch(params.url, {
        method: params.method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: (data && params.method === 'POST') ? JSON.stringify(data) : null,
      })
      .then(response => response.json())
      .then(data => {
        params.success(data);
      })
      .catch((error) => {
        console.error('Error:', error);
      });
    }
    static isIE() {
      var ua = navigator.userAgent;
      var is_ie = ua.indexOf("MSIE ") > -1 || ua.indexOf("Trident/") > -1;
      return is_ie;
    }
    static extractImageObject(val) {
      if(!val || val == '') return false;
      var val = val;

      // Handle src-sets
      if(val.search(/([0-9]+w?h?x?,)/gi) > -1) {
        val = val.split(/([0-9]+w?h?x?,)/gi)[0];
      }

      var hostBegin = val.indexOf('//') ? val.indexOf('//') : 0;
      var hostEnd = val.lastIndexOf('/') + 1;
      var host = val.substring(hostBegin, hostEnd);      
      var afterHost = val.substring(hostEnd, val.length);

      var url = val;
      var file = ''; 
      var name = ''; 
      var type = '';
      afterHost = afterHost.substring(0, (afterHost.indexOf("#") == -1) ? afterHost.length : afterHost.indexOf("#"));
      afterHost = afterHost.substring(0, (afterHost.indexOf("?") == -1) ? afterHost.length : afterHost.indexOf("?"));
      name = afterHost.replace(/(_[0-9]+x[0-9]*|_{width}x|_{size})?(_crop_(top|center|bottom|left|right))?(@[0-9]*x)?(\.progressive)?\.(png\.jpg|jpe?g|png|gif|webp)/gi, "");

      if(afterHost.search(/(\.png\.jpg|\.jpg\.jpg)/gi) > -1) {
        type = afterHost.substring(afterHost.search(/(\.png\.jpg|\.jpg\.jpg)/gi)+1, afterHost.length);
      } else {
        type = afterHost.substring(afterHost.lastIndexOf('.')+1, afterHost.length);
      }
      
      file = afterHost.replace(/(_[0-9]+x[0-9]*|_{width}x|_{size})?(_crop_(top|center|bottom|left|right))?(@[0-9]*x)?(\.progressive)?\.(png\.jpg|jpe?g|png|gif|webp)/gi, '.'+type);

      return {
        host: host,
        name: name,
        type: type,
        file: file
      }
    }
    static getCurrentLanguage() {
      return window.langify.locale.iso_code;
    }
    static setLanguage(code) {
      
    }
    static getCookie(name) {
      var v = document.cookie.match('(^|;) ?' + name + '=([^;]*)(;|$)');
      if(v) return v[2];
      else return null;
    }
    static setCookie(name, value, days) {
      var d = new Date;
      d.setTime(d.getTime() + 24*60*60*1000*days);
      document.cookie = name + "=" + value + ";path=/;expires=" + d.toGMTString();
    }
    static getVersion() {
      return 2;
    }
    static isOutOfViewport (elem) {
      var bounding = elem.getBoundingClientRect();
      var out = {};
      out.top = bounding.top < 0;
      out.left = bounding.left < 0;
      out.bottom = Math.ceil(bounding.bottom) >= (window.innerHeight || document.documentElement.clientHeight);
      out.right = bounding.right > (window.innerWidth || document.documentElement.clientWidth);
      out.any = out.top || out.left || out.bottom || out.right;
      out.inViewport = bounding.x > 0 && bounding.y > 0;
      return out;
    }
    static isDomainFeatureEnabled() {
      return window.langify.locale.domain_feature_enabled;
    }
    static getVal(str) {
      var v = window.location.search.match(new RegExp('(?:[?&]'+str+'=)([^&]+)'));
      return v ? v[1] : null;
    }
    static inIframe() {
      try {
        return window.self !== window.top;
      } catch (e) {
        return true;
      }
    }
    static shopifyAPI() {
      var root_url = window.langify.locale.root_url != '/' ? window.langify.locale.root_url : '';

      return {
        attributeToString: function(attribute) {
          if((typeof attribute) !== 'string') {
            attribute += '';
            if(attribute === 'undefined') {attribute = '';}
          }
          return attribute.trim();
        },
        getCart: function(callback) {
          LyHelper.ajax({
            method: 'GET',
            url: root_url+'/cart.js',
            success: function (cart, textStatus) {
              if((typeof callback) === 'function') {
                callback(cart);
              }
            }
          });
        },
        updateCartNote: function(note, callback) {
          var params = {
            method: 'POST',
            url: root_url+'/cart/update.js',
            data: 'note=' + this.attributeToString(note),
            dataType: 'json',
            success: function(cart) {if((typeof callback) === 'function') {callback(cart);}},
            error: this.onError
          };
          LyHelper.ajax(params);
        },
        updateCartAttributes: function(attributes, callback) {
          var params = {
            method: 'POST',
            url: root_url+'/cart/update.js',
            data: {"attributes": attributes},
            dataType: 'json',
            success: function(cart) {
              if((typeof callback) === 'function') {
                callback(cart);
              }
            },
            error: this.onError
          };
          LyHelper.ajax(params);
        },
        onError: function(XMLHttpRequest, textStatus) {

        }      
      }
    }
    static loadScript(url, callback) {
      var script = document.createElement('script');
      script.type = 'text/javascript';
      if(script.readyState) {
        script.onreadystatechange = function () {
          if(script.readyState == 'loaded' || script.readyState == 'complete') {
            script.onreadystatechange = null;
            callback();
          }
        };
      } else {
        script.onload = function () {
          callback();
        };
      }
      script.src = url;
      document.getElementsByTagName('head')[0].appendChild(script);
    }
    static localizationRedirect(type, code, additionalFields, additionalParams, domain) {
      if(type !== 'country_code' && type !== 'language_code') { 
        return false; 
      }
      if(!additionalParams) {
        var additionalParams = '';
      }
      if(domain) {
        window.location.href = domain;
        return true;
      }
      var params = [
        {
          name: type,
          value: code
        },
        {
          name: 'return_to',
          value: window.location.pathname + window.location.search + additionalParams + window.location.hash
        },
        {
          name: 'form_type',
          value: 'localization'
        },
        {
          name: '_method',
          value: 'put'
        }
      ];
      if(additionalFields) {
        for(var i=0; i<additionalFields.length; i++) {
          params.push(additionalFields[i]);
        }
      }
      const form = document.createElement('form');
      form.method = 'POST';
      form.action = '/localization';
      params.forEach(function(param) {
        const field = document.createElement('input');
        field.type = 'hidden';
        field.name = param['name'];
        field.value = param['value'];
        form.appendChild(field);
      });
      document.body.appendChild(form);
      form.submit();
    }
    static changeCurrency(code) {
      const params = [
        {
          name: 'currency_code',
          value: code
        },
        {
          name: 'return_to',
          value: window.location.pathname + window.location.search + window.location.hash
        },
        {
          name: 'form_type',
          value: 'localization'
        },
        {
          name: '_method',
          value: 'put'
        }
      ];
      const form = document.createElement('form');
      form.method = 'POST';
      form.action = '/localization';
      params.forEach(function(param) {
        const field = document.createElement('input');
        field.type = 'hidden';
        field.name = param['name'];
        field.value = param['value'];
        form.appendChild(field);
      });
      document.body.appendChild(form);
      form.submit();
    }
    static log(title, data, type) {
      if(langify.settings.debug) {
        if(!data) {
          var data = '';
        }
        
        var css = 'color: green; font-weight: bold;';
        if(type === 'error') {
          css = 'color: red; font-weight: bold;';
        }

        console.log(`%c ${title}: \n`, css, data);
      }
    }
  };


  /**
  *
  *
  * @class TranslationObserver
  */
  class TranslationObserver {
    constructor() {
      LyHelper.log('TranslationObserver instantiation')
    }

    init() {
      if(langify.settings.observe && !LyHelper.isIE()) {
        var langifyObserver = this.langifyObserverCore();
        langifyObserver.init();
        
        // HOTFIX: translation observer sometimes refuses to trigger on page load_
        langifyObserver.triggerCustomContents();

        langify.api = {
          observer: {
            start: langifyObserver.init,
            stop: langifyObserver.stopObserver,
            trigger: langifyObserver.triggerCustomContents
          }
        };
      }

      return this;
    }

    langifyObserverCore() {
      var observedNodes = [];
      var intersectionObs = null;
      var mutationCount = 0;
      var mutationObs = null;
      var mutationObsConfig = {
        characterData: true,
        characterDataOldValue: true,
        attributes: true,
        attributeOldValue: true,
        childList: true,
        subtree: true,
      };
      var customContents_html = {};
      var customContents_text = {};
      var customContents_attr = {};
    
      function init() {
        if(langify.settings.observe) {
          spreadCustomContents();
          if (window.MutationObserver) {
            if (langify.settings.lazyload && window.IntersectionObserver) {
              startIntersectionObserver();
            } else {
              startMutationObserver(null);
            }
          } else {
            startMutationEvents();
          }
        }
      }
    
    
      // Intersection Observer
      function startIntersectionObserver() {
        intersectionObs = new IntersectionObserver(callbackIntersectionChange);
        var elementNodes = document.getElementsByClassName('shopify-section');
        Array.prototype.slice.call(elementNodes).forEach(function(node) {
          intersectionObs.observe(node);
        });
      }
    
      function callbackIntersectionChange(intersections) {
        intersections.forEach(function(intersection) {
          var target = intersection.target;
          var targetIsIntersecting = Boolean(target.getAttribute('ly-is-intersecting') === "true" ? true : false);
    
          if(typeof targetIsIntersecting == 'undefined' || targetIsIntersecting == null) {
            target.setAttribute('ly-is-intersecting', intersection.isIntersecting);
            targetIsIntersecting = intersection.isIntersecting;
          }
          
          // On Screen
          if(targetIsIntersecting == false && intersection.isIntersecting == true) {
            if(!target.getAttribute('ly-is-observing')) {
              startMutationObserver(target);
              intersectionObs.unobserve(target);
    
              // As the mutation observer didn't see the "add" of the elememts, trigger the initial translation manually
              var elementNodes = getNodesUnder(target, 'SHOW_ELEMENT');
              var textNodes = getNodesUnder(target, 'SHOW_TEXT');
              var nodes = elementNodes.concat(textNodes);
              nodes.forEach(function(node) {
                translate(node, 'mutation -> childList');
              });
            }
          } 
          // Off Screen
          //else if(targetIsIntersecting == true && intersection.isIntersecting == false) {
          //  stopMutationObserver(target);
          //}
          target.setAttribute('ly-is-intersecting', intersection.isIntersecting);
        });
      }
    
    
      // Mutation Observer
      function startMutationObserver(observerTarget) {
        if (!observerTarget) {
          var target = document.documentElement || document.body; // main window
        } else {
          if (observerTarget.nodeName  === 'IFRAME') {
            try { var target = observerTarget.contentDocument || observerTarget.contentWindow.document; }
            catch { return false; }
          } else {
            var target = observerTarget;
          }
        }
        mutationObs = new MutationObserver(callbackDomChange);
        mutationObs.observe(target, mutationObsConfig);
        observedNodes.push(target);
        try { target.setAttribute('ly-is-observing', 'true'); } catch(e) {}
      }
    
      function stopMutationObserver(target) {
        if(observedNodes.indexOf(target) > -1) {
          observedNodes.splice(observedNodes.indexOf(target), 1);
        }
        var mutations = mutationObs.takeRecords();
        mutationObs.disconnect();
        observedNodes.forEach(node => {
          mutationObs.observe(node, mutationObsConfig);
        });
        try { target.removeAttribute('ly-is-observing'); } catch(e) {}
      }
    
    
      function callbackDomChange(mutations, mutationObs) {
        for (var i = 0, length = mutations.length; i < length; i++) {
          var mutation = mutations[i];
          var target = mutation.target;
    
          // Links-Hook
          if (mutation.type === 'attributes' && (mutation.attributeName === 'href' || mutation.attributeName === 'action')) {
            var target = mutation.target;
            translateLink(target.getAttribute(mutation.attributeName), target);
          }
          // Images-Hook
          else if (mutation.type === 'attributes' && (mutation.attributeName === 'src' || mutation.attributeName === 'data-src' || mutation.attributeName === 'srcset' || mutation.attributeName === 'data-srcset')) {
            translateImage(target, mutation.attributeName);
          }
          else if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
            translateCssImage(target);
          }
          // Subtree Events
          else if (mutation.type === 'childList') {
            // Added Nodes
            if(mutation.addedNodes.length > 0) {
              var elementNodes = getNodesUnder(mutation.target, 'SHOW_ELEMENT');
              var textNodes = getNodesUnder(mutation.target, 'SHOW_TEXT');
              var nodes = elementNodes.concat(textNodes);
              nodes.forEach(function(node) {
                translate(node, 'mutation -> childList');
              });
            }
          }
          // CharacterData Events
          else if (mutation.type === 'characterData') {
            var target = mutation.target;
            translate(target, 'mutation -> characterData');
          }
        }
      }
    
    
      function translate(node, info) {
        if((node.nodeType === 1 && node.hasAttribute('data-ly-locked')) || (node.nodeType === 3 && node.parentNode && node.parentNode.hasAttribute('data-ly-locked'))) {
          return;
        }
        // CC-Hook
        if (node.nodeName  !== 'SCRIPT' && node.nodeName  !== 'STYLE') {
          if (node.nodeType === 3) {
            translateTextNode(node, info);
          }
          translateNodeAttrs(node);
        }
        // Links-Hook
        if (node.nodeName  === 'A' || node.nodeName  === 'FORM') {
          if(node.hasAttribute('href')) var attrName = 'href';
          else var attrName = 'action';
          var url = node.getAttribute(attrName);
          translateLink(url, node);
        }
        // Images-Hook
        if (node.nodeName  === 'IMG' || node.nodeName  === 'SOURCE') {
          translateImage(node, ['src', 'data-src', 'srcset', 'data-srcset']);
        }
        if (node.attributes && node.getAttribute('style')) {
          translateCssImage(node);
        }
        // Iframe Observation
        if (node.nodeName  === 'IFRAME') {
          // Todo: handle srcdoc iframe content observing
          if (node.getAttribute('ly-is-observing') == null && node.getAttribute('src') == null && !node.hasAttribute('srcdoc')) {
            node.setAttribute('ly-is-observing', 'true');
            startMutationObserver(node);
          }
        }
      }
    
      function translateNodeAttrs(node) {
        if (node.attributes && node.getAttribute('placeholder')) {
          var src = node.getAttribute('placeholder').trim().replace(/(\r\n|\n|\r)/gim,"").replace(/\s+/g," ").toLowerCase();
          if(customContents_text[src] && node.getAttribute('placeholder') !== customContents_text[src]) {
            node.setAttribute('placeholder', customContents_text[src]);
          }
        }
      }
    
    
      function translateTextNode(node, info) {
        if(langify.settings.observeCustomContents === false || !node.textContent || node.textContent.trim().length === 0) {
          return;
        }
        var oldSrc = node.textContent.trim().replace(/(\r\n|\n|\r)/gim,"").replace(/\s+/g," ").toLowerCase();
        var src = node.textContent.trim().replace(/(\r\n|\n|\r)/gim," ").replace(/\s+/g," ").toLowerCase();
        if(customContents_text[oldSrc]) {
          src = oldSrc;
        }
        if(customContents_text[src] && node.textContent !== customContents_text[src]) {
          var newContent = node.textContent.replace(node.textContent.trim(), customContents_text[src]);
          if (newContent != node.textContent) {
            if(!node.parentNode.hasAttribute('data-ly-mutation-count') || parseInt(node.parentNode.getAttribute('data-ly-mutation-count')) < langify.settings.maxMutations) {
              var count = node.parentNode.hasAttribute('data-ly-mutation-count') ? parseInt(node.parentNode.getAttribute('data-ly-mutation-count')) : 0;
              node.parentNode.setAttribute('data-ly-mutation-count', count+1);
              node.textContent = newContent;
    
              mutationCount = mutationCount + 1;
              LyHelper.log('REPLACED (TEXT)', {
                  oldValue: src,
                  newValue: customContents_text[src],
                  mutationCount,
                }, 'success'
              );
            
              var event = new CustomEvent('langify.observer.aftertranslatetext', { 
                bubbles: true,
                detail: {
                  target: node,
                  original: src,
                  translation: customContents_text[src]
                }
              });
              node.dispatchEvent(event);
            }
          }
        }
      }
    
      function translateLink(url, node) {
        if(langify.settings.observeLinks === false || isLocalizationForm(node) || node.hasAttribute('data-ly-locked') || !url || (url.indexOf('mailto:') !== -1 || url.indexOf('javascript:') !== -1 || url.indexOf('tel:') !== -1 || url.indexOf('file:') !== -1 || url.indexOf('ftp:') !== -1 || url.indexOf('sms:') !== -1 || url.indexOf('market:') !== -1 || url.indexOf('fax:') !== -1 || url.indexOf('callto:') !== -1 || url.indexOf('ts3server:') !== -1)) {
          return;
        }
        var isLocalized = (
          url.indexOf('/'+ langify.locale.iso_code +'/') === 0 || url === '/'+ langify.locale.iso_code || 
          url.indexOf('/'+ langify.locale.iso_code.toLowerCase() +'/') === 0 || url === '/'+ langify.locale.iso_code.toLowerCase() ||          
          url.indexOf('/'+ langify.locale.iso_code.toLowerCase().substring(0,2) +'/') === 0 || url === '/'+ langify.locale.iso_code.toLowerCase().substring(0,2) ||
          url.indexOf(langify.locale.root_url + '/') === 0 || url === langify.locale.root_url || 
          url.indexOf(langify.locale.shop_url + langify.locale.root_url) === 0 || url === langify.locale.shop_url + langify.locale.root_url
        );
        var cleanUrl = url.replace(langify.locale.shop_url, '');
        var re = new RegExp(`\/\\b${langify.locale.iso_code.replace('-', '\-')}\\b\/`, 'gi');
        var link = cleanUrl.replace(re, '/');
        var isUrlAbsolute = (link.indexOf('://') > 0 || link.indexOf('//') === 0);
        var blacklist = ['#', '/'+ langify.locale.iso_code +'#'].concat(langify.settings.linksBlacklist);
        var isUrlBlacklisted = blacklist.find(x => url.indexOf(x) === 0);
        if(!isLocalized && !isUrlAbsolute && !isUrlBlacklisted && langify.locale.root_url != '/') {
          if(node.hasAttribute('href')) var attrName = 'href'; else var attrName = 'action';
          if(link === '/' || link == langify.locale.root_url) link = '';
          var newLink = langify.locale.root_url + link;
          var timeStamp = Math.floor(Date.now());
          if(!node.hasAttribute('data-ly-processed') || timeStamp > parseInt(node.getAttribute('data-ly-processed')) + langify.settings.timeout) {
            node.setAttribute('data-ly-processed', timeStamp);
            node.setAttribute(attrName, newLink);
            if(node.hasAttribute('data-'+attrName)) node.setAttribute('data-'+attrName, newLink);
    
            mutationCount = mutationCount + 1;
            LyHelper.log('REPLACED (LINK)', {
                attrName,
                oldValue: url,
                newValue: newLink,
                mutationCount,
              }, 'success'
            );
            
            var event = new CustomEvent('langify.observer.aftertranslatelink', { 
              bubbles: true,
              detail: {
                target: node,
                attribute: attrName,
                original: url,
                translation: newLink
              }
            });
            node.dispatchEvent(event);
          }
        }
      }
    
      function translateImage(node, attr) {
        if(langify.settings.observeImages === false || node.hasAttribute('data-ly-locked') || Object.keys(customContents_image).length === 0) {
          return
        }
        var attrs = [];
        if(typeof attr === 'string') attrs.push(attr);
        else if(typeof attr === 'object') attrs = attr;
        var timeStamp = Math.floor(Date.now());
        if(!node.hasAttribute('data-ly-processed') || timeStamp > parseInt(node.getAttribute('data-ly-processed')) + langify.settings.timeout) {
          node.setAttribute('data-ly-processed', timeStamp);
          attrs.forEach(function(attr) {
            if(node.hasAttribute(attr)) {
              var imgObject = LyHelper.extractImageObject(node.getAttribute(attr));
              var imgKey = imgObject ? imgObject.file.toLowerCase() : '';
    
              if(customContents_image[imgKey]) {
    
                // Replace
                var oldValue = node.getAttribute(attr);
                var translation = node.getAttribute(attr);
                translation = translation.replace(new RegExp(imgObject.host, 'g'), customContents_image[imgKey].host);
                translation = translation.replace(new RegExp(imgObject.name, 'g'), customContents_image[imgKey].name);
                translation = translation.replace(new RegExp(imgObject.type, 'g'), customContents_image[imgKey].type);
    
                // China Hook
                if(node.getAttribute(attr).indexOf('cdn.shopifycdn.net') >= 0) {
                  translation = translation.replace(new RegExp('cdn.shopify.com', 'g'), 'cdn.shopifycdn.net'); 
                }
    
                if(node.getAttribute(attr) != translation) {
                  node.setAttribute(attr, translation);
    
                  mutationCount = mutationCount + 1;
                  LyHelper.log('REPLACED (IMAGE)', {
                    attrName: attr,
                    oldValue: oldValue,
                    newValue: translation,
                    mutationCount,
                  }, 'success');
            
                  var event = new CustomEvent('langify.observer.aftertranslateimage', { 
                    bubbles: true,
                    detail: {
                      target: node,
                      attribute: attr,
                      original: oldValue,
                      translation: translation
                    }
                  });
                  node.dispatchEvent(event);
                }
              }
            }
          });
        }
      }
    
      function translateCssImage(node) {
        if(langify.settings.observeImages === false || node.hasAttribute('data-ly-locked') || Object.keys(customContents_image).length === 0 || !node.getAttribute('style')) {
          return
        }
    
        var imgMatches = node.getAttribute('style').match(/url\(("|')?(.*)("|')?\)/gi);
        if(imgMatches !== null) {
          var imgSource = imgMatches[0].replace(/url\(("|')?|("|')?\)/, '');
          var imgObject = LyHelper.extractImageObject(imgSource);
          var imgKey = imgObject ? imgObject.file.toLowerCase() : '';
          var attr = 'style';
    
          if(customContents_image[imgKey]) {
    
            // Replace
            var translation = node.getAttribute(attr);
            translation = translation.replace(new RegExp(imgObject.host, 'g'), customContents_image[imgKey].host);
            translation = translation.replace(new RegExp(imgObject.name, 'g'), customContents_image[imgKey].name);
            translation = translation.replace(new RegExp(imgObject.type, 'g'), customContents_image[imgKey].type);
    
            // China Hook
            if(node.getAttribute(attr).indexOf('cdn.shopifycdn.net') >= 0) {
              translation = translation.replace(new RegExp('cdn.shopify.com', 'g'), 'cdn.shopifycdn.net'); 
            }
    
            if(node.getAttribute(attr) != translation) {
              var timeStamp = Math.floor(Date.now());
              if(!node.hasAttribute('data-ly-processed') || timeStamp > parseInt(node.getAttribute('data-ly-processed')) + langify.settings.timeout) {
                node.setAttribute('data-ly-processed', timeStamp);
                node.setAttribute(attr, translation);
                mutationCount = mutationCount + 1;
              }
            }
          }          
        }
      }
    
      function findAndLocalizeLinks(target, parent) {
        if(target.parentNode && parent) var allLinks = target.parentElement.querySelectorAll('[href],[action]');
        else var allLinks = target.querySelectorAll('[href],[action]');
        allLinks.forEach(link => {
          if(link.hasAttribute('href')) var attrName = 'href'; else var attrName = 'action';
          var url = link.getAttribute(attrName);
          translateLink(url, link);
        });
      }
      
      function isLocalizationForm(node) {
        if(node.querySelector('input[name="form_type"][value="localization"]')){
          return true;
        }
        return false;
      }
    
      function getNodesUnder(el, show){
        var n, a=[], walk=document.createTreeWalker(el, NodeFilter[show] , null, false);
        while(n=walk.nextNode()) a.push(n);
        return a;
      }
    
      function spreadCustomContents() {
        var getFileName = function(url) {
          if(!url || url == '') return;
          url = url.substring(0, (url.indexOf("#") == -1) ? url.length : url.indexOf("#"));
          url = url.substring(0, (url.indexOf("?") == -1) ? url.length : url.indexOf("?"));
          url = url.substring(url.lastIndexOf("/") + 1, url.length);
          url = url.replace(/(_[0-9]+x[0-9]*|_{width}x)?(_crop_(top|center|bottom|left|right))?(@[0-9]*x)?(\.progressive)?\.(jpe?g|png|gif|webp)/gi, "");
          return '/' + url;
        }
        Object.entries(customContents).forEach(function(entry) {
          //if(/<\/?[a-z][\s\S]*>/i.test(entry[0])) customContents_html[entry[0]] = entry[1];
          //else if(/(http(s?):)?([/|.|\w|\s|-])*\.(?:jpe?g|gif|png)/.test(entry[0])) customContents_image[getFileName(entry[0])] = getFileName(entry[1]);
          if(/(http(s?):)?([/|.|\w|\s|-])*\.(?:jpe?g|gif|png|webp)/.test(entry[0])) customContents_image[LyHelper.extractImageObject(entry[0]).file] = LyHelper.extractImageObject(entry[1]);
          else customContents_text[entry[0]] = entry[1];
        });
        LyHelper.log('CUSTOM CONTENTS:', {customContents, customContents_text, customContents_image}, 'info')
      }
    
      // Polyfill for old browsers
      function startMutationEvents() {
        var target = document.querySelector('body');
        target.addEventListener("DOMAttrModified", function (event) {
          if(event.attrName === 'href' || event.attrName === 'action') {
            if(event.prevValue != event.newValue) {
              translateLink(event.newValue, event.target);
            }
          }
        }, false);
        target.addEventListener("DOMSubtreeModified", function (event) {
          //findAndLocalizeLinks(event.target, false);
          //matchCustomContent(event.target);
        }, false);
      }
    
      function triggerCustomContents() {
        var rootnode = document.getElementsByTagName('body')[0]
        var walker = document.createTreeWalker(rootnode, NodeFilter.SHOW_ALL, null, false)
        
        while (walker.nextNode()) {
          //LyHelper.log(walker.currentNode.tagName)
          translate(walker.currentNode, null);
        }
      }
    
      function stopObserver() {
        mutationObs.takeRecords();
        mutationObs.disconnect();
      }
    
      //init();
      return {
        init: init,
        triggerCustomContents: triggerCustomContents,
        stopObserver: stopObserver,
      }
    };
  }


  /**
  *
  * 
  * @class Switcher
  */
  class Switcher {
    constructor(switcherSettings) {
      LyHelper.log('Switcher instantiation');

      this.lyForceOff = location.search.split('ly-force-off=')[1];
      this.switcherElements = Array.prototype.slice.call(document.getElementsByClassName('ly-switcher-wrapper'));
    }

    init() {

      // Initial cart.attributes update
      LyHelper.shopifyAPI().getCart(function (cart) {
        var currentLanguage = langify.locale.iso_code;
        if(!cart.attributes.language || cart.attributes.language != currentLanguage) {
          LyHelper.shopifyAPI().updateCartAttributes({"language": currentLanguage}, function(data){});
        }
      });

      if(langify.locale.languages.length === 1) {
        console.info(`%c LANGIFY INFO:\n`, 'font-weight: bold;', 'The language switcher is hidden! This can have one of the following reasons: \n * All additional languages are disabled in the "Langify -> Dashboard -> Languages" section. \n * If you are using different domains for your additional languages, ensure that "Cross Domain Links" are enabled inside the "Langify -> Dashboard -> Switcher Configurator -> Domains" section.');
        return false;
      }

      this.setCustomPosition();
      this.initCustomDropdown();
      this.bindCountrySwitchers();
      this.bindCurrencySwitchers();
      this.bindLanguageSwitchers();

      var event = new CustomEvent('langify.switcher.initialized', { 
        bubbles: true,
        detail: {}
      });
    }

    initCustomDropdown() {
      // Custom Dropdown
      var root = this;
      var switcher = document.getElementsByClassName('ly-custom-dropdown-switcher');
      for(var a = 0; a < switcher.length; a++) {
        switcher[a].classList.toggle('ly-is-open');
        var isOut = LyHelper.isOutOfViewport(switcher[a]);
        if(isOut.bottom && isOut.inViewport) {
          switcher[a].classList.add('ly-is-dropup');
          var arrows = switcher[a].querySelectorAll('.ly-arrow');
          for(var b = 0; b < arrows.length; b++) {
            arrows[b].classList.add('ly-arrow-up');    
          }
        }
        switcher[a].classList.toggle('ly-is-open');
        switcher[a].onclick = function(event) {
          root.toggleSwitcherOpen(this);
        }
      }

      document.addEventListener('click', function(event) {
        if(!event.target.closest('.ly-custom-dropdown-switcher')) {
          var openSwitchers = document.querySelectorAll('.ly-custom-dropdown-switcher.ly-is-open')
          for(var i = 0; i < openSwitchers.length; i++) {
            openSwitchers[i].classList.remove('ly-is-open');
          }
        }
      });
    }
    
    bindLanguageSwitchers() {
      var root = this;
      var links = document.getElementsByClassName('ly-languages-switcher-link');
      for(var l = 0; l < links.length; l++) {
        links[l].addEventListener('click', function(event) {
          event.preventDefault();
          if(LyHelper.isDomainFeatureEnabled() && this.getAttribute('href') !== '#') {
          	var domain = this.getAttribute('href');
          }
          LyHelper.setCookie('ly-lang-selected', this.getAttribute('data-language-code'), 365);
          LyHelper.localizationRedirect('language_code', this.getAttribute('data-language-code'), null, null, domain);
        });
      }

      // Native select event handling
      var nativeLangifySelects = document.querySelectorAll('select.ly-languages-switcher');
      for(var i = 0; i < nativeLangifySelects.length; i++) {
        nativeLangifySelects[i].onchange = function() {
          event.preventDefault();
          if(LyHelper.isDomainFeatureEnabled() && this[this.selectedIndex].getAttribute('data-domain') !== '#') {
            var domain = this[this.selectedIndex].getAttribute('data-domain');
          }
          LyHelper.setCookie('ly-lang-selected', this[this.selectedIndex].getAttribute('data-language-code'), 365);
          LyHelper.localizationRedirect('language_code', this[this.selectedIndex].getAttribute('data-language-code'), null, null, domain);
        };
      }
    }
    
    bindCountrySwitchers() {
      var countrySelectSwitcher = document.querySelectorAll('select.ly-country-switcher');
      var countryCustomSwitcher = document.querySelectorAll('div.ly-country-switcher a');
      for(var i=0; i<countryCustomSwitcher.length; i++) {
        countryCustomSwitcher[i].addEventListener('click', function(e) {
          LyHelper.localizationRedirect('country_code', this.getAttribute('data-country-code'));
        });
      }
      for(var a=0; a<countrySelectSwitcher.length; a++) {
        countrySelectSwitcher[a].addEventListener('change', function(){
          LyHelper.localizationRedirect('country_code', this[this.selectedIndex].getAttribute('data-country-code'));
        });
      }  
    }
    
    bindCurrencySwitchers() {
      var currencySelectSwitcher = document.querySelectorAll('select.ly-currency-switcher');
      var currencyCustomSwitcher = document.querySelectorAll('div.ly-currency-switcher a');
      for(var i=0; i<currencyCustomSwitcher.length; i++) {
        currencyCustomSwitcher[i].addEventListener('click', function(e) {
          LyHelper.changeCurrency(this.getAttribute('data-currency-code'));
        });
      }
      for(var a=0; a<currencySelectSwitcher.length; a++) {
        currencySelectSwitcher[a].addEventListener('change', function(){
          LyHelper.changeCurrency(this[this.selectedIndex].getAttribute('data-currency-code'));
        });
      }  
    }

    setCustomPosition() {
      for(var i = 0; i < this.switcherElements.length; i++) {
        if(this.lyForceOff !== 'true') {
          if(langify.locale.languages.length <= 1) {
            this.switcherElements[i].querySelector('.ly-languages-switcher').classList.add('ly-hide');
          }
          this.switcherElements[i].classList.remove('ly-hide');
        }
        if(this.switcherElements[i].classList.contains('ly-custom') && (document.getElementById('ly-custom-'+this.switcherElements[i].getAttribute('data-breakpoint')) || document.getElementsByClassName('ly-custom-'+this.switcherElements[i].getAttribute('data-breakpoint')).length )) {
          var targets = Array.from(document.getElementsByClassName('ly-custom-'+this.switcherElements[i].getAttribute('data-breakpoint')));
          var target = document.getElementById('ly-custom-'+this.switcherElements[i].getAttribute('data-breakpoint'));
          if(target) targets.push(target);
          for(var c = 0; c < targets.length; c++) {
            var clone = this.switcherElements[i].cloneNode(true);
            targets[c].innerHTML = clone.outerHTML;
          }
          this.switcherElements[i].classList.add('ly-hide');
        }
      }
    }

    toggleSwitcherOpen(e) {
      var target = e;
      if(!target.classList.contains('ly-is-open')) {
        var openSwitchers = document.querySelectorAll('.ly-custom-dropdown-switcher.ly-is-open')
        for(var i = 0; i < openSwitchers.length; i++) {
          openSwitchers[i].classList.remove('ly-is-open');
        }

        target.getElementsByClassName('ly-custom-dropdown-current')[0].setAttribute('aria-expanded', 'true');
      } else {
        target.getElementsByClassName('ly-custom-dropdown-current')[0].setAttribute('aria-expanded', 'false');
      }
      target.classList.toggle('ly-is-open');
      var isOut = LyHelper.isOutOfViewport(target);
      if(isOut.bottom) {
        target.classList.add('ly-is-dropup');
      }
    }

    togglePopupOpen(e) {
      e.closest('.ly-popup-switcher').classList.toggle('ly-is-open');
    }

    _getCountryCodeFromHreflang(language_code) {
      var hreflangTags = document.querySelectorAll('link[hreflang]');
      var languageDomains = {};
      var countryCode = false;
      for(var i = 0; i < hreflangTags.length; i++) {
        if(hreflangTags[i].getAttribute('hreflang').indexOf(language_code) === 0) {
          if(!languageDomains[hreflangTags[i].getAttribute('href')]) {
            languageDomains[hreflangTags[i].getAttribute('href')] = hreflangTags[i];
          }
        }
      }
      if(Object.keys(languageDomains).length > 0) {
        countryCode = Object.values(languageDomains)[0].getAttribute('hreflang');
        countryCode = countryCode.split('-');
        if(countryCode[1]) {
          countryCode = countryCode[1]
        } else {
          
        }
      }
      return countryCode;
    }
  }


  /**
  *
  *
  * @class LanguageDetection
  */
  class LanguageDetection {

    constructor() {
      this.hreflangList = null;
      LyHelper.log('LanguageDetection instantiation');
    }

    init() {
      if(LyHelper.inIframe()) return null;
      var currentLang = langify.locale.iso_code.toLowerCase();
      //var localizationData = this.getLocalizationData();
      this.hreflangList = this.getHreflangList();

      var userLang = navigator.language || navigator.userLanguage;
      userLang = userLang.toLowerCase();
      var bot = /bot|google|baidu|bing|msn|duckduckbot|teoma|slurp|yandex|Chrome-Lighthouse/i.test(navigator.userAgent);
      var blockedRoutes = window.lyBlockedRoutesList || [];
      var blockedRoute = blockedRoutes.find(x => window.location.pathname.indexOf(x) === 0);
      var is404 = document.getElementsByClassName('template-404').length;

      if(LyHelper.getVal('redirected') === 'true') {
        LyHelper.setCookie('ly-lang-selected', currentLang, 365);
        var _href = window.location.href;
        history.replaceState(null, '', _href.replace(/(\?|&)(redirected=true)/i, ''));
        return null;
      }

      if(!is404 && !bot && !blockedRoute && LyHelper.getVal('ly-lang-detect') !== 'off') {
        if(userLang && !LyHelper.getCookie('ly-lang-selected')) {
          var hasMatch = this.matchHreflangList(userLang);
          if(currentLang !== this._hreflangToLanguageCode(hasMatch)) {
            if(hasMatch) {
              this.redirectMatched(userLang);
            } else if (!hasMatch && langify.settings.switcher.languageDetectionDefault) {
              // Redirect to default language
              this.redirectMatched(langify.settings.switcher.languageDetectionDefault);
            }
          }
        } else {
          if(LyHelper.getCookie('ly-lang-selected') && LyHelper.getCookie('ly-lang-selected') !== currentLang) {
            // Only save cookie when the domain feature is active
            if(LyHelper.isDomainFeatureEnabled()) {
              LyHelper.setCookie('ly-lang-selected', currentLang, 365);
            }
            this.redirectMatched(LyHelper.getCookie('ly-lang-selected'));
          }
        }
      }
      
      this._geolocationAppSupport();
    }
    redirectMatched(userLang) {
      var match = this.matchHreflangList(userLang);
      if(match) {
        this.redirectHref(match.href, match.lang);
      }
      return match;
    }
    matchHreflangList(userLang) {
      var matchedHreflang = false;
      var browserLang = userLang;

      // Find candidates
      var candidates = this.hreflangList.filter(item => item.lang === browserLang);
      if(candidates.length === 0) {
        browserLang = userLang.toLowerCase();
        candidates = this.hreflangList.filter(item => item.lang.toLowerCase().indexOf(browserLang) === 0);
      }
      if(candidates.length === 0) {
        browserLang = userLang.toLowerCase().substring(0, 2);
        candidates = this.hreflangList.filter(item => item.lang.toLowerCase().indexOf(browserLang) === 0);
      }

      // Handle candidates
      if(candidates.length === 1) {
        return({href: candidates[0].href, lang: browserLang});
      } 
      else if(candidates.length > 1) {
        var preferedCandidate = candidates.find(item => item.href.indexOf(window.location.host) >= 0)
        if(preferedCandidate) {
          return({href: preferedCandidate.href, lang: browserLang});
        } else {
          return({href: candidates[0].href, lang: browserLang});
        }
      } 
      
      return false;
    }
    redirectHref(href, userLang) {
      var queryString = window.location.search;
      var url = href.split('?')[0];
      if(queryString === '') {
        queryString = '?redirected=true';
      } else {
        queryString += '&redirected=true';
      }
      window.location.replace(url + queryString);
    }
    getHreflangList() {
      var hreflangTags = document.querySelectorAll('link[hreflang]');
      var hreflangList = [];
      var countryCode = false;
      // Cross Domain Links option
      if(LyHelper.isDomainFeatureEnabled()) {
        for(var i = 0; i < langify.locale.languages.length; i++) {
          hreflangList.push({
            href: 'https://' + langify.locale.languages[i].domain + window.location.pathname + window.location.search + window.location.hash,
            lang: langify.locale.languages[i].iso_code,
          });
        }
      }
      for(var i = 0; i < hreflangTags.length; i++) {
        var hreflang = hreflangTags[i].getAttribute('hreflang').toLowerCase();
        hreflang = hreflang.replace('zh-hans', 'zh-cn').replace('zh-hant', 'zh-tw'); // Chinese special cases
        hreflangList.push({
          href: hreflangTags[i].getAttribute('href'),
          lang: hreflang
        });
      }
      return hreflangList;
    }
    _langToHreflang(userLang) {
      var hreflang = '';
      this.hreflangList.map(function(item) {
        if(item.lang === userLang) {
          hreflang = userLang;
        }
      });
      if(hreflang === '') {
        this.hreflangList.map(function(item) {
          var browserLang = userLang.toLowerCase();
          if(item.lang.toLowerCase().indexOf(browserLang) === 0) {
            hreflang = browserLang;
          }
          else if(item.lang.toLowerCase().indexOf(browserLang.substring(0, 2)) === 0) {
            hreflang = browserLang.substring(0, 2);
          }
        });        
      }
      return hreflang || userLang;
    }
    _hreflangToLanguageCode(hreflangTag) {
      if(!hreflangTag || !hreflangTag.lang) return false;
	  var languageCode = false;
      langify.locale.languages.forEach(function(item) {
        if(item.iso_code.toLowerCase() === hreflangTag.lang.toLowerCase()) {
          languageCode = item.iso_code;
        }
      });
      if(!languageCode) {
        langify.locale.languages.forEach(function(item) {
          if(item.iso_code.toLowerCase() === hreflangTag.lang.substring(0, 2).toLowerCase()) {
            languageCode = item.iso_code;
          }
        });
      }
      return languageCode;
    }
    _geolocationAppSupport() {
      var target = document.querySelector('body');
      var config = { childList: true, subtree: true };
      var geolocationObserver = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
          if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
            var target = mutation.target.getElementsByClassName('locale-bar__form');
            if(target[0]) {
              target[0].onsubmit = function() {
                var locale_code = target[0].elements['locale_code'].value;
                var selector = target[0].getElementsByClassName('locale-bar__selector');
                if(selector.length >= 1) {
                  locale_code = selector[0].options[selector[0].selectedIndex].value;
                }
                LyHelper.setCookie('ly-lang-selected', locale_code, 365);
              };
            }
            var selectors = mutation.target.getElementsByClassName('locale-selectors__selector');
            if(selectors.length > 0) {
              for(var selector of selectors) {
                if(selector.getAttribute('name') === 'locale_code') {
                  selector.onchange = function() {
                    var locale_code = selector.options[selector.selectedIndex].value;
                    LyHelper.setCookie('ly-lang-selected', locale_code, 365);
                  };        
                }
              }
            }
          }
        });
      });
      geolocationObserver.observe(target, config);
      setTimeout(function() {
        geolocationObserver.disconnect();
      }, 10000);
    }
  }


  /**
  *
  *
  * @class Recommendation
  */
  class Recommendation extends LanguageDetection {
    
    constructor() {
      super();
      LyHelper.log('Recommendation instantiation');
    }

    init() {
      this.hreflangList = this.getHreflangList();

      var userLang = navigator.language || navigator.userLanguage;
      userLang = userLang.toLowerCase();

      var recommendation = this.matchHreflangList(userLang);
      var recommendationLanguageCode = this._hreflangToLanguageCode(recommendation);
      if(recommendationLanguageCode && recommendationLanguageCode !== langify.locale.iso_code && !LyHelper.getCookie('ly-lang-selected')) {
        this.recommended_language_code = recommendationLanguageCode;
        this.recommended_country_code = null;
        this.recommended_currency_code = null;
        this._translateStrings(recommendationLanguageCode);
        this._bindings();
      } else {
        return false;
      }
    }

    _translateStrings(languageCode) {
      var recommendationElement = document.querySelectorAll('.ly-recommendation')[0];
      var strings = langify.settings.switcher.recommendation_strings[languageCode];
      var recommendationStr = 'Looks like your browser is set to [[language]]. Change the language?';
      var buttonStr = 'Change';
      var matchedLanguageCode = this._langToHreflang(languageCode).toLowerCase();
      var languageStr = langify.settings.switcher.languages.find(x => x.iso_code.toLowerCase() === matchedLanguageCode) ? langify.settings.switcher.languages.find(x => x.iso_code.toLowerCase() === matchedLanguageCode).name : languageCode.toUpperCase();
      if(strings) {
        recommendationStr = strings.recommendation ? strings.recommendation : recommendationStr;
        buttonStr = strings.button ? strings.button : buttonStr;
        languageStr = strings.language ? strings.language : languageStr;
      }
      var newCode = recommendationElement.innerHTML;
      newCode = newCode.replaceAll('[[recommendation]]', recommendationStr).replaceAll('[[button]]', buttonStr).replaceAll('[[language]]', languageStr);
      recommendationElement.innerHTML = newCode;     
    }

    _bindings() {
      var _this = this;
      var lyForceOff = location.search.split('ly-force-off=')[1];
      var recommendationElement = document.querySelectorAll('.ly-recommendation')[0];
      var form = recommendationElement.querySelectorAll('.ly-recommendation-form')[0];
      var links = recommendationElement.getElementsByClassName('ly-custom-dropdown-list-element');
      var customDropdown = recommendationElement.querySelectorAll('.ly-custom-dropdown-switcher');
      var nativeSelects = recommendationElement.getElementsByClassName('ly-native-select');

      form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if(_this.recommended_country_code || _this.recommended_currency_code) {
          // When a country or currency is involved, use old redirection
          var additionalFields = [];
          if(_this.recommended_country_code) {
            additionalFields.push({
              name: 'country_code',
              value: _this.recommended_country_code
            });
          }
          if(_this.recommended_currency_code) {
            additionalFields.push({
              name: 'currency_code',
              value: _this.recommended_currency_code
            });
          }
          LyHelper.setCookie('ly-lang-selected', _this.recommended_language_code, 365);
          LyHelper.localizationRedirect('language_code', _this.recommended_language_code, additionalFields);
        } else {
          //...or use new hreflang redirection
          _this.redirectMatched(_this.recommended_language_code);
        }
      });
      
      // Overwrite switchers
      for(var l = 0; l < links.length; l++) {
        links[l].addEventListener('click', function(event) {
          event.preventDefault();
          
          // When clicked from within a custom dropdown
          var parentCustomDropdown = event.currentTarget.closest('.ly-custom-dropdown-switcher');
          if(parentCustomDropdown) {
            _this._selectCustomDropdownEntry(parentCustomDropdown, this);
          } else {
            _this.recommended_language_code = this.getAttribute('data-language-code');
          }
        });
      }

      for(var i = 0; i < nativeSelects.length; i++) {
        nativeSelects[i].onchange = _this._selectNativeDropdownEntry.bind(this);
        
        if(nativeSelects[i].classList.contains('ly-languages-switcher')) {
          nativeSelects[i].value = _this.recommended_language_code;
        }
      }

      for(var i = 0; i < customDropdown.length; i++) {
        if(recommendationElement.className.indexOf('bottom') > -1) {
          customDropdown[i].classList.add('ly-is-dropup');       
        }
        
        if(customDropdown[i].classList.contains('ly-languages-switcher')) {
          _this._selectCustomDropdownEntry(customDropdown[i], customDropdown[i].querySelector('.ly-custom-dropdown-list a[data-language-code="'+ _this.recommended_language_code +'"]'));
        }
      } 
      if(!lyForceOff) {
        recommendationElement.classList.add('ly-is-open');
      }
    }

    _selectNativeDropdownEntry(event) {
      event.preventDefault();
      var element = event.currentTarget;
      var _this = this;

      if(element[element.selectedIndex].hasAttribute('data-language-code')) {
        _this.recommended_language_code = element[element.selectedIndex].getAttribute('data-language-code');
      }
      if(element[element.selectedIndex].hasAttribute('data-country-code')) {
        _this.recommended_country_code = element[element.selectedIndex].getAttribute('data-country-code');
      }
      if(element[element.selectedIndex].hasAttribute('data-currency-code')) {
        _this.recommended_currency_code = element[element.selectedIndex].getAttribute('data-currency-code');
      }
    }

    _selectCustomDropdownEntry(element, entry) {
      if(!entry) return;
      var _this = this;
      var currentElem = element.querySelector('.ly-custom-dropdown-current');
      var currentIcon = currentElem.querySelector('.ly-icon');        
      var currentLabel = currentElem.querySelector('span');
      var valueToSelect = '';

      if(entry.hasAttribute('data-language-code')) {
        _this.recommended_language_code = entry.getAttribute('data-language-code');
        valueToSelect = _this.recommended_language_code;
      }
      if(entry.hasAttribute('data-country-code')) {
        _this.recommended_country_code = entry.getAttribute('data-country-code');
        valueToSelect = _this.recommended_country_code;
      }
      if(entry.hasAttribute('data-currency-code')) {
        _this.recommended_currency_code = entry.getAttribute('data-currency-code');
        valueToSelect = _this.recommended_currency_code;
      }
      if(entry.hasAttribute('data-country-code') && entry.hasAttribute('data-currency-code')) {
        valueToSelect = _this.recommended_country_code + '-' + _this.recommended_currency_code;
      }

      if(element.querySelector('.ly-custom-dropdown-list li.current')) {
        element.querySelector('.ly-custom-dropdown-list li.current').classList.remove('current');  
        element.querySelector('.ly-custom-dropdown-list li[key="'+ valueToSelect +'"]').classList.add('current');  
      }
      if(element.querySelector('.ly-custom-dropdown-list li.current span')) currentLabel.innerHTML = element.querySelector('.ly-custom-dropdown-list li.current span').innerHTML;
      if(element.querySelector('.ly-custom-dropdown-list li.current .ly-icon')) currentIcon.classList = element.querySelector('.ly-custom-dropdown-list li.current .ly-icon').classList;
    }

    toggleOpen(e) {
      e.closest('.ly-recommendation').classList.toggle('ly-is-open');
      if(!e.closest('.ly-recommendation').classList.contains('ly-is-open')) {
        LyHelper.setCookie('ly-lang-selected', this.recommended_language_code, 365);
      }
    }
  }


  /**
  *
  *
  * @class Langify
  */
  class Langify {
    constructor(settings, locale) {
      LyHelper.log('Langify instantiation')

      var translationObserver = new TranslationObserver().init();

      langify.helper = new LyHelper();
      LyHelper.log(langify.helper);


      var lyForceOff = location.search.split('ly-force-off=')[1];
      if(lyForceOff === 'true') {
        document.getElementById('preview-bar-iframe').classList.add('ly-hide');
        return false;
      }

      if(settings.theme && ((settings.theme.loadJquery && settings.theme.loadJquery === true) || typeof settings.theme.loadJquery === 'undefined')) {
        if(typeof jQuery === 'undefined') {
          LyHelper.loadScript('//cdn.jsdelivr.net/jquery/1.9.1/jquery.min.js', function() {});
        } 
      }

      //document.addEventListener("DOMContentLoaded", function() {
        langify.switcher = new Switcher();
        langify.switcher.init();

        if(settings.switcher) {
          if(settings.switcher.recommendation && settings.switcher.recommendation_enabled) {
            langify.recommendation = new Recommendation();
            langify.recommendation.init();
          } else {
            if(settings.switcher.languageDetection) {
              langify.languageDetection = new LanguageDetection();
              langify.languageDetection.init();
            }        
          }
        }
      //});
    }
  }

  // Polyfills
  (function(){var t;null==window.MutationObserver&&(t=function(){function t(t){this.callBack=t}return t.prototype.observe=function(t,n){return this.element=t,this.interval=setInterval((e=this,function(){var t;if((t=e.element.innerHTML)!==e.oldHtml)return e.oldHtml=t,e.callBack.apply(null)}),200);var e},t.prototype.disconnect=function(){return window.clearInterval(this.interval)},t}(),window.MutationObserver=t)}).call(this);
  !function(){"use strict";if("object"==typeof window)if("IntersectionObserver"in window&&"IntersectionObserverEntry"in window&&"intersectionRatio"in window.IntersectionObserverEntry.prototype)"isIntersecting"in window.IntersectionObserverEntry.prototype||Object.defineProperty(window.IntersectionObserverEntry.prototype,"isIntersecting",{get:function(){return this.intersectionRatio>0}});else{var t=function(t){for(var e=window.document,o=i(e);o;)o=i(e=o.ownerDocument);return e}(),e=[],o=null,n=null;s.prototype.THROTTLE_TIMEOUT=100,s.prototype.POLL_INTERVAL=null,s.prototype.USE_MUTATION_OBSERVER=!0,s._setupCrossOriginUpdater=function(){return o||(o=function(t,o){n=t&&o?l(t,o):{top:0,bottom:0,left:0,right:0,width:0,height:0},e.forEach(function(t){t._checkForIntersections()})}),o},s._resetCrossOriginUpdater=function(){o=null,n=null},s.prototype.observe=function(t){if(!this._observationTargets.some(function(e){return e.element==t})){if(!t||1!=t.nodeType)throw new Error("target must be an Element");this._registerInstance(),this._observationTargets.push({element:t,entry:null}),this._monitorIntersections(t.ownerDocument),this._checkForIntersections()}},s.prototype.unobserve=function(t){this._observationTargets=this._observationTargets.filter(function(e){return e.element!=t}),this._unmonitorIntersections(t.ownerDocument),0==this._observationTargets.length&&this._unregisterInstance()},s.prototype.disconnect=function(){this._observationTargets=[],this._unmonitorAllIntersections(),this._unregisterInstance()},s.prototype.takeRecords=function(){var t=this._queuedEntries.slice();return this._queuedEntries=[],t},s.prototype._initThresholds=function(t){var e=t||[0];return Array.isArray(e)||(e=[e]),e.sort().filter(function(t,e,o){if("number"!=typeof t||isNaN(t)||t<0||t>1)throw new Error("threshold must be a number between 0 and 1 inclusively");return t!==o[e-1]})},s.prototype._parseRootMargin=function(t){var e=(t||"0px").split(/\s+/).map(function(t){var e=/^(-?\d*\.?\d+)(px|%)$/.exec(t);if(!e)throw new Error("rootMargin must be specified in pixels or percent");return{value:parseFloat(e[1]),unit:e[2]}});return e[1]=e[1]||e[0],e[2]=e[2]||e[0],e[3]=e[3]||e[1],e},s.prototype._monitorIntersections=function(e){var o=e.defaultView;if(o&&-1==this._monitoringDocuments.indexOf(e)){var n=this._checkForIntersections,r=null,s=null;this.POLL_INTERVAL?r=o.setInterval(n,this.POLL_INTERVAL):(h(o,"resize",n,!0),h(e,"scroll",n,!0),this.USE_MUTATION_OBSERVER&&"MutationObserver"in o&&(s=new o.MutationObserver(n)).observe(e,{attributes:!0,childList:!0,characterData:!0,subtree:!0})),this._monitoringDocuments.push(e),this._monitoringUnsubscribes.push(function(){var t=e.defaultView;t&&(r&&t.clearInterval(r),c(t,"resize",n,!0)),c(e,"scroll",n,!0),s&&s.disconnect()});var u=this.root&&(this.root.ownerDocument||this.root)||t;if(e!=u){var a=i(e);a&&this._monitorIntersections(a.ownerDocument)}}},s.prototype._unmonitorIntersections=function(e){var o=this._monitoringDocuments.indexOf(e);if(-1!=o){var n=this.root&&(this.root.ownerDocument||this.root)||t;if(!this._observationTargets.some(function(t){var o=t.element.ownerDocument;if(o==e)return!0;for(;o&&o!=n;){var r=i(o);if((o=r&&r.ownerDocument)==e)return!0}return!1})){var r=this._monitoringUnsubscribes[o];if(this._monitoringDocuments.splice(o,1),this._monitoringUnsubscribes.splice(o,1),r(),e!=n){var s=i(e);s&&this._unmonitorIntersections(s.ownerDocument)}}}},s.prototype._unmonitorAllIntersections=function(){var t=this._monitoringUnsubscribes.slice(0);this._monitoringDocuments.length=0,this._monitoringUnsubscribes.length=0;for(var e=0;e<t.length;e++)t[e]()},s.prototype._checkForIntersections=function(){if(this.root||!o||n){var t=this._rootIsInDom(),e=t?this._getRootRect():{top:0,bottom:0,left:0,right:0,width:0,height:0};this._observationTargets.forEach(function(n){var i=n.element,s=u(i),h=this._rootContainsTarget(i),c=n.entry,a=t&&h&&this._computeTargetAndRootIntersection(i,s,e),l=null;this._rootContainsTarget(i)?o&&!this.root||(l=e):l={top:0,bottom:0,left:0,right:0,width:0,height:0};var f=n.entry=new r({time:window.performance&&performance.now&&performance.now(),target:i,boundingClientRect:s,rootBounds:l,intersectionRect:a});c?t&&h?this._hasCrossedThreshold(c,f)&&this._queuedEntries.push(f):c&&c.isIntersecting&&this._queuedEntries.push(f):this._queuedEntries.push(f)},this),this._queuedEntries.length&&this._callback(this.takeRecords(),this)}},s.prototype._computeTargetAndRootIntersection=function(e,i,r){if("none"!=window.getComputedStyle(e).display){for(var s,h,c,a,f,d,g,m,v=i,_=p(e),b=!1;!b&&_;){var w=null,y=1==_.nodeType?window.getComputedStyle(_):{};if("none"==y.display)return null;if(_==this.root||9==_.nodeType)if(b=!0,_==this.root||_==t)o&&!this.root?!n||0==n.width&&0==n.height?(_=null,w=null,v=null):w=n:w=r;else{var I=p(_),E=I&&u(I),T=I&&this._computeTargetAndRootIntersection(I,E,r);E&&T?(_=I,w=l(E,T)):(_=null,v=null)}else{var R=_.ownerDocument;_!=R.body&&_!=R.documentElement&&"visible"!=y.overflow&&(w=u(_))}if(w&&(s=w,h=v,c=void 0,a=void 0,f=void 0,d=void 0,g=void 0,m=void 0,c=Math.max(s.top,h.top),a=Math.min(s.bottom,h.bottom),f=Math.max(s.left,h.left),d=Math.min(s.right,h.right),m=a-c,v=(g=d-f)>=0&&m>=0&&{top:c,bottom:a,left:f,right:d,width:g,height:m}||null),!v)break;_=_&&p(_)}return v}},s.prototype._getRootRect=function(){var e;if(this.root&&!d(this.root))e=u(this.root);else{var o=d(this.root)?this.root:t,n=o.documentElement,i=o.body;e={top:0,left:0,right:n.clientWidth||i.clientWidth,width:n.clientWidth||i.clientWidth,bottom:n.clientHeight||i.clientHeight,height:n.clientHeight||i.clientHeight}}return this._expandRectByRootMargin(e)},s.prototype._expandRectByRootMargin=function(t){var e=this._rootMarginValues.map(function(e,o){return"px"==e.unit?e.value:e.value*(o%2?t.width:t.height)/100}),o={top:t.top-e[0],right:t.right+e[1],bottom:t.bottom+e[2],left:t.left-e[3]};return o.width=o.right-o.left,o.height=o.bottom-o.top,o},s.prototype._hasCrossedThreshold=function(t,e){var o=t&&t.isIntersecting?t.intersectionRatio||0:-1,n=e.isIntersecting?e.intersectionRatio||0:-1;if(o!==n)for(var i=0;i<this.thresholds.length;i++){var r=this.thresholds[i];if(r==o||r==n||r<o!=r<n)return!0}},s.prototype._rootIsInDom=function(){return!this.root||f(t,this.root)},s.prototype._rootContainsTarget=function(e){var o=this.root&&(this.root.ownerDocument||this.root)||t;return f(o,e)&&(!this.root||o==e.ownerDocument)},s.prototype._registerInstance=function(){e.indexOf(this)<0&&e.push(this)},s.prototype._unregisterInstance=function(){var t=e.indexOf(this);-1!=t&&e.splice(t,1)},window.IntersectionObserver=s,window.IntersectionObserverEntry=r}function i(t){try{return t.defaultView&&t.defaultView.frameElement||null}catch(t){return null}}function r(t){this.time=t.time,this.target=t.target,this.rootBounds=a(t.rootBounds),this.boundingClientRect=a(t.boundingClientRect),this.intersectionRect=a(t.intersectionRect||{top:0,bottom:0,left:0,right:0,width:0,height:0}),this.isIntersecting=!!t.intersectionRect;var e=this.boundingClientRect,o=e.width*e.height,n=this.intersectionRect,i=n.width*n.height;this.intersectionRatio=o?Number((i/o).toFixed(4)):this.isIntersecting?1:0}function s(t,e){var o,n,i,r=e||{};if("function"!=typeof t)throw new Error("callback must be a function");if(r.root&&1!=r.root.nodeType&&9!=r.root.nodeType)throw new Error("root must be a Document or Element");this._checkForIntersections=(o=this._checkForIntersections.bind(this),n=this.THROTTLE_TIMEOUT,i=null,function(){i||(i=setTimeout(function(){o(),i=null},n))}),this._callback=t,this._observationTargets=[],this._queuedEntries=[],this._rootMarginValues=this._parseRootMargin(r.rootMargin),this.thresholds=this._initThresholds(r.threshold),this.root=r.root||null,this.rootMargin=this._rootMarginValues.map(function(t){return t.value+t.unit}).join(" "),this._monitoringDocuments=[],this._monitoringUnsubscribes=[]}function h(t,e,o,n){"function"==typeof t.addEventListener?t.addEventListener(e,o,n||!1):"function"==typeof t.attachEvent&&t.attachEvent("on"+e,o)}function c(t,e,o,n){"function"==typeof t.removeEventListener?t.removeEventListener(e,o,n||!1):"function"==typeof t.detatchEvent&&t.detatchEvent("on"+e,o)}function u(t){var e;try{e=t.getBoundingClientRect()}catch(t){}return e?(e.width&&e.height||(e={top:e.top,right:e.right,bottom:e.bottom,left:e.left,width:e.right-e.left,height:e.bottom-e.top}),e):{top:0,bottom:0,left:0,right:0,width:0,height:0}}function a(t){return!t||"x"in t?t:{top:t.top,y:t.top,bottom:t.bottom,left:t.left,x:t.left,right:t.right,width:t.width,height:t.height}}function l(t,e){var o=e.top-t.top,n=e.left-t.left;return{top:o,left:n,height:e.height,width:e.width,bottom:o+e.height,right:n+e.width}}function f(t,e){for(var o=e;o;){if(o==t)return!0;o=p(o)}return!1}function p(e){var o=e.parentNode;return 9==e.nodeType&&e!=t?i(e):(o&&o.assignedSlot&&(o=o.assignedSlot.parentNode),o&&11==o.nodeType&&o.host?o.host:o)}function d(t){return t&&9===t.nodeType}}();
  !function(){if("function"==typeof window.CustomEvent)return!1;window.CustomEvent=function(n,t){t=t||{bubbles:!1,cancelable:!1,detail:null};var e=document.createEvent("CustomEvent");return e.initCustomEvent(n,t.bubbles,t.cancelable,t.detail),e}}();

  var langify = window.langify || {};
  var customContents = {};
  var customContents_image = {};
  var langifySettingsOverwrites = {};
  var defaultSettings = {
    debug: false,               // BOOL : Enable/disable console logs
    observe: true,              // BOOL : Enable/disable the entire mutation observer (off switch)
    observeLinks: false,
    observeImages: true,
    observeCustomContents: true,
    maxMutations: 5,
    timeout: 100,               // INT : Milliseconds to wait after a mutation, before the next mutation event will be allowed for the element
    linksBlacklist: [],
  };

  if(langify.settings) { 
    langifySettingsOverwrites = Object.assign({}, langify.settings);
    langify.settings = Object.assign(defaultSettings, langify.settings); 
  } else { 
    langify.settings = defaultSettings; 
  }

  {%- if themeSettings -%}
    langify.settings = Object.assign(langify.settings, {{ themeSettings | json }}, langifySettingsOverwrites);
  {% endif %}

  langify.settings.theme = {{ themeSettings | json }};
  langify.settings.switcher = {{ switcherSettings | json }};

  langify.locale = langify.locale || {
    primary: {{ request.locale.primary | json }},
    iso_code: {{ request.locale.iso_code | json }},
    root_url: {{ request.locale.root_url | json }},
    language_id: {{ current_language | json }},
    shop_url: {{ shop.url | json }},
    domain_feature_enabled: {% if shop.metafields.langify_v2.cross_domain_links.type %}{{ shop.metafields.langify_v2.cross_domain_links }}{% else %}{{ shop.metafields.langify_v2.cross_domain_links | json }}{% endif %},
    languages: [
    {%- for language in languages -%}
      {
        iso_code: {{ language.iso_code | json }},
        root_url: {{ language.root_url | json }},
        domain: {{ language.domain | json }},
      },
    {%- endfor -%}
    ],
    {%- comment -%}
    countries: [
    {%- for country in countries -%}
      {
        currency: {
          name: {{ country.currency.name | json }},
          iso_code: {{ country.currency.iso_code | json }},
          symbol: {{ country.currency.symbol | json }}
        },
        iso_code: {{ country.iso_code | json }},
        name: {{ country.name | json }},
        unit_system: {{ country.unit_system | json }}
      },
    {%- endfor -%}
    ]
    {%- endcomment -%}
  };

  // Disable link correction ALWAYS on languages with mapped domains
  {% if supported_4_letter_codes contains request.locale.iso_code %}
    {% assign used_request_locale_iso_code = request.locale.iso_code %}
  {% else %}
    {% assign used_request_locale_iso_code = request.locale.iso_code | slice: 0, 2 %}
  {% endif %}
  if(langify.locale.root_url == '/') {
    langify.settings.observeLinks = false;
  }

  window.addEventListener('DOMContentLoaded', function() {
    langify.helper = new LyHelper();

    // Custom Contents Section
    {%- capture customs -%}{%- render 'ly-get-customs' with global_strict_mode: true -%}{%- endcapture -%}
    {%- assign customs = customs | split : '[ly_custom]' -%}
    {% for custom in customs %}
    {%- assign used_custom = custom | split: '[ly_custom_split]' -%}
    {%- assign src = used_custom | first -%}
    {%- assign dst = used_custom | last -%}
    {%- if dst -%}
    customContents[{{ src | json }}.toLowerCase().trim().replace(/(\r\n|\n|\r)/gim," ").replace(/\s+/g," ")] = '{{ dst | replace: "\", "\\\" |  replace: "'", "\\'" }}';
    {%- endif -%}
    {% endfor %}

    // Images Section
    {% for custom in shop.metafields.image %}
    {%- assign src = custom | last -%}
    {%- assign key = custom | first -%}
    {%- assign key = key | split: "ly" | last -%}
    {%- assign translation_namespace = current_language | append: "img" -%}
    {%- assign translation_key = "id" | append: key -%}
    {%- if shop.metafields[translation_namespace][translation_key] -%}
    {%- assign translation = shop.metafields[translation_namespace][translation_key] %}
    customContents_image[langify.helper.extractImageObject({{ src | append: '' | json }}.toLowerCase()).file] = langify.helper.extractImageObject({{ translation | replace: "\", "\\\" |  replace: "'", "\\'"  | json}});
    {%- endif %}
    {% endfor %}


    var langifyInstance = new Langify(langify.settings, langify.locale);
  });
</script>