{%- comment -%}Version 4.1.0{%- endcomment -%}
{%- comment -%}
  !!! IMPORTANT !!!
  This file has been automatically created by Lang<PERSON>.
  Please don't edit this file, as all changes will be overwritten.
{%- endcomment -%}

{%- assign key = 'ly_switcher_data_' | append: theme.id -%}
{%- if shop.metafields.global[key].type -%}{%- assign switcherSettings = shop.metafields.global[key].value -%}{%- else -%}{%- assign switcherSettings = shop.metafields.global[key] -%}{%- endif -%}

{%- assign languages = localization.available_languages -%}
{%- if shop.metafields.langify_v2.cross_domain_links and switcherSettings.languages.size > 1 -%}
  {%- assign languages = switcherSettings.languages -%} 
{%- endif -%}

{%- if switcherSettings -%}
  {%- assign languageDetection = switcherSettings.languageDetection -%}
  {%- assign languageDetectionDefault = switcherSettings.languageDetectionDefault -%}
  {%- assign customCSS = switcherSettings.customCSS -%}
  {%- assign breakpoints = switcherSettings.breakpoints -%}
  {%- assign recommendation = switcherSettings.recommendation -%}
  {%- assign recommendation_enabled = switcherSettings.recommendation_enabled -%}
  {%- assign recommendation_type = switcherSettings.recommendation_type -%}
  {%- assign recommendation_switcher_key = switcherSettings.recommendation_switcher_key -%}
  {%- assign recommendation_strings = switcherSettings.recommendation_strings -%}
{%- else -%}
  {%- assign type = 'custom' -%}
  {%- assign show_name = true -%}
  {%- assign show_custom_name = false -%}
  {%- assign show_iso_code = false -%}
  {%- assign rectangle_icons = true -%}
  {%- assign square_icons = false -%}
  {%- assign round_icons = false -%}
  {%- assign is_transparent = false -%}
  {%- assign is_dark = false -%}
  {%- assign position = 'fixed' -%}
  {%- assign corner = 'bottom_right' -%}
  {%- assign h_space = 0 -%}
  {%- assign v_space = 0 -%}
  {%- assign h_item_space = 0 -%}
  {%- assign v_item_space = 0 -%}
  {%- assign breakpoints = 'default' -%}
  {%- assign arrow_size = 100 -%}
  {%- assign arrow_width = 1 -%}
  {%- assign arrow_filled = false -%}
  {%- assign fontsize = 14 -%}
  {%- assign text_color = '#000000' -%}
  {%- assign bg_color = '#ffffff' -%}
{%- endif -%}

{%- assign current_root_url = request.locale.root_url | downcase | append: '/' -%}
{%- assign path = request.path | split: "'" -%}
{%- assign unlocalized_path = path[0] | downcase | replace_first: current_root_url, '/' -%}
{%- if unlocalized_path == request.locale.root_url -%}
  {%- assign unlocalized_path = '' -%}
{%- endif -%}


{%- for breakpoint in breakpoints -%}
  {%- capture switcher -%}

    {%- if switcherSettings -%}
      {%- assign switcherData = breakpoint.config -%}
      {%- assign type = switcherData.type -%}
      {%- assign show_name = switcherData.show_name -%}
      {%- assign show_custom_name = switcherData.show_custom_name -%}
      {%- assign show_iso_code = switcherData.show_iso_code -%}
      {%- assign rectangle_icons = switcherData.rectangle_icons -%}
      {%- assign square_icons = switcherData.square_icons -%}
      {%- assign round_icons = switcherData.round_icons -%}
      {%- assign is_transparent = switcherData.is_transparent -%}
      {%- assign is_dropup = switcherData.is_dropup -%}
      {%- assign is_dark = switcherData.is_dark -%}
      {%- assign position = switcherData.position -%}
      {%- assign corner = switcherData.corner -%}
      {%- assign h_space = switcherData.h_space -%}
      {%- assign v_space = switcherData.v_space -%}
      {%- assign h_item_space = switcherData.h_item_space -%}
      {%- assign v_item_space = switcherData.v_item_space -%}
      {%- assign h_item_padding = switcherData.h_item_padding -%}
      {%- assign v_item_padding = switcherData.v_item_padding -%}
      {%- assign fontsize = switcherData.fontsize -%}
      {%- assign text_color = switcherData.text_color -%}
      {%- assign bg_color = switcherData.bg_color -%}
      {%- assign border_color = switcherData.border_color -%}
      {%- assign border_width = switcherData.border_width -%}
      {%- assign border_radius = switcherData.border_radius -%}
      {%- assign arrow_size = switcherData.arrow_size -%}
      {%- assign arrow_width = switcherData.arrow_width -%}
      {%- assign arrow_filled = switcherData.arrow_filled -%}
      {%- assign show_currency_selector = switcherData.show_currency_selector -%}
      {%- assign currency_switcher_enabled = switcherData.currency_switcher_enabled -%}
      {%- assign country_switcher_enabled = switcherData.country_switcher_enabled -%}
    {%- endif -%}

    {% capture arrow %}
      {%- assign scalefactor = arrow_size | times: 0.01 -%}
      {%- assign realsize = fontsize | times: scalefactor -%}
      {%- assign quarter = realsize | divided_by: 4 -%}
      <div class="ly-arrow {% if is_dark %}ly-arrow-white{% else %}ly-arrow-black{% endif %} {% if arrow_filled %}caret{% else %}stroke{% endif %}" style="vertical-align: middle; width: {{ realsize }}px; height: {{ realsize }}px;">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 {{ realsize }} {{ realsize }}" height="{{ realsize }}px" width="{{ realsize }}px" style="position: absolute;">
          <path d="M{{ arrow_width | plus: 0 }} {{ arrow_width | plus: quarter }} L{{ realsize | divided_by: 2 }} {{ realsize | divided_by: 2 | plus: quarter }} L{{ realsize | minus: arrow_width }} {{ arrow_width | plus: quarter }}" fill="{% if arrow_filled %}{{ text_color }}{% else %}transparent{% endif -%}" stroke="{% if arrow_filled %}transparent{% else %}{{ text_color }}{% endif -%}" stroke-width="{% if arrow_filled %}0{% else %}{{ arrow_width }}px{% endif -%}"/>
        </svg>
      </div>
      {%- if recommendation_enabled and breakpoint.key == recommendation_switcher_key -%}
        {%- assign realsize = recommendation.recommendation_fontsize -%}
        {%- assign quarter = realsize | divided_by: 4 -%}        
        <div class="ly-arrow recommendation {% if is_dark %}ly-arrow-white{% else %}ly-arrow-black{% endif %} {% if arrow_filled %}caret{% else %}stroke{% endif %}" style="vertical-align: middle; width: {{ realsize }}px; height: {{ realsize }}px;">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 {{ realsize }} {{ realsize }}" height="{{ realsize }}px" width="{{ realsize }}px" style="position: absolute;">
            <path d="M{{ arrow_width | plus: 0 }} {{ arrow_width | plus: quarter }} L{{ realsize | divided_by: 2 }} {{ realsize | divided_by: 2 | plus: quarter }} L{{ realsize | minus: arrow_width }} {{ arrow_width | plus: quarter }}" fill="{% if arrow_filled %}{{ text_color }}{% else %}transparent{% endif -%}" stroke="{% if arrow_filled %}transparent{% else %}{{ text_color }}{% endif -%}" stroke-width="{% if arrow_filled %}0{% else %}{{ arrow_width }}px{% endif -%}"/>
          </svg>
        </div>
      {%- endif -%}
    {% endcapture %}

    {%- capture border_style -%}{% if border_width %} border-style: solid; border-width: {{ border_width }}px; border-radius: {{ border_radius }}px; border-color: {{ border_color }};{% endif %}{%- endcapture -%}
    {%- capture spacing_style -%}{% if v_space or h_space %} margin: {{ v_space }}px {{ h_space }}px;{% endif %}{%- endcapture -%}
    {%- capture item_spacing_style -%}{% if v_item_space or h_item_space %} margin: {{ v_item_space }}px {{ h_item_space }}px;{% endif %}{%- endcapture -%}
    {%- capture item_padding_style -%}{% if v_item_padding or h_item_padding %} padding: {{ v_item_padding }}px {{ h_item_padding }}px;{% endif %}{%- endcapture -%}

    <div data-breakpoint="{{ breakpoint.key }}" class="ly-switcher-wrapper ly-breakpoint-{{ forloop.index }} {{ position }} {% if position != 'custom' %}{{ corner }}{% else %}ly-custom{% endif %}{% if is_transparent %} ly-is-transparent{% endif %} ly-hide" style="font-size: {{ fontsize }}px; {% if position != 'custom' %}margin: {{ v_space }}px {{ h_space }}px; {% endif %}">

      {%- if country_switcher_enabled and type != 'none' -%}
        {%- if type == 'native_select' -%}
          <select aria-label="Selected country: {{ localization.country.name }}" aria-description="Country selector" name="country_code" class="ly-country-switcher ly-native-select" style="{{ item_spacing_style }} {{ item_padding_style }} {{ border_style }}">
            {% for country in localization.available_countries %}
              <option data-currency-code="{{ country.currency.iso_code }}" data-country-code="{{ country.iso_code }}" value="{{ country.iso_code }}"{% if country == localization.country %} selected="selected"{% endif %}>
                {{ country.name }} ({{ country.currency.iso_code }} {{ country.currency.symbol }})
              </option>
            {% endfor %}
          </select>
        {%- else -%}
          <div aria-label="Selected country: {{ localization.country.name }}" aria-description="Country selector" data-dropup="{% if is_dropup %}true{% else %}false{% endif %}" onclick="langify.switcher.toggleSwitcherOpen(this)" class="ly-country-switcher ly-custom-dropdown-switcher{% unless is_dark %} ly-bright-theme{% endunless %}{% if is_transparent %} ly-is-transparent{% endif %}{% if is_dropup %} ly-is-dropup{% endif %}" style="{{ item_spacing_style }}">
            <span role="button" aria-expanded="false" aria-controls="countrySwitcherList-{{ forloop.index }}" class="ly-custom-dropdown-current" style="{% unless is_transparent %}background: {{ bg_color }}; {% endunless %}color: {{ text_color }}; {{ item_padding_style }} {{ border_style }}">
              <span class="ly-custom-dropdown-current-inner ly-custom-dropdown-current-inner-text">
                {{ localization.country.name }} ({{ localization.country.currency.iso_code }} {{ localization.country.currency.symbol }})
              </span>
              {{ arrow }}
            </span>
            <ul id="countrySwitcherList-{{ forloop.index }}" role="list" class="ly-custom-dropdown-list ly-is-open {% unless is_dark %}ly-bright-theme{% endunless %}" style="{% unless is_transparent %}background: {{ bg_color }}; {% endunless %}color: {{ text_color }}; {{ border_style }}">
              {%- assign countries = localization.available_countries | reverse -%}
              {% for country in countries %}
                <li key="{{ country.iso_code }}-{{ country.currency.iso_code }}"{% if country == localization.country %} class="current"{% endif %} style="{{ item_padding_style }}" tabindex="-1">
                  <a class="ly-custom-dropdown-list-element" href="#" data-currency-code="{{ country.currency.iso_code }}" data-country-code="{{ country.iso_code }}"><span class="ly-inner-text">{{ country.name }} ({{ country.currency.iso_code }} {{ country.currency.symbol }})</span></a>
                </li>
              {% endfor %}
            </ul>
          </div>
        {%- endif -%}
      {%- endif -%}
      
      {%- case type -%}
      {%- when 'custom' -%}
        <div aria-label="Selected language: {{ request.locale.name }}" aria-description="Language selector" data-dropup="{% if is_dropup %}true{% else %}false{% endif %}" onclick="langify.switcher.toggleSwitcherOpen(this)" class="ly-languages-switcher ly-custom-dropdown-switcher{% unless is_dark %} ly-bright-theme{% endunless %}{% if is_transparent %} ly-is-transparent{% endif %}{% if is_dropup %} ly-is-dropup{% endif %}" style="{{ item_spacing_style }}">
          <span role="button" aria-expanded="false" aria-controls="languagesSwitcherList-{{ forloop.index }}" class="ly-custom-dropdown-current" style="{% unless is_transparent %}background: {{ bg_color }}; {% endunless %}color: {{ text_color }}; {{ border_style }} {{ item_padding_style }}">
            {%- if rectangle_icons or square_icons or round_icons -%}
              <i class="ly-icon ly-flag-icon ly-flag-icon-{{ localization.language.iso_code }}{% if square_icons %} ly-flag-squared{% endif %}{% if round_icons %} ly-flag-rounded{% endif %}"></i>
            {%- endif -%}
            {%- if show_name or show_custom_name or show_iso_code -%}
              <span class="ly-custom-dropdown-current-inner ly-custom-dropdown-current-inner-text">
                {%- if show_name %}{{ localization.language.name }}{% endif -%}
                {%- if show_custom_name -%}{% for lang in switcherSettings.languages %}{% if lang.iso_code == localization.language.iso_code %}{% if lang.custom_name != false and lang.custom_name != '' %}{{ lang.custom_name }}{% else %}{{ localization.language.name }}{% endif %}{% endif %}{% endfor %}{%- endif -%}
                {%- if show_iso_code %}{{ localization.language.iso_code | upcase }}{% endif -%}
              </span>
            {%- endif -%}

            {{ arrow }}
          </span>
          <ul id="languagesSwitcherList-{{ forloop.index }}" role="list" class="ly-custom-dropdown-list ly-is-open {% unless is_dark %}ly-bright-theme{% endunless %}" style="{% unless is_transparent %}background: {{ bg_color }}; {% endunless %}color: {{ text_color }}; {{ border_style }}">
            {%- assign flippedLanguages = languages | reverse -%}
            {%- for language in flippedLanguages -%}
              {%- unless language.published == 'false' -%}
                {%- assign current_root_url = request.locale.root_url | downcase | append: '/' -%}{%- assign path = request.path | split: "'" -%}{%- assign unlocalized_path = path[0] | downcase | replace_first: current_root_url, '/' -%}{%- if unlocalized_path == request.locale.root_url -%}{%- assign unlocalized_path = '' -%}{%- endif -%}
                <li key="{{language.iso_code}}" {% if language.iso_code == localization.language.iso_code %}class="current"{% endif %} style="color: {{ text_color }}; {{ item_spacing_style }}{{ item_padding_style }}" tabindex="-1">
                  <a class="ly-custom-dropdown-list-element ly-languages-switcher-link{% unless is_dark %} ly-bright-theme{% endunless %}{% if request.locale.iso_code == language.iso_code %} current_lang{% endif %}" href="{% if language.domain %}{{ 'https://' | append: language.domain }}{% unless language.iso_code contains '__custom' %}{{ unlocalized_path }}{% endunless %}{% else %}#{% endif %}" data-language-code="{{language.iso_code}}" data-ly-locked="true">
                    {%- if rectangle_icons or square_icons or round_icons -%}
                      <i class="ly-icon ly-flag-icon ly-flag-icon-{{ language.iso_code }}{% if square_icons %} ly-flag-squared{% endif %}{% if round_icons %} ly-flag-rounded{% endif %}"></i>
                    {%- endif -%}
                    {%- if show_name or show_custom_name or show_iso_code -%}
                      <span class="ly-custom-dropdown-list-element-right">
                        {%- if show_name %}{{ language.name }}{%- endif -%}
                        {%- if show_custom_name -%}{% for lang in switcherSettings.languages %}{% if lang.iso_code == language.iso_code %}{% if lang.custom_name != false and lang.custom_name != '' %}{{ lang.custom_name }}{% else %}{{ language.name }}{% endif %}{% endif %}{% endfor %}{%- endif -%}
                        {%- if show_iso_code %}{{ language.iso_code | upcase }}{%- endif -%}
                      </span>
                    {%- endif -%}
                  </a>
                </li>
              {%- endunless -%}
            {%- endfor -%}
          </ul>
        </div>
      {%- when 'native_select' -%}
        <select aria-label="Selected language: {{ request.locale.name }}" aria-description="Language selector" data-breakpoint="{{ breakpoint.key }}" class="ly-languages-switcher ly-native-select {% unless is_dark %} ly-bright-theme{% endunless %}{% if is_transparent %} ly-is-transparent{% endif %}" style="{% unless is_transparent %}background: {{ bg_color }}; {% endunless %}color: {{ text_color }}; {{ item_spacing_style }} {{ item_padding_style }} {{ border_style }}">
          {%- for language in languages -%}
            {%- unless language.published == 'false' -%}
              {%- assign current_root_url = request.locale.root_url | downcase | append: '/' -%}{%- assign path = request.path | split: "'" -%}{%- assign unlocalized_path = path[0] | downcase | replace_first: current_root_url, '/' -%}{%- if unlocalized_path == request.locale.root_url -%}{%- assign unlocalized_path = '' -%}{%- endif -%}
              <option data-language-code="{{language.iso_code}}"{% if language.domain %} data-domain="{{ 'https://' | append: language.domain }}{% unless language.iso_code contains '__custom' %}{{ unlocalized_path }}{% endunless %}"{% endif %}{% if request.locale.iso_code == language.iso_code %} selected="selected"{% endif %} class="{% if request.locale.iso_code == language.iso_code %}current_lang{% endif %}" value="{{language.iso_code}}" style="{{ item_padding_style }}">
                {%- if show_name %}{{ language.name }}{% endif -%}
                {%- if show_custom_name -%}{% for lang in switcherSettings.languages %}{% if lang.iso_code == language.iso_code %}{% if lang.custom_name != false and lang.custom_name != '' %}{{ lang.custom_name }}{% else %}{{ language.name }}{% endif %}{% endif %}{% endfor %}{%- endif -%}
                {%- if show_iso_code %}{{ language.iso_code | upcase }}{% endif -%}
              </option>
            {%- endunless -%}
          {%- endfor -%}
        </select>
      {%- when 'popup' -%}
        <div aria-label="Selected language: {{ request.locale.name }}" aria-description="Language selector" data-breakpoint="{{ breakpoint.key }}" class="ly-languages-switcher ly-popup-switcher{% unless is_dark %} ly-bright-theme{% endunless %}{% if is_transparent %} ly-is-transparent{% endif %}" style="font-size: {{ fontsize }}px; color: {{ text_color }};">
          <div class="ly-popup-current" onclick="langify.switcher.togglePopupOpen(this)" style="{% unless is_transparent %}background: {{ bg_color }}; {% endunless %} {{ item_padding_style }}{{ border_style }}">
            {%- if rectangle_icons or square_icons or round_icons -%}
              <i class="ly-icon ly-flag-icon ly-flag-icon-{{ localization.language.iso_code }}{% if square_icons %} ly-flag-squared{% endif %}{% if round_icons %} ly-flag-rounded{% endif %}"></i>
            {%- endif -%}
            {%- if show_name or show_custom_name or show_iso_code -%}
              <span class="ly-custom-dropdown-current-inner ly-custom-dropdown-current-inner-text">
                {%- if show_name %}{{ localization.language.name }}{% endif -%}
                {%- if show_custom_name -%}{% for lang in switcherSettings.languages %}{% if lang.iso_code == localization.language.iso_code %}{% if lang.custom_name != false and lang.custom_name != '' %}{{ lang.custom_name }}{% else %}{{ localization.language.name }}{% endif %}{% endif %}{% endfor %}{%- endif -%}
                {%- if show_iso_code %}{{ localization.language.iso_code | upcase }}{% endif -%}
              </span>
            {%- endif -%}
          </div>
          <div class="ly-popup-modal">        
            <div class="ly-popup-modal-backdrop"></div>
            <div class="ly-popup-modal-content" style="{% unless is_transparent %}background: {{ bg_color }}; {% endunless %} {{ border_style }}">
              <h3>
                Select language 
              </h3>
              <a href="#" class="ly-close" onclick="langify.switcher.togglePopupOpen(this)"><span class="inner"></span></a>
              <ul role="list" data-breakpoint="{{ breakpoint.key }}" style="font-size: {{ fontsize }}px;  {% unless is_transparent %}background: {{ bg_color }}; {% endunless %}color: {{ text_color }};">
              {%- for language in languages -%}
                {%- unless language.published == 'false' -%}
                  {%- assign current_root_url = request.locale.root_url | downcase | append: '/' -%}{%- assign path = request.path | split: "'" -%}{%- assign unlocalized_path = path[0] | downcase | replace_first: current_root_url, '/' -%}{%- if unlocalized_path == request.locale.root_url -%}{%- assign unlocalized_path = '' -%}{%- endif -%}
                  <li key="{{language.iso_code}}" style="{{ item_spacing_style }} {{ item_padding_style }}" tabindex="-1">
                    <a href="{% if language.domain %}{{ 'https://' | append: language.domain }}{% unless language.iso_code contains '__custom' %}{{ unlocalized_path }}{% endunless %}{% else %}#{% endif %}" data-language-code="{{language.iso_code}}" data-ly-locked="true" class="ly-languages-switcher-link{% if used_request_locale_iso_code == language.iso_code %} current_lang{% endif %}" style=" {% unless is_transparent %}background: {{ bg_color }}; {% endunless %}color: {{ text_color }};">
                      {%- if rectangle_icons or square_icons or round_icons -%}<i class="ly-icon ly-flag-icon ly-flag-icon-{{ language.iso_code }}{% if square_icons %} ly-flag-squared{% endif %}{% if round_icons %} ly-flag-rounded{% endif %}"></i>{% endif -%}
                      {%- if show_name %}{{ language.name }}{% endif -%}
                      {%- if show_custom_name -%}{% for lang in switcherSettings.languages %}{% if lang.iso_code == language.iso_code %}{% if lang.custom_name != false and lang.custom_name != '' %}{{ lang.custom_name }}{% else %}{{ language.name }}{% endif %}{% endif %}{% endfor %}{%- endif -%}
                      {%- if show_iso_code %}{{ language.iso_code | upcase }}{% endif -%}
                    </a>
                  </li>
                {%- endunless -%}
              {%- endfor -%}
              </ul>
            </div>		  
          </div>
        </div>
      {%- when 'links_list' -%}
        <ul aria-label="Selected language: {{ request.locale.name }}" aria-description="Language selector" role="list" data-breakpoint="{{ breakpoint.key }}" class="ly-languages-switcher ly-list {% unless is_dark %} ly-bright-theme{% endunless %}{% if is_transparent %} ly-is-transparent{% endif %}" style="{% unless is_transparent %}background: {{ bg_color }}; {% endunless %}color: {{ text_color }}; {{ border_style }}">
          {%- for language in languages -%}
            {%- unless language.published == 'false' -%}
              {%- assign current_root_url = request.locale.root_url | downcase | append: '/' -%}{%- assign path = request.path | split: "'" -%}{%- assign unlocalized_path = path[0] | downcase | replace_first: current_root_url, '/' -%}{%- if unlocalized_path == request.locale.root_url -%}{%- assign unlocalized_path = '' -%}{%- endif -%}
              <li key="{{language.iso_code}}" style="{{ item_spacing_style }} {{ item_padding_style }}" tabindex="-1">
                <a href="{% if language.domain %}{{ 'https://' | append: language.domain }}{% unless language.iso_code contains '__custom' %}{{ unlocalized_path }}{% endunless %}{% else %}#{% endif %}" data-language-code="{{language.iso_code}}" data-ly-locked="true" class="ly-languages-switcher-link{% if used_request_locale_iso_code == language.iso_code %} current_lang{% endif %}" style=" {% unless is_transparent %}background: {{ bg_color }}; {% endunless %}color: {{ text_color }};">
                  {%- if rectangle_icons or square_icons or round_icons -%}<i class="ly-icon ly-flag-icon ly-flag-icon-{{ language.iso_code }}{% if square_icons %} ly-flag-squared{% endif %}{% if round_icons %} ly-flag-rounded{% endif %}"></i>{%- endif -%}
                  {%- if show_name -%}<span class="ly-inner-text">{{ language.name }}</span>{%- endif -%}
                  {%- if show_custom_name -%}<span class="ly-inner-text">{% for lang in switcherSettings.languages %}{% if lang.iso_code == language.iso_code %}{% if lang.custom_name != false and lang.custom_name != '' %}{{ lang.custom_name }}{% else %}{{ language.name }}{% endif %}{% endif %}{% endfor %}</span>{%- endif -%}
                  {%- if show_iso_code -%}<span class="ly-inner-text">{{ language.iso_code | upcase }}</span>{%- endif -%}
                </a>
              </li>
            {%- endunless -%}
          {%- endfor -%}
        </ul>
      {%- when 'links' -%}
        <div aria-label="Selected language: {{ request.locale.name }}" aria-description="Language selector" data-breakpoint="{{ breakpoint.key }}" class="ly-languages-switcher ly-links {% unless is_dark %} ly-bright-theme{% endunless %}" style="color: {{ text_color }};">
          {%- for language in languages -%}
            {%- unless language.published == 'false' -%}
              {%- assign current_root_url = request.locale.root_url | downcase | append: '/' -%}{%- assign path = request.path | split: "'" -%}{%- assign unlocalized_path = path[0] | downcase | replace_first: current_root_url, '/' -%}{%- if unlocalized_path == request.locale.root_url -%}{%- assign unlocalized_path = '' -%}{%- endif -%}
              <a href="{% if language.domain %}{{ 'https://' | append: language.domain }}{% unless language.iso_code contains '__custom' %}{{ unlocalized_path }}{% endunless %}{% else %}#{% endif %}" data-language-code="{{ language.iso_code }}" data-ly-locked="true" class="ly-languages-switcher-link{% if language.iso_code == localization.language.iso_code %} current_lang{% endif %}" style=" {% unless is_transparent %}background: {{ bg_color }}; {% endunless %}color: {{ text_color }}; {{ item_spacing_style }} {{ item_padding_style }} {{ border_style }}" tabindex="-1">
                {%- if rectangle_icons or square_icons or round_icons -%}<i class="ly-icon ly-flag-icon ly-flag-icon-{{ language.iso_code }}{% if square_icons %} ly-flag-squared{% endif %}{% if round_icons %} ly-flag-rounded{% endif %}"></i>{%- endif -%}
                {%- if show_name -%}<span class="ly-inner-text">{{ language.name }}</span>{%- endif -%}
                {%- if show_custom_name -%}<span class="ly-inner-text">{% for lang in switcherSettings.languages %}{% if lang.iso_code == language.iso_code %}{% if lang.custom_name != false and lang.custom_name != '' %}{{ lang.custom_name }}{% else %}{{ language.name }}{% endif %}{% endif %}{% endfor %}</span>{%- endif -%}
                {%- if show_iso_code -%}<span class="ly-inner-text">{{ language.iso_code | upcase }}</span>{%- endif -%}
              </a>
            {%- endunless -%}
          {%- endfor -%}
        </div>
      {%- else -%}
      {%- endcase -%}

      {%- if currency_switcher_enabled and type != 'none' -%}
        {%- if type == 'native_select' -%}
          <select aria-label="Selected currency: {{ cart.currency.iso_code }}" aria-description="Currency selector" name="currency_code" class="ly-currency-switcher ly-native-select" style="{{ item_spacing_style }} {{ item_padding_style }} {{ border_style }}">
            {% for currency in shop.enabled_currencies %}
              {% if currency == cart.currency %}
                <option selected="true" data-currency-code="{{currency.iso_code}}" style="{{ item_padding_style }}">{{currency.iso_code}} {{currency.symbol}}</option>
                {% else %}
                <option data-currency-code="{{currency.iso_code}}" style="{{ item_padding_style }}">{{currency.iso_code}} {{currency.symbol}}</option>
              {% endif %}
            {% endfor %}
          </select>
        {%- else -%}
          <div aria-label="Selected currency: {{ cart.currency.iso_code }}" aria-description="Currency selector" data-dropup="{% if is_dropup %}true{% else %}false{% endif %}" onclick="langify.switcher.toggleSwitcherOpen(this)" class="ly-currency-switcher ly-custom-dropdown-switcher{% unless is_dark %} ly-bright-theme{% endunless %}{% if is_transparent %} ly-is-transparent{% endif %}{% if is_dropup %} ly-is-dropup{% endif %}" style="{{ item_spacing_style }}">
            <span role="button" aria-expanded="false" aria-controls="currencySwitcherList-{{ forloop.index }}" class="ly-custom-dropdown-current" style="{% unless is_transparent %}background: {{ bg_color }}; {% endunless %}color: {{ text_color }}; {{ item_padding_style }} {{ border_style }}">
              <span class="ly-custom-dropdown-current-inner ly-custom-dropdown-current-inner-text">
                <span class="ly-iso-code">{{cart.currency.iso_code}}</span> <span class="ly-symbol">{{cart.currency.symbol}}</span>
              </span>
              {{ arrow }}
            </span>
            <ul id="currencySwitcherList-{{ forloop.index }}" role="list" class="ly-custom-dropdown-list ly-is-open {% unless is_dark %}ly-bright-theme{% endunless %}" style="{% unless is_transparent %}background: {{ bg_color }}; {% endunless %}color: {{ text_color }}; {{ border_style }}">
              {% for currency in shop.enabled_currencies %}
                <li key="{{ currency.iso_code }}"{% if currency == cart.currency %} class="current"{% endif %} style="{{ item_padding_style }}" tabindex="-1">
                  <a class="ly-custom-dropdown-list-element" href="#" data-currency-code="{{currency.iso_code}}"><span class="ly-inner-text"><span class="ly-iso-code">{{currency.iso_code}}</span> <span class="ly-symbol">{{currency.symbol}}</span></span></a>
                </li>
              {% endfor %}
            </ul>
          </div>
        {%- endif -%}
      {%- endif -%}

    </div>
  {%- endcapture -%}

  {{ switcher }}

  {%- if recommendation_enabled and breakpoint.key == recommendation_switcher_key or recommendation_switcher == nil and recommendation_switcher_key == -1 -%}
    {%- capture recommendation_switcher -%}
      <form class="ly-recommendation-form" action="" method="GET" data-ly-locked="true">
        <span {% if recommendation_switcher_key == -1 %}style="display: none;"{% endif %}>{{- switcher -}}</span>
        <button type="submit" style="font-size: {{ recommendation.recommendation_fontsize }}px; background-color: {{ recommendation.recommendation_button_bg_color }}; color: {{ recommendation.recommendation_button_text_color }}; border: none; padding: 0.2em 0.5em;">
          [[button]]
        </button>
      </form>
    {%- endcapture -%}
  {%- endif -%}
{%- endfor -%}


{%- assign recommendation_orientation = recommendation.recommendation_orientation -%}
{%- assign recommendation_backdrop_show = recommendation.recommendation_backdrop_show -%}
{%- assign recommendation_corner = recommendation.recommendation_corner -%}
{%- assign recommendation_banner_corner = recommendation.recommendation_banner_corner -%}
{%- assign recommendation_fontsize = recommendation.recommendation_fontsize -%}
{%- assign recommendation_border_width = recommendation.recommendation_border_width -%}
{%- assign recommendation_border_radius = recommendation.recommendation_border_radius -%}
{%- assign recommendation_text_color = recommendation.recommendation_text_color -%}
{%- assign recommendation_link_color = recommendation.recommendation_link_color -%}
{%- assign recommendation_button_bg_color = recommendation.recommendation_button_bg_color -%}
{%- assign recommendation_button_text_color = recommendation.recommendation_button_text_color -%}
{%- assign recommendation_bg_color = recommendation.recommendation_bg_color -%}
{%- assign recommendation_border_color = recommendation.recommendation_border_color -%}
{%- if recommendation_enabled -%}  
  <div class="ly-recommendation">        
    {%- if recommendation_type == 'popup' -%}
      {%- if recommendation_backdrop_show -%}<div class="ly-popup-modal-backdrop" onclick="langify.recommendation.toggleOpen(this)"></div>{%- endif -%}
      <div class="ly-popup-modal ly-is-open {{ recommendation_corner }}">        
        <div class="ly-popup-modal-content" style="background: {{ recommendation_bg_color }}; border-radius: {{ recommendation_border_radius }}px; border-width: {{ recommendation_border_width }}px; border-color: {{ recommendation_border_color }}; border-style: solid; font-size: {{ recommendation_fontsize }}px;">
          <h3>
            [[recommendation]]
          </h3>
          <a href="#" class="ly-close" onclick="langify.recommendation.toggleOpen(this)"><span class="inner"></span></a>
          {{ recommendation_switcher }}
        </div>		  
      </div>    
    {%- endif -%}
    {%- if recommendation_type == 'banner' -%}
      <div class="ly-banner {{ recommendation_banner_corner }}">        
        <div class="ly-banner-content" style="background: {{ recommendation_bg_color }}; border-radius: {{ recommendation_border_radius }}px; border-width: {{ recommendation_border_width }}px; border-color: {{ recommendation_border_color }}; border-style: solid; font-size: {{ recommendation_fontsize }}px;">
          [[recommendation]] {{ recommendation_switcher }}
          <a href="#" class="ly-close" onclick="langify.recommendation.toggleOpen(this)"><span class="inner"></span></a>
        </div>		  
      </div>    
    {%- endif -%} 
  </div>    
{%- endif -%}

<style>
  {%- if switcherSettings.languages.size > 1 -%}
    {%- for language in switcherSettings.languages -%}
      .ly-languages-switcher ul > li[key="{{ language.iso_code }}"] {
        order: -{{ forloop.index }} !important;
      }
      .ly-popup-modal .ly-popup-modal-content ul > li[key="{{ language.iso_code }}"],
      .ly-languages-switcher.ly-links a[data-language-code="{{ language.iso_code }}"] {
        order: {{ forloop.index }} !important;
      }
    {%- endfor -%}
  {%- endif -%}

  {%- for language in languages -%}
    {%- assign iconPath = 'ly-icon-' | append: language.iso_code | append: '.svg' %}
    .ly-flag-icon-{{ language.iso_code }} { background-image: url('{{ iconPath | asset_url }}'); }
  {%- endfor -%}

  {%- if switcherSettings -%}
    {%- for breakpoint in switcherSettings.breakpoints -%}
      {%- capture minWidth -%}
        {%- if forloop.first == true %}0px{% else %}{% assign prevIndex = forloop.index | minus: 1 %}{{ switcherSettings.breakpoints[prevIndex].key }}px{% endif -%}
      {%- endcapture -%}
      {%- capture maxWidth -%}
        {%- assign maxValue = switcherSettings.breakpoints[forloop.index].key | minus: 1 -%}
        and (max-width: {{ maxValue }}px )
      {%- endcapture %}
      .ly-breakpoint-{{ forloop.index }} { display: none; }
      @media (min-width: {{- minWidth -}}) {% unless forloop.last %}{{- maxWidth -}}{% endunless %} {
        .ly-breakpoint-{{ forloop.index }} { display: inline-block; }

        .ly-recommendation .ly-banner-content, 
        .ly-recommendation .ly-popup-modal-content {
          font-size: {{ breakpoint.config.recommendation_fontsize }}px !important;
          color: {{ breakpoint.config.recommendation_text_color }} !important;
          background: {{ breakpoint.config.recommendation_bg_color }} !important;
          border-radius: {{ breakpoint.config.recommendation_border_radius }}px !important;
          border-width: {{ breakpoint.config.recommendation_border_width }}px !important;
          border-color: {{ breakpoint.config.recommendation_border_color }} !important;
        }
        .ly-recommendation-form button[type="submit"] {
          font-size: {{ breakpoint.config.recommendation_fontsize }}px !important;
          color: {{ breakpoint.config.recommendation_button_text_color }} !important;
          background: {{ breakpoint.config.recommendation_button_bg_color }} !important;
        }
      }
      {% assign parent_loopindex = forloop.index -%}
      {%- assign parent_loopindex0 = forloop.index0 -%}
      {%- for language in languages -%}
        {%- assign iconPath = 'ly-icon-' | append: language.iso_code | append: '.svg' -%}
        {%- if switcherSettings.breakpoints[parent_loopindex0].config.square_icons or switcherSettings.breakpoints[parent_loopindex0].config.round_icons -%}
          {%- assign iconPath = iconPath | replace: '.svg', '_1x1.svg' -%}
        {%- endif %}
        .ly-breakpoint-{{ parent_loopindex }} .ly-flag-icon-{{ language.iso_code }} { background-image: url('{{ iconPath | asset_url }}') !important; }
      {%- endfor -%}
    {%- endfor -%}
  {%- endif %}

  .ly-switcher-wrapper {
    
  }
  .ly-switcher-wrapper.fixed {
    position: fixed;
    z-index: 1000;
  }
  .ly-switcher-wrapper.absolute {
    position: absolute;
    z-index: 1000;
  }
  .ly-switcher-wrapper.fixed.top_left,
  .ly-switcher-wrapper.absolute.top_left {
    top: 0;
    left: 0;
  }
  .ly-switcher-wrapper.fixed.top_right,
  .ly-switcher-wrapper.absolute.top_right {
    top: 0;
    right: 0;
  }
  .ly-switcher-wrapper.fixed.bottom_left,
  .ly-switcher-wrapper.absolute.bottom_left {
    bottom: 0;
    left: 0;
  }
  .ly-switcher-wrapper.fixed.bottom_right,
  .ly-switcher-wrapper.absolute.bottom_right {
    bottom: 0;
    right: 0;
  }
  .ly-switcher-wrapper .shopify-currency-form {
    display: inline-block;
  }


  .ly-currency-switcher {
    font-size: 1em;
  }

  .ly-inner-text {
    margin: 0 0.2em;
    white-space: nowrap;
  }

  .ly-flag-icon {
    background-size: contain;
    background-position: 50%;
    background-repeat: no-repeat;
    position: relative;
    display: inline-block;
    width: 1.33333333em;
    line-height: 1em;
  }
  .ly-flag-icon:before {
    content: '';
    display: inline-block;
  }
  .ly-flag-icon.ly-flag-squared {
    width: 1em;
  }
  .ly-flag-icon.ly-flag-rounded {
    width: 1em;
    border-radius: 50%;
  }
  .ly-languages-switcher {
    white-space: nowrap;
    list-style-type: none;
    display: flex;
    font-size: 1em;
  }
  .ly-languages-switcher-link {
    margin: 0 0.2em;
    cursor: pointer;
    white-space: nowrap;
    text-decoration: none;
    display: inline-block;
  }
  .ly-languages-switcher.ly-list {
    margin: 0;
    padding: 0;
    display: block;
  }
  .ly-languages-switcher.ly-native-select {
    
  }
  .ly-icon {
    margin: 0 0.2em;
  }

  .ly-arrow {
    display: inline-block;
    position: relative;
    vertical-align: middle;
    direction: ltr !important;
    -webkit-transition: transform 0.25s;
    -moz-transition: transform 0.25s;
    transition: transform 0.25s;
    margin: 0 0.2em;
  }
  .ly-custom-dropdown-switcher.ly-is-open .ly-arrow {
    transform: rotate(180deg);
  }
  .ly-custom-dropdown-switcher.ly-arrow.ly-arrow-up {
    transform: rotate(180deg);
  }
  .ly-custom-dropdown-switcher.ly-is-open .ly-arrow.ly-arrow-up {
    transform: rotate(0deg);
  }

  .ly-custom-dropdown-switcher {
    text-align: left;
    display: inline-block;
    position: relative;
    user-select: none;
    cursor: pointer;
  }
  .ly-custom-dropdown-current {
    display: block;
    vertical-align: middle;
  }
  .ly-custom-dropdown-current-inner {
    display: inline-block;
    vertical-align: middle;
  }
  .ly-custom-dropdown-current-inner-image {
    margin-right: 0.25em;
  }
  .ly-custom-dropdown-current-inner-text {
    margin: 0 0.2em;
  }
  .ly-custom-dropdown-list {
    display: none !important;
    list-style: none;
    position: absolute;
    left: 0;
    padding: inherit !important;
    padding: 0 !important;
    margin: 0;
    z-index: 99999;
    text-align: left;
    width: 100%;
    font-size: 1em;
  }
  .ly-custom-dropdown-list-element {
    color: inherit !important;
    display: block;
  }
  .ly-custom-dropdown-list > li {
    margin: 0 !important;
    display: block;
  }
  .ly-custom-dropdown-list-element-left {
    margin-right: 0.5em;
  }
  .ly-custom-dropdown-list-element-right {
    text-align: left;
  }
  .ly-custom-dropdown-list-element-left,
  .ly-custom-dropdown-list-element-right {
    vertical-align: middle;
  }
  .ly-custom-dropdown-switcher .ly-languages-switcher-link {
    margin: 0;
  }
  .ly-languages-switcher-dropdown {
    display: inline-block;
  }
  .ly-languages-links-switcher {
    display: inline-block;
  }
  .ly-languages-links-switcher > .ly-languages-link {
    /*margin-right: 0.5em;*/
    padding: inherit !important;
    color: inherit !important;
  }
  .ly-custom-dropdown-list-element {
    /*padding: inherit !important;*/
    text-decoration: none;
  }
  .ly-is-uppercase {
    text-transform: uppercase;
  }
  .ly-is-transparent.ly-links,
  .ly-is-transparent.ly-list,
  .ly-is-transparent.ly-currency-switcher,
  .ly-is-transparent.ly-custom-dropdown-switcher,
  .ly-is-transparent.ly-custom-dropdown-switcher .ly-custom-dropdown-list {
    background: transparent !important;
  }
  .ly-arrow-up {
    transform: rotateZ(180deg);
  }

  .ly-custom-dropdown-switcher .ly-custom-dropdown-list li.current{
    display: none;
  }
  .ly-custom-dropdown-switcher.ly-is-open .ly-custom-dropdown-list {
    display: flex !important;
    min-width: 100%;
    width: 100%;
    flex-direction: column-reverse;
    right: 0;
    left: auto;
    overflow: auto;
    max-height: 350px;
    max-height: 50vh;
  }

  .ly-is-dropup .ly-custom-dropdown-list {
    bottom: 100%;
  }
  .ly-hide {
    display: none !important;
  }
  body.ly-force-off #preview-bar-iframe,
  body.ly-force-off .ly-languages-switcher {
    display: none !important;
  }

  .ly-close {
    position: absolute;
    opacity: 0.3;
    right: 0;
    top: 0;
    width: 3em;
    height: 3em;
    display: inline-block !important;
    background: rbga(0,0,0,0);
    padding: 1em;
  }
  .ly-close:hover {
    opacity: 1;
  }
  .ly-close .inner {
    transform: translateX(-50%);
    position: relative;
    width: 100%;
    height: 100%;
    display: inline-block;
  }
  .ly-close .inner:before, .ly-close .inner:after {
    position: absolute;
    left: 1em;
    content: ' ';
    height: 1em;
    width: 2px;
    background-color: #333;
  }
  .ly-close .inner:before {
    transform: rotate(45deg);
  }
  .ly-close .inner:after {
    transform: rotate(-45deg);
  }



  .ly-popup-switcher {}
  .ly-popup-switcher .ly-popup-current {
    cursor: pointer;
    background: transparent;
  }
  .ly-popup-switcher .ly-popup-modal {
    position: fixed;
    z-index: 1000;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: none;
    justify-content: center; 
    align-items: center;
  }
  .ly-popup-switcher.ly-is-open .ly-popup-modal {
    display: flex;
  }
  .ly-popup-switcher .ly-popup-modal-backdrop {
    position: fixed;
    width: 100%;
    height: 100%;
    z-index: 0;
    top: 0;
    left: 0;
    background:rgba(0, 0, 0, 0.38);
    cursor: pointer;
  }
  .ly-popup-switcher .ly-popup-modal .ly-popup-modal-content {
    padding: 1em;
    position: absolute;
    z-index: 1000;
  }
  .ly-popup-switcher .ly-popup-modal .ly-popup-modal-content ul {
    padding: 0;
    margin: 12px 0 0 0;
    list-style: none;
    display: flex;
    flex-direction: column;
  }
  .ly-popup-switcher .ly-popup-modal .ly-popup-modal-content ul .ly-languages-switcher-link {
    margin: 0;
    border: solid 1px;
    padding: 1em;
    display: block;
    min-width: 225px;
    margin-top: 1em;
  }
  .ly-popup-switcher .ly-popup-modal .ly-popup-modal-content ul .ly-languages-switcher-link.current_lang {
    display: none;
  }



  /* Recommendation */
  .ly-recommendation {
    display: none;
  }
  .ly-recommendation.ly-is-open {
    display: inline-block;
  }
  .ly-recommendation form {
    display: inline-block;
  }
  .ly-recommendation .ly-arrow,
  .ly-arrow.recommendation {
    display: none;
  }
  .ly-recommendation .ly-arrow.recommendation {
    display: inline-block;
  }
  .ly-recommendation .ly-switcher-wrapper {
    font-size: 1em !important;
  }
  .ly-recommendation .ly-popup-modal .ly-popup-switcher .ly-popup-modal .ly-popup-modal-backdrop {
    display: none;
  }
  .ly-recommendation-form button[type="submit"] {
    cursor: pointer;
  }

  /* Recommendation Banner */
  .ly-recommendation .ly-banner {
    position: fixed;
    z-index: 100000;
    width: 100%;
  }
  .ly-recommendation .ly-banner .ly-banner-content {
    width: 100%;
    padding: 1em 30px 1em 1em;
    box-shadow: 0 0 20px rgba(0,0,0,.25);
  }
  .ly-recommendation .ly-banner.top {
    top: 0;
  }
  .ly-recommendation .ly-banner.bottom {
    bottom: 0;
  }
  .ly-recommendation .ly-banner .ly-banner-content .ly-switcher-wrapper {
    position: relative !important;
    margin: 0 !important;
    display: inline-block !important;
  }
  .ly-recommendation .ly-banner .ly-recommendation-form button[type="submit"] {
    margin-left: 10px;
  }


  /* Recommendation Popup */
  .ly-recommendation .ly-popup-modal {
    position: fixed;
    z-index: 10000;
  }
  .ly-recommendation .ly-popup-modal .ly-switcher-wrapper {
    position: relative !important;
    margin: 0 !important;
    display: inline-block !important;
  }
  .ly-recommendation .ly-popup-modal-backdrop {
    position: fixed;
    width: 100%;
    height: 100%;
    z-index: 1000;
    top: 0;
    left: 0;
    background:rgba(0, 0, 0, 0.38);
  }
  .ly-recommendation .ly-popup-modal .ly-popup-modal-content {
    padding: 1em 30px 1em 1em;
  }
  .ly-recommendation .ly-popup-modal .ly-popup-modal-content .ly-languages-switcher {
    position: relative !important;
    margin: 0 !important;
  }
  .ly-recommendation .ly-popup-modal .ly-popup-modal-content h3 {
    margin: 0 2em 1em 0;
    font-size: inherit;
    position: relative;
  }
  .ly-recommendation .ly-popup-modal.top_left { top: 0; left: 0; margin-right: 0 !important; margin-bottom: 0 !important; padding-right: 0 !important; padding-bottom: 0 !important;}
  .ly-recommendation .ly-popup-modal.top_center { top: 0; left: 50%; transform: translateX(-50%); margin-right: 0 !important; margin-bottom: 0 !important; margin-left: 0 !important; padding-right: 0 !important; padding-bottom: 0 !important; padding-left: 0 !important;}
  .ly-recommendation .ly-popup-modal.top_right { top: 0; right: 0; margin-left: 0 !important; margin-bottom: 0 !important; padding-left: 0 !important; padding-bottom: 0 !important; }
  .ly-recommendation .ly-popup-modal.center_center { top: 50%; left: 50%; transform: translateX(-50%) translateY(-50%); margin: 0 !important; padding: 0 !important; }
  .ly-recommendation .ly-popup-modal.bottom_left { bottom: 0; left: 0; margin-right: 0 !important; margin-top: 0 !important; padding-right: 0 !important; padding-top: 0 !important; }
  .ly-recommendation .ly-popup-modal.bottom_center { bottom: 0; left: 50%; transform: translateX(-50%); margin-right: 0 !important; margin-left: 0 !important; margin-top: 0 !important; padding-right: 0 !important; padding-left: 0 !important; padding-top: 0 !important; }
  .ly-recommendation .ly-popup-modal.bottom_right { bottom: 0; right: 0; margin-left: 0 !important; margin-top: 0 !important; padding-left: 0 !important; padding-top: 0 !important; }





  {% if customCSS and customCSS != '' %}
    {{ customCSS }}
  {% endif %}
</style>