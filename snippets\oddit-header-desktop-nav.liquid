{%- liquid
  unless limit
    assign limit = main_menu.links.size
  endunless
  unless offset
    assign offset = 0
  endunless
-%}

<ul
  class="site-nav site-navigation small--hide"
  {% unless disable_aria %}
    role="navigation" aria-label="Primary"
  {% endunless %}
>
  {%- for link in main_menu.links limit: limit offset: offset -%}
    {%- liquid
      assign has_dropdown = false
      assign is_megamenu = false
      if link.links != blank
        assign has_dropdown = true
        if link.levels > 1
          assign is_megamenu = true
        endif
      endif
    -%}

    <li
      class="site-nav__item site-nav__expanded-item{% if has_dropdown %} site-nav--has-dropdown{% endif %}{% if is_megamenu %} site-nav--is-megamenu{% endif %}"
      {% if has_dropdown %}
        aria-haspopup="true"
      {% endif %}
    >
      <a
        href="{{ link.url }}"
        class="site-nav__link site-nav__link--underline{% if has_dropdown %} site-nav__link--has-dropdown{% endif %} !tw-flex tw-items-center tw-gap-[8px] !tw-text-[14px] !tw-font-medium !tw-font-dm-sans !tw-text-darkblack"
      >
        {{ link.title }}
        {% if has_dropdown != blank %}
          {{ 'icon-down.svg' | inline_asset_content }}
        {% endif %}
      </a>
      {%- if is_megamenu -%}
        {%- assign previous_column_type = '' -%}
        {%- assign animation_column = 1 -%}

        <div class="site-nav__dropdown megamenu text-left">
          <div class="page-width">
            <div class="grid{% if dropdown_alignment == 'center' %} grid--center{% endif %}">
              <div class="grid__item medium-up--one-fifth appear-animation appear-delay-{{ animation_column }}">
                {%- assign animation_column = animation_column | plus: 1 -%}

                {%- for childlink in link.links -%}
                  {%- liquid
                    assign create_new_column = false

                    if childlink.levels > 0 and forloop.index != 1
                      assign create_new_column = true
                    endif

                    if childlink.levels == 0 and previous_column_type == 'full'
                      assign create_new_column = true
                    endif
                  -%}

                  {%- if create_new_column -%}
                    </div>
                    <div class="grid__item medium-up--one-fifth appear-animation appear-delay-{{ animation_column }}">
                      {%- assign animation_column = animation_column | plus: 1 -%}
                  {%- endif -%}

                  {%- if childlink.levels > 0 and childlink.url contains '/collections/' -%}
                    {%- if collections[childlink.object.handle].image != blank -%}
                      <a
                        href="{{ childlink.url }}"
                        class="megamenu__colection-image"
                        aria-label="{{collections[childlink.object.handle].title}}"
                        style="background-image: url({{ collections[childlink.object.handle].image | img_url: '400x' }})"
                      ></a>
                    {%- endif -%}
                  {%- endif -%}

                  <div class="h5">
                    <a href="{{ childlink.url }}" class="site-nav__dropdown-link site-nav__dropdown-link--top-level">
                      {{- childlink.title -}}
                    </a>
                  </div>

                  {%- liquid
                    if childlink.levels > 0
                      assign previous_column_type = 'full'
                    else
                      assign previous_column_type = 'single'
                    endif
                  -%}

                  {%- for grandchildlink in childlink.links -%}
                    <div>
                      <a href="{{ grandchildlink.url }}" class="site-nav__dropdown-link">
                        {{ grandchildlink.title }}
                      </a>
                    </div>
                  {%- endfor -%}
                {%- endfor -%}
              </div>
            </div>
          </div>
        </div>
      {%- elsif has_dropdown -%}
        <ul class="site-nav__dropdown text-left">
          {%- for childlink in link.links -%}
            {%- liquid
              assign has_sub_dropdown = false
              if childlink.links != blank
                assign has_sub_dropdown = true
              endif
            -%}

            <li class="{% if has_sub_dropdown %} site-nav__deep-dropdown-trigger{% endif %}">
              <a
                href="{{ childlink.url }}"
                class="site-nav__dropdown-link site-nav__dropdown-link--second-level{% if has_sub_dropdown %} site-nav__dropdown-link--has-children{% endif %}"
              >
                {{ childlink.title | escape }}
                {%- if has_sub_dropdown -%}
                  <svg
                    aria-hidden="true"
                    focusable="false"
                    role="presentation"
                    class="icon icon--wide icon-chevron-down"
                    viewBox="0 0 28 16"
                  >
                    <path d="M1.57 1.59l12.76 12.77L27.1 1.59" stroke-width="2" stroke="#000" fill="none" fill-rule="evenodd"/>
                  </svg>
                {%- endif -%}
              </a>
              {%- if has_sub_dropdown -%}
                <ul class="site-nav__deep-dropdown">
                  {%- for grandchildlink in childlink.links -%}
                    <li>
                      <a href="{{ grandchildlink.url }}" class="site-nav__dropdown-link">
                        {{- grandchildlink.title | escape -}}
                      </a>
                    </li>
                  {%- endfor -%}
                </ul>
              {%- endif -%}
            </li>
          {%- endfor -%}
        </ul>
      {%- endif -%}
    </li>
  {%- endfor -%}

  {%- for link in second_menu.links -%}
    {%- liquid
      assign has_dropdown = false
      assign is_megamenu = false
      if link.links != blank
        assign has_dropdown = true
        if link.levels > 1
          assign is_megamenu = true
        endif
      endif
    -%}

    <li
      class="site-nav__item site-nav__expanded-item{% if has_dropdown %} site-nav--has-dropdown{% endif %}{% if is_megamenu %} site-nav--is-megamenu{% endif %}"
      {% if has_dropdown %}
        aria-haspopup="true"
      {% endif %}
    >
      <a
        href="{{ link.url }}"
        class="site-nav__link site-nav__link--underline{% if has_dropdown %} site-nav__link--has-dropdown{% endif %} !tw-text-[14px] !tw-font-medium !tw-font-dm-sans !tw-text-darkblack !tw-flex{% if forloop.first %} tw-border-0 tw-border-l tw-border-solid tw-border-darkblack{% endif %}"
      >
        {{ link.title }}
        {% if has_dropdown != blank %}
          {{ 'icon-down.svg' | inline_asset_content }}
        {% endif %}
      </a>
      {%- if is_megamenu -%}
        {%- assign previous_column_type = '' -%}
        {%- assign animation_column = 1 -%}

        <div class="site-nav__dropdown megamenu text-left">
          <div class="page-width">
            <div class="grid{% if dropdown_alignment == 'center' %} grid--center{% endif %}">
              <div class="grid__item medium-up--one-fifth appear-animation appear-delay-{{ animation_column }}">
                {%- assign animation_column = animation_column | plus: 1 -%}

                {%- for childlink in link.links -%}
                  {%- liquid
                    assign create_new_column = false

                    if childlink.levels > 0 and forloop.index != 1
                      assign create_new_column = true
                    endif

                    if childlink.levels == 0 and previous_column_type == 'full'
                      assign create_new_column = true
                    endif
                  -%}

                  {%- if create_new_column -%}
                    </div>
                    <div class="grid__item medium-up--one-fifth appear-animation appear-delay-{{ animation_column }}">
                      {%- assign animation_column = animation_column | plus: 1 -%}
                  {%- endif -%}

                  {%- if childlink.levels > 0 and childlink.url contains '/collections/' -%}
                    {%- if collections[childlink.object.handle].image != blank -%}
                      <a
                        href="{{ childlink.url }}"
                        class="megamenu__colection-image"
                        aria-label="{{collections[childlink.object.handle].title}}"
                        style="background-image: url({{ collections[childlink.object.handle].image | img_url: '400x' }})"
                      ></a>
                    {%- endif -%}
                  {%- endif -%}

                  <div class="h5">
                    <a href="{{ childlink.url }}" class="site-nav__dropdown-link site-nav__dropdown-link--top-level">
                      {{- childlink.title -}}
                    </a>
                  </div>

                  {%- liquid
                    if childlink.levels > 0
                      assign previous_column_type = 'full'
                    else
                      assign previous_column_type = 'single'
                    endif
                  -%}

                  {%- for grandchildlink in childlink.links -%}
                    <div>
                      <a href="{{ grandchildlink.url }}" class="site-nav__dropdown-link">
                        {{ grandchildlink.title }}
                      </a>
                    </div>
                  {%- endfor -%}
                {%- endfor -%}
              </div>
            </div>
          </div>
        </div>
      {%- elsif has_dropdown -%}
        <ul class="site-nav__dropdown text-left">
          {%- for childlink in link.links -%}
            {%- liquid
              assign has_sub_dropdown = false
              if childlink.links != blank
                assign has_sub_dropdown = true
              endif
            -%}

            <li class="{% if has_sub_dropdown %} site-nav__deep-dropdown-trigger{% endif %}">
              <a
                href="{{ childlink.url }}"
                class="site-nav__dropdown-link site-nav__dropdown-link--second-level{% if has_sub_dropdown %} site-nav__dropdown-link--has-children{% endif %}"
              >
                {{ childlink.title | escape }}
                {%- if has_sub_dropdown -%}
                  <svg
                    aria-hidden="true"
                    focusable="false"
                    role="presentation"
                    class="icon icon--wide icon-chevron-down"
                    viewBox="0 0 28 16"
                  >
                    <path d="M1.57 1.59l12.76 12.77L27.1 1.59" stroke-width="2" stroke="#000" fill="none" fill-rule="evenodd"/>
                  </svg>
                {%- endif -%}
              </a>
              {%- if has_sub_dropdown -%}
                <ul class="site-nav__deep-dropdown">
                  {%- for grandchildlink in childlink.links -%}
                    <li>
                      <a href="{{ grandchildlink.url }}" class="site-nav__dropdown-link">
                        {{- grandchildlink.title | escape -}}
                      </a>
                    </li>
                  {%- endfor -%}
                </ul>
              {%- endif -%}
            </li>
          {%- endfor -%}
        </ul>
      {%- endif -%}
    </li>
  {%- endfor -%}
</ul>
