{% comment %}
  Renders a media element for the product gallery.
  Media types include: image, video, external_video and model.
  Accepts:
  - media: {Object} Media Liquid object (required)
  - featured_media: {Object} Media Liquid object (required) - featured media of a given product or variant
{% endcomment %}

{%- liquid
  assign is_featured = false
  if featured_media == media
    assign is_featured = true
  endif

  assign has_video = false
  assign video_type = ''
  assign video_modal = false

  assign image_set = false
  assign image_set_group = ''
  if media.alt contains '#'
    assign image_set_full = media.alt | split: '#' | last
    if image_set_full contains '_'
      assign image_set = true
      assign image_set_group = image_set_full | split: '_' | first
    endif
  endif

  case media.media_type
    when 'external_video'
      assign has_video = true
      assign video_type = media.host
      assign video_id = media.external_id
    when 'video'
      assign has_video = true
      assign video_type = 'mp4'
  endcase

  if has_video and video_style == 'unmuted'
    assign video_modal = true
  endif

  assign media_aspect_ratio = media.aspect_ratio
  assign media_width = media.width
  assign media_height = media.height

  if media.media_type != 'image'
    assign media_width = media.preview_image.width
    assign media_height = media.preview_image.height
  endif
-%}

<div
  class="{{ loopIndex0 }} product-main-two-images first:tw-col-span-2 tw-relative"
  data-index="{{ loopIndex0 }}"
  {% if image_set %}
    data-set-name="{{image_set_group}}"
    data-group="{{image_set_full}}"
  {% endif %}
>
  {% if loopIndex == 1 %}
    {% if section.settings.badge_label != blank %}
      <div class="grid-product__tag grid-product__tag--custom black_friday_label !tw-py-[4px] !tw-px-[11px] !tw-bg-[#EF4444] tw-rounded-[38px] !tw-text-[15px] tw-font-bold tw-text-white tw-uppercase !tw-leading-[normal] tw-tracking-normal !tw-left-[31px] max-lg:!tw-left-[20px] !tw-top-[38px] max-lg:!tw-top-[20px] tw-font-dm-sans">
        {{ section.settings.badge_label }}
      </div>
    {% endif %}
    {% if section.settings.gallery_icon != blank or section.settings.gallery_text != blank %}
      <div class="gallary-icon tw-flex tw-items-center tw-gap-x-[12px] tw-absolute tw-bottom-[29px] tw-right-[24px]">
        {% if section.settings.gallery_icon != blank %}
          <div class="image tw-flex">
            <img
              class="tw-h-[40px] tw-max-w-full"
              src="{{ section.settings.gallery_icon | img_url: 'master' }}"
              alt="{{ section.settings.gallery_icon.alt }}"
            >
          </div>
        {% endif %}
        {% if section.settings.gallery_text != blank %}
          <div class="gallery-text tw-text-[15px] tw-font-medium tw-text-darkblack tw-font-dm-sans tw-leading-[normal] tw-tracking-normal tw-max-w-[110px] tw-w-full">
            {{ section.settings.gallery_text }}
          </div>
        {% endif %}
      </div>
    {% endif %}
  {% endif %}
  <div data-product-image-main class="product-image-main-two-images">
    {%- if media.media_type == 'model' -%}
      {%- liquid
        if media.preview_image.aspect_ratio
          assign media_aspect_ratio = media.preview_image.aspect_ratio
        endif
      -%}
      <div
        class="image-wrap-two-images tw-rounded-[20px] tw-bg-[#F6F6F6]"
        style="height: 0; padding-bottom: {{ 100 | divided_by: media_aspect_ratio }}%;"
        data-product-media-type-model
        data-media-id="{{ section_id }}-{{ media.id }}"
      >
        {{
          media
          | model_viewer_tag:
            image_size: product_zoom_size,
            reveal: 'interaction',
            toggleable: true,
            data-model-id: media.id
        }}
      </div>
      <button class="hide btn btn--circle btn--static product-single__close-media">
        <svg aria-hidden="true" focusable="false" role="presentation" class="icon icon-close" viewBox="0 0 64 64">
          <path d="M19 17.61l27.12 27.13m0-27.12L19 44.74"/>
        </svg>
      </button>
    {%- else -%}
      {%- liquid
        if has_video and video_style == 'unmuted'
          assign media_aspect_ratio = media.preview_image.aspect_ratio
        endif
      -%}
      <div
        class="image-wrap-two-images tw-rounded-[20px] tw-overflow-hidden tw-bg-[#F6F6F6] {% if has_video and video_modal == false %} hide{% endif %}"
        style="height: 0; padding-bottom: {{ 100 | divided_by: media_aspect_ratio }}%;"
      >
        {%- liquid
          assign img_url = media | img_url: '1x1' | replace: '_1x1.', '_{width}x.'
          assign zoom_img_url = media.preview_image | img_url: product_zoom_size
        -%}

        <img
          class="{% if product_zoom_enable %}photoswipe__image{% endif %} lazyload"
          data-photoswipe-src="{{ zoom_img_url }}"
          data-photoswipe-width="{{ media_width }}"
          data-photoswipe-height="{{ media_height }}"
          data-index="{{ loopIndex }}"
          data-src="{{ img_url }}"
          data-widths="[360, 540, 720, 900, 1080]"
          data-aspectratio="{{ media_aspect_ratio }}"
          data-sizes="auto"
          alt="{{ media.alt | escape | split: '#' | first }}"
        >
      </div>

      {%- if has_video and video_modal -%}
        {%- if video_type == 'youtube' -%}
          <a
            href="https://youtube.com/watch?v={{ media.external_id }}"
            class="btn btn--circle btn--large btn--static product-video-trigger"
            data-video-id="{{ media.external_id }}"
          >
            <svg
              aria-hidden="true"
              focusable="false"
              role="presentation"
              class="icon icon-play"
              viewBox="18.24 17.35 24.52 28.3"
            >
              <path fill="#323232" d="M22.1 19.151v25.5l20.4-13.489-20.4-12.011z"/>
            </svg>
          </a>
        {%- endif -%}
        {%- if video_type == 'vimeo' -%}
          <a
            href="https://player.vimeo.com/video/{{ media.external_id }}"
            class="btn btn--circle btn--large btn--static product-video-trigger"
            data-video-id="{{ media.external_id }}"
            data-video-loop="{{ video_looping }}"
          >
            <svg
              aria-hidden="true"
              focusable="false"
              role="presentation"
              class="icon icon-play"
              viewBox="18.24 17.35 24.52 28.3"
            >
              <path fill="#323232" d="M22.1 19.151v25.5l20.4-13.489-20.4-12.011z"/>
            </svg>
          </a>
        {%- endif -%}
        {%- if video_type == 'mp4' -%}
          <button
            type="button"
            class="btn btn--circle btn--large btn--static product-video-trigger product-video-trigger--mp4"
          >
            <svg
              aria-hidden="true"
              focusable="false"
              role="presentation"
              class="icon icon-play"
              viewBox="18.24 17.35 24.52 28.3"
            >
              <path fill="#323232" d="M22.1 19.151v25.5l20.4-13.489-20.4-12.011z"/>
            </svg>
          </button>

          {% comment %}
            Hidden video element that gets placed in the video modal
          {% endcomment %}
          <video
            playsinline
            {% if video_looping %}
              loop
            {% endif %}
            controls
            controlsList="nodownload"
            poster="{{ media.preview_image | img_url: product_image_size }}"
            class="hide product-video-mp4-sound"
          >
            {%- for source in media.sources -%}
              <source src="{{ source.url }}" type="{{ source.mime_type }}">
            {%- endfor -%}
            Your browser does not support the video tag.
          </video>
        {%- endif -%}
      {%- endif -%}

      {%- if has_video -%}
        {%- unless video_modal -%}
          <div
            data-video-type="{{ video_type }}"
            class="product__video-wrapper{% if video_type == 'youtube' or video_type == 'vimeo' %} loading{% endif %}"
            data-video-style="{{ video_style }}"
            style="padding-bottom: {{ 100 | divided_by: media.aspect_ratio }}%;"
          >
            {%- if video_type == 'youtube' or video_type == 'vimeo' -%}
              <div
                id="ProductVideo-{{ section_id }}-{{ loopIndex }}"
                class="product__video"
                data-image-count="{{ product.media.size }}"
                data-video-type="{{ video_type }}"
                data-video-style="{{ video_style }}"
                data-video-loop="{{ video_looping }}"
                data-video-id="{{ video_id }}"
              ></div>
            {%- endif -%}
            {%- if video_type == 'mp4' -%}
              <video
                playsinline
                {% if video_looping %}
                  loop
                {% endif %}
                {% if video_style == 'muted' %}
                  muted
                {% endif %}
                {% if video_style == 'unmuted' %}
                  controls
                {% endif %}
                controlsList="nodownload"
                poster="{{ media.preview_image | img_url: product_image_size }}"
                data-image-count="{{ product.media.size }}"
                data-video-type="{{ video_type }}"
                data-video-style="{{ video_style }}"
                id="ProductVideo-{{ section_id }}-{{ loopIndex }}"
                class="product__video"
              >
                {%- for source in media.sources -%}
                  <source src="{{ source.url }}" type="{{ source.mime_type }}">
                {%- endfor -%}
                Your browser does not support the video tag.
              </video>
            {%- endif -%}
          </div>
        {%- endunless -%}
      {%- endif -%}
    {%- endif -%}
  </div>
</div>
