{%- form 'product', product, id: form_id, class: 'product-single__form' -%}
  {%- liquid
    assign enable_dynamic_buttons = false
    if show_dynamic_checkout and template != 'product.preorder'
      assign enable_dynamic_buttons = true
    endif
  -%}

  {%- if enable_dynamic_buttons -%}
    <div class="payment-buttons">
  {%- endif -%}

  {%- liquid
    assign default_text = 'products.product.add_to_cart' | t
    assign button_text = 'products.product.add_to_cart' | t
    if template contains 'preorder'
      assign default_text = 'products.product.preorder' | t
      assign button_text = 'products.product.preorder' | t
    endif
    unless current_variant.available
      assign button_text = 'products.product.sold_out' | t
    endunless

    if template.suffix == 'oddit' and enable_product_title
      assign button_text = 'Buy ' | append: product.title
    endif
  -%}

  <button
    {% if product.empty? %}
      type="button"
    {% else %}
      type="submit"
    {% endif %}
    name="add"
    data-add-to-cart
    class="btn btn--full add-to-cart !tw-rounded-[40px] !tw-text-[18px] max-md:!tw-text-[16px] !tw-font-bold !tw-font-dm-sans !tw-capitalize !tw-py-[14px] !tw-tracking-normal max-md:!tw-px-[8px] {% if enable_dynamic_buttons and product.selling_plan_groups == empty %} btn--secondary{% endif %}"
    {% unless current_variant.available %}
      disabled="disabled"
    {% endunless %}
  >
    <span data-add-to-cart-text data-default-text="{{ default_text }}"> {{ button_text }} </span>
  </button>

  {%- if enable_dynamic_buttons -%}
    {{ form | payment_button }}
  {%- endif -%}

  {%- if enable_dynamic_buttons -%}
    </div>
  {%- endif -%}

  <div class="shopify-payment-terms product__policies">{{ form | payment_terms }}</div>

  <select name="id" data-product-select class="product-single__variants no-js">
    {%- for variant in product.variants -%}
      {%- if variant.available -%}
        <option
          {% if variant == product.selected_or_first_available_variant %}
            selected="selected"
          {% endif %}
          value="{{ variant.id }}"
        >
          {{ variant.title }} - {{ variant.price | money_with_currency }}
        </option>
      {%- else -%}
        <option disabled="disabled">{{ variant.title }} - {{ 'products.product.sold_out' | t }}</option>
      {%- endif -%}
    {%- endfor -%}
  </select>
{%- endform -%}
