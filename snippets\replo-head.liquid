
    {% if request.page_type == "page" and page.metafields.replo.page_featured_image != blank %}
      <meta property="og:image" content="{{ page.metafields.replo.page_featured_image }}"/>
      <meta property="og:image:secure_url" content="{{ page.metafields.replo.page_featured_image }}"/>
      <meta property="twitter:image" content="{{ page.metafields.replo.page_featured_image }}"/>
    {% endif %}
    {% # theme-check-disable %}
    {% assign request_object = [request.page_type] %}
    {% # theme-check-enable %}
    {% if request_object %}
      {% if request_object.metafields.replo.before_head_end_content != blank %}
        {{ request_object.metafields.replo.before_head_end_content }}
      {% endif %}
      {% if request_object.metafields.replo.before_head_end_styles != blank %}
        {{ request_object.metafields.replo.before_head_end_styles }}
      {% endif %}
    {% endif %}
  