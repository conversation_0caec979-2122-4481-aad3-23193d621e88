{%- liquid
  assign output_tab = true
  if title == blank and content == blank
    assign output_tab = false
  endif
-%} 
{%- if output_tab -%}
  <div class="collapsibles-wrapper collapsibles-wrapper--border-bottom">
    <button type="button"
      class="label collapsible-trigger collapsible-trigger-btn collapsible-trigger-btn--borders collapsible--auto-height{% if force_open %} is-open{% endif %} !tw-capitalize !tw-text-[16px] !tw-text-darkblack !tw-font-dm-sans !tw-font-medium !tw-tracking-normal !tw-py-[12px]" aria-controls="Product-content-{{ id }}"
      {% if force_open %}aria-expanded="true"{% endif %}>
      {{ title }}
      {%- render 'collapsible-icons' -%}
    </button>
    <div id="Product-content-{{ id }}"
      class="collapsible-content collapsible-content--all{% if force_open %} is-open{% endif %}"
      {% if force_open %}style="height: auto;"{% endif %}>
      <div class="collapsible-content__inner rte !tw-font-dm-sans *:!tw-text-[14px] *:!tw-text-black">
        {{ content }}
      </div>
    </div>
  </div>
{%- endif -%}
