/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "01afcd77-137b-4281-b4df-77cff90c02f2": {
      "type": "promo-grid",
      "disabled": true,
      "blocks": {
        "template--16654241300728__01afcd77-137b-4281-b4df-77cff90c02f2-1675960657572faa1b-0": {
          "type": "advanced",
          "settings": {
            "subheading": "Gard pro®",
            "heading": "Smartwatches",
            "textarea": "Ontdek onze nieuwe stijlvolle smartwatch die is ontworpen voor elk fitnessniveau en doel.",
            "cta_text1": "",
            "cta_link1": "",
            "cta_text2": "",
            "cta_link2": "",
            "image": "shopify://shop_images/Untitled_design_-_2022-12-31T003302.245_2.png",
            "video_url": "",
            "width": "100",
            "height": 500,
            "text_size": 100,
            "text_align": "vertical-center horizontal-left",
            "focal_point": "center",
            "color_accent": "rgba(0,0,0,0)",
            "boxed": false,
            "framed": false
          }
        }
      },
      "block_order": [
        "template--16654241300728__01afcd77-137b-4281-b4df-77cff90c02f2-1675960657572faa1b-0"
      ],
      "settings": {
        "full_width": false,
        "gutter_size": 0,
        "space_above": true,
        "space_below": false
      }
    },
    "collection-header": {
      "type": "collection-header",
      "settings": {
        "enable": true,
        "collection_image_enable": false,
        "parallax": true,
        "parallax_direction": "left"
      }
    },
    "main-collection": {
      "type": "main-collection",
      "blocks": {
        "collection_description": {
          "type": "collection_description",
          "settings": {}
        },
        "subcollections": {
          "type": "subcollections",
          "settings": {
            "subcollections_per_row": 5
          }
        },
        "product_grid": {
          "type": "product_grid",
          "settings": {
            "enable_collection_count": true,
            "per_row": 3,
            "rows_per_page": 7,
            "mobile_flush_grid": false
          }
        }
      },
      "block_order": [
        "collection_description",
        "subcollections",
        "product_grid"
      ],
      "settings": {
        "enable_sidebar": true,
        "collapsed": true,
        "filter_style": "drawer",
        "enable_color_swatches": true,
        "enable_sort": true
      }
    },
    "1684092788e585dabe": {
      "type": "apps",
      "settings": {
        "full_width": false,
        "space_around": true
      }
    }
  },
  "order": [
    "01afcd77-137b-4281-b4df-77cff90c02f2",
    "collection-header",
    "main-collection",
    "1684092788e585dabe"
  ]
}
