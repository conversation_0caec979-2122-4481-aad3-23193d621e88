{"sections": {"main": {"type": "main-page", "disabled": true, "settings": {}}, "advanced_content_QJRx98": {"type": "advanced-content", "blocks": {"liquid_HFWMWW": {"type": "liquid", "settings": {"code": "<script defer src=\"https://help-center.gorgias.help/api/help-centers/loader.js?v=2\" data-gorgias-loader-help-center data-gorgias-help-center-uid=\"szuq63rp\"></script>", "width": "100%", "alignment": "center"}}}, "block_order": ["liquid_HFWMWW"], "settings": {"full_width": true, "space_around": true}}, "advanced_content_xnWA9T": {"type": "advanced-content", "blocks": {"liquid_JUWBLt": {"type": "liquid", "settings": {"code": "<!--<PERSON><PERSON><PERSON> Chat Widget Start-->\n<script id=\"gorgias-chat-widget-install-v3\" \n    src=\"https://config.gorgias.chat/bundle-loader/01HD9ACAD0HPBTFNCN8YWS9X31\">\n</script>\n<script>\n    var initGorgiasChatPromise = (window.GorgiasChat)\n        ? window.GorgiasChat.init()\n        : new Promise(function (resolve) {\n              window.addEventListener('gorgias-widget-loaded', function () {\n                  resolve();\n              });\n          });\n\n    initGorgiasChatPromise.then(function () {\n        GorgiasChat.setCustomBusinessHours({\n            timezone: \"Europe/Berlin\",\n            businessHours: [\n                {\n                    days: [1,2,3,4,5,6,7],\n                    fromTime: \"15:00\",\n                    toTime: \"23:00\"\n                }\n            ]\n        });\n    });\n</script>\n<!--Gorg<PERSON> Chat Widget End-->", "width": "50%", "alignment": "center"}}}, "block_order": ["liquid_JUWBLt"], "settings": {"full_width": false, "space_around": true}}}, "order": ["main", "advanced_content_QJRx98", "advanced_content_xnWA9T"]}