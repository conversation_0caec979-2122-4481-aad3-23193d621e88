/*
 * ------------------------------------------------------------
 * IMPORTANT: The contents of this file are auto-generated.
 *
 * This file may be updated by the Shopify admin theme editor
 * or related systems. Please exercise caution as any changes
 * made to this file may be overwritten.
 * ------------------------------------------------------------
 */
{
  "sections": {
    "main": {
      "type": "main-product",
      "blocks": {
        "price": {
          "type": "price",
          "settings": {}
        },
        "quantity_selector": {
          "type": "quantity_selector",
          "settings": {}
        },
        "buy_buttons": {
          "type": "buy_buttons",
          "settings": {}
        },
        "description": {
          "type": "description",
          "settings": {}
        },
        "variant_picker": {
          "type": "variant_picker",
          "settings": {}
        },
        "inventory_status": {
          "type": "inventory_status",
          "settings": {}
        },
        "share": {
          "type": "share",
          "settings": {}
        },
        "sales_point": {
          "type": "sales_point",
          "settings": {}
        },
        "tab": {
          "type": "tab",
          "settings": {}
        },
        "text": {
          "type": "text",
          "settings": {}
        },
        "separator": {
          "type": "separator",
          "settings": {}
        },
        "contact": {
          "type": "contact",
          "settings": {}
        }
      },
      "block_order": [
        "price",
        "separator",
        "variant_picker",
        "sales_point",
        "inventory_status",
        "buy_buttons",
        "description",
        "tab",
        "contact",
        "share"
      ],
      "settings": {}
    },
    "sub": {
      "type": "product-full-width",
      "settings": {}
    },
    "product-recommendations": {
      "type": "product-recommendations",
      "settings": {}
    },
    "recently-viewed": {
      "type": "recently-viewed",
      "settings": {}
    },
    "collection-return": {
      "type": "collection-return",
      "settings": {}
    },
    "alireviews-section-1672312879": {
      "type": "apps",
      "settings": {}
    }
  },
  "order": [
    "main",
    "sub",
    "product-recommendations",
    "recently-viewed",
    "collection-return",
    "alireviews-section-1672312879"
  ]
}
